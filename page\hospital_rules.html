<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医院个性化规则推荐系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        /* 全局缩放到90% */
        body {
            transform: scale(0.9);
            transform-origin: top left;
            width: 111.11%; /* 100% / 0.9 = 111.11% */
            margin: 0;
            padding: 0;
            min-height: auto;
        }

        .container-fluid {
            width: 100%;
            max-width: none;
            min-height: auto;
        }

        /* 确保页面内容不会产生多余的空白 */
        html, body {
            overflow-x: hidden;
        }

        .hospital-card {
            transition: transform 0.2s;
        }
        .hospital-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .match-score {
            font-weight: bold;
        }
        .match-high { color: #28a745; }
        .match-medium { color: #ffc107; }
        .match-low { color: #dc3545; }
        .rule-tag {
            display: inline-block;
            padding: 2px 8px;
            margin: 2px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        .tag-recommended { background-color: #e3f2fd; color: #1976d2; }
        .tag-adopted { background-color: #e8f5e8; color: #2e7d32; }
        .tag-ignored { background-color: #fafafa; color: #757575; }

        /* 分组规则样式 */
        .grouped-rule {
            position: relative;
        }

        .grouped-rule::before {
            content: '';
            position: absolute;
            left: -3px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, #0dcaf0, #0d6efd);
            border-radius: 0 2px 2px 0;
        }

        .grouped-rule .card-body {
            background: linear-gradient(to right, rgba(13, 202, 240, 0.05), transparent);
        }

        /* 分组标题样式 */
        .alert-info {
            border-left: 4px solid #0dcaf0;
        }

        .alert-light {
            border-left: 4px solid #6c757d;
        }

        /* 规则卡片悬停效果 */
        .rule-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .rule-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        /* 规则状态更新动画 */
        .rule-card.updating {
            background-color: #e8f5e8 !important;
            transition: background-color 0.3s ease;
        }

        .rule-card.highlighted {
            box-shadow: 0 0 20px rgba(13, 202, 240, 0.5) !important;
            transition: box-shadow 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="bi bi-hospital"></i> 医院个性化规则推荐系统</h2>
                <p class="text-muted">基于医院收费数据，智能推荐适用的飞检规则</p>
            </div>
        </div>

        <!-- 功能导航 -->
        <div class="row mb-4">
            <div class="col-12">
                <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="hospital-tab" data-bs-toggle="tab" data-bs-target="#hospital-panel" type="button" role="tab">
                            <i class="bi bi-building"></i> 医院管理
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="data-tab" data-bs-toggle="tab" data-bs-target="#data-panel" type="button" role="tab">
                            <i class="bi bi-upload"></i> 收费数据导入
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="rules-tab" data-bs-toggle="tab" data-bs-target="#rules-panel" type="button" role="tab">
                            <i class="bi bi-magic"></i> 规则推荐
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis-panel" type="button" role="tab">
                            <i class="bi bi-graph-up"></i> 数据分析
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 标签页内容 -->
        <div class="tab-content" id="mainTabContent">
            <!-- 医院管理面板 -->
            <div class="tab-pane fade show active" id="hospital-panel" role="tabpanel">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h4>医院列表</h4>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-primary" onclick="addHospital()">
                            <i class="bi bi-plus"></i> 添加医院
                        </button>
                    </div>
                </div>

                <!-- 医院列表 -->
                <div class="row" id="hospitalList">
                    <!-- 医院卡片将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 收费数据导入面板 -->
            <div class="tab-pane fade" id="data-panel" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-upload"></i> 医院收费数据导入</h5>
                            </div>
                            <div class="card-body">
                                <form id="uploadForm" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="hospitalSelect" class="form-label">选择医院</label>
                                        <select class="form-select" id="hospitalSelect" required>
                                            <option value="">请选择医院</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="dataFile" class="form-label">收费数据文件</label>
                                        <input type="file" class="form-control" id="dataFile" accept=".xlsx,.xls,.csv" required>
                                        <div class="form-text">支持Excel和CSV格式，文件应包含：医保项目编码、医保项目名称、收费金额、收费次数等字段</div>
                                    </div>
                                    <button type="submit" class="btn btn-success">
                                        <i class="bi bi-upload"></i> 上传数据
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>数据格式说明</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>必需字段：</strong></p>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success"></i> 医保项目编码</li>
                                    <li><i class="bi bi-check-circle text-success"></i> 医保项目名称</li>
                                    <li><i class="bi bi-check-circle text-success"></i> 收费金额</li>
                                    <li><i class="bi bi-check-circle text-success"></i> 收费次数</li>
                                </ul>
                                <p><strong>可选字段：</strong></p>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-info-circle text-info"></i> 科室名称</li>
                                    <li><i class="bi bi-info-circle text-info"></i> 医生姓名</li>
                                    <li><i class="bi bi-info-circle text-info"></i> 患者年龄</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据预览区域 -->
                <div class="row mt-4" id="dataPreviewSection" style="display: none;">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>数据预览</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="dataPreviewTable">
                                        <thead></thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-primary" onclick="confirmUpload()">
                                        <i class="bi bi-check"></i> 确认导入
                                    </button>
                                    <button class="btn btn-secondary" onclick="cancelUpload()">
                                        <i class="bi bi-x"></i> 取消
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 规则推荐面板 -->
            <div class="tab-pane fade" id="rules-panel" role="tabpanel">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h4>规则推荐</h4>
                    </div>
                    <div class="col-md-6 text-end">
                        <select class="form-select d-inline-block w-auto me-2" id="ruleHospitalSelect">
                            <option value="">选择医院</option>
                        </select>
                        <button class="btn btn-success" onclick="generateRecommendations()">
                            <i class="bi bi-magic"></i> 生成推荐
                        </button>
                    </div>
                </div>

                <!-- 过滤条件 -->
                <div class="card mb-3" id="filterSection" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-funnel"></i> 过滤条件
                            <button class="btn btn-outline-secondary btn-sm float-end" onclick="clearFilters()">
                                <i class="bi bi-arrow-clockwise"></i> 清除过滤
                            </button>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="filterCity" class="form-label">城市</label>
                                <select class="form-select form-select-sm" id="filterCity" onchange="applyFilters()">
                                    <option value="">全部城市</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filterSource" class="form-label">规则来源</label>
                                <select class="form-select form-select-sm" id="filterSource" onchange="applyFilters()">
                                    <option value="">全部来源</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="filterType" class="form-label">类型</label>
                                <select class="form-select form-select-sm" id="filterType" onchange="applyFilters()">
                                    <option value="">全部类型</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="filterRuleCategory" class="form-label">规则类型</label>
                                <select class="form-select form-select-sm" id="filterRuleCategory" onchange="applyFilters()">
                                    <option value="">全部规则类型</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="filterRuleName" class="form-label">规则名称</label>
                                <input type="text" class="form-control form-control-sm" id="filterRuleName"
                                       placeholder="输入规则名称关键词" onkeyup="applyFilters()">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-3">
                                <label for="filterStatus" class="form-label">状态</label>
                                <select class="form-select form-select-sm" id="filterStatus" onchange="applyFilters()">
                                    <option value="">全部状态</option>
                                    <option value="推荐">推荐</option>
                                    <option value="已采用">已采用</option>
                                    <option value="已忽略">已忽略</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filterMatchScore" class="form-label">匹配度</label>
                                <select class="form-select form-select-sm" id="filterMatchScore" onchange="applyFilters()">
                                    <option value="">全部匹配度</option>
                                    <option value="high">高匹配度 (≥80%)</option>
                                    <option value="medium">中匹配度 (50-79%)</option>
                                    <option value="low">低匹配度 (<50%)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">显示统计</label>
                                <div class="small text-muted" id="filterStats">
                                    总计：0 条规则
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 推荐结果 -->
                <div id="recommendationsSection">
                    <!-- 推荐结果将通过JavaScript动态生成 -->
                </div>

                <!-- 批量操作工具栏 -->
                <div id="batchToolbar" class="card mt-3" style="display: none;">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <span id="selectedCount">已选择 0 条规则</span>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-success me-2" onclick="batchAdoptRules()">
                                    <i class="bi bi-check-circle"></i> 批量采用
                                </button>
                                <button class="btn btn-secondary me-2" onclick="batchIgnoreRules()">
                                    <i class="bi bi-x-circle"></i> 批量忽略
                                </button>
                                <button class="btn btn-outline-secondary" onclick="clearSelection()">
                                    <i class="bi bi-arrow-clockwise"></i> 清除选择
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据分析面板 -->
            <div class="tab-pane fade" id="analysis-panel" role="tabpanel">
                <div id="hospitalDataSection">
                    <!-- 医院数据将通过JavaScript动态生成 -->
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> 请从医院管理页面点击"查看数据"来查看具体医院的收费数据分析
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加医院模态框 -->
    <div class="modal fade" id="hospitalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加医院</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="hospitalForm">
                        <div class="mb-3">
                            <label for="hospitalName" class="form-label">医院名称</label>
                            <input type="text" class="form-control" id="hospitalName" required>
                        </div>
                        <div class="mb-3">
                            <label for="hospitalCode" class="form-label">医院编码</label>
                            <input type="text" class="form-control" id="hospitalCode">
                        </div>
                        <div class="mb-3">
                            <label for="hospitalCity" class="form-label">所在城市</label>
                            <select class="form-select" id="hospitalCity" required>
                                <option value="">请选择城市</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="hospitalLevel" class="form-label">医院等级</label>
                            <select class="form-select" id="hospitalLevel">
                                <option value="">请选择等级</option>
                                <option value="三甲">三甲</option>
                                <option value="三乙">三乙</option>
                                <option value="二甲">二甲</option>
                                <option value="二乙">二乙</option>
                                <option value="一甲">一甲</option>
                                <option value="一乙">一乙</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveHospital()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // 全局变量
        let hospitals = [];
        let currentUploadData = null;
        let hospitalModal;

        // 页面初始化
        $(document).ready(function() {
            hospitalModal = new bootstrap.Modal(document.getElementById('hospitalModal'));
            loadHospitals();
            loadCities();

            // 绑定文件上传事件
            $('#uploadForm').on('submit', handleFileUpload);
        });

        // 显示提示消息
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`;
            toast.style.zIndex = '1050';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // 加载医院列表
        function loadHospitals() {
            fetch('/api/hospitals')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        hospitals = data.hospitals;
                        renderHospitalList();
                        updateHospitalSelects();
                    } else {
                        showToast('加载医院列表失败: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('加载医院列表失败:', error);
                    showToast('加载医院列表失败', 'error');
                });
        }

        // 渲染医院列表
        function renderHospitalList() {
            const container = document.getElementById('hospitalList');
            container.innerHTML = '';

            hospitals.forEach(hospital => {
                const card = document.createElement('div');
                card.className = 'col-md-4 mb-3';
                card.innerHTML = `
                    <div class="card hospital-card">
                        <div class="card-body">
                            <h5 class="card-title">${hospital.医院名称}</h5>
                            <p class="card-text">
                                <small class="text-muted">
                                    <i class="bi bi-geo-alt"></i> ${hospital.所在城市 || '未设置'}
                                    <br>
                                    <i class="bi bi-award"></i> ${hospital.医院等级 || '未设置'}
                                    <br>
                                    <i class="bi bi-hash"></i> ${hospital.医院编码 || '未设置'}
                                </small>
                            </p>
                            <div class="mb-2" id="adoptedRules_${hospital.医院ID}">
                                <small class="text-muted">正在加载已采用规则...</small>
                            </div>
                            <div class="btn-group w-100">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewHospitalData(${hospital.医院ID})">
                                    <i class="bi bi-eye"></i> 查看数据
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="generateHospitalRules(${hospital.医院ID})">
                                    <i class="bi bi-magic"></i> 生成规则
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="viewAdoptedRules(${hospital.医院ID})">
                                    <i class="bi bi-check-circle"></i> 已采用
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteHospital(${hospital.医院ID})">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                container.appendChild(card);

                // 加载该医院的已采用规则
                loadHospitalAdoptedRules(hospital.医院ID);
            });
        }

        // 更新医院选择下拉框
        function updateHospitalSelects() {
            const selects = ['hospitalSelect', 'ruleHospitalSelect'];
            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                select.innerHTML = '<option value="">请选择医院</option>';
                hospitals.forEach(hospital => {
                    const option = document.createElement('option');
                    option.value = hospital.医院ID;
                    option.textContent = hospital.医院名称;
                    select.appendChild(option);
                });
            });
        }

        // 加载城市列表
        function loadCities() {
            fetch('/api/city_types')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const citySelect = document.getElementById('hospitalCity');
                        citySelect.innerHTML = '<option value="">请选择城市</option>';
                        data.types.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city;
                            option.textContent = city;
                            citySelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载城市列表失败:', error);
                });
        }

        // 添加医院
        function addHospital() {
            document.getElementById('hospitalForm').reset();
            hospitalModal.show();
        }

        // 保存医院
        function saveHospital() {
            const form = document.getElementById('hospitalForm');
            const formData = new FormData(form);

            const hospitalData = {
                医院名称: document.getElementById('hospitalName').value,
                医院编码: document.getElementById('hospitalCode').value,
                所在城市: document.getElementById('hospitalCity').value,
                医院等级: document.getElementById('hospitalLevel').value
            };

            fetch('/api/hospitals', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(hospitalData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('医院添加成功');
                    hospitalModal.hide();
                    loadHospitals();
                } else {
                    showToast('添加失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('添加医院失败:', error);
                showToast('添加医院失败', 'error');
            });
        }

        // 删除医院
        function deleteHospital(hospitalId) {
            if (confirm('确定要删除这家医院吗？这将同时删除相关的收费数据和规则推荐。')) {
                fetch(`/api/hospitals/${hospitalId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('医院删除成功');
                        loadHospitals();
                    } else {
                        showToast('删除失败: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('删除医院失败:', error);
                    showToast('删除医院失败', 'error');
                });
            }
        }

        // 处理文件上传
        function handleFileUpload(event) {
            event.preventDefault();

            const hospitalId = document.getElementById('hospitalSelect').value;
            const file = document.getElementById('dataFile').files[0];

            if (!hospitalId || !file) {
                showToast('请选择医院和上传文件', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('hospital_id', hospitalId);
            formData.append('file', file);

            showToast('正在处理文件，请稍候...', 'info');

            fetch('/api/hospital-data/preview', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentUploadData = data.full_data || data.data; // 使用完整数据
                    showDataPreview(data.data); // 只预览前100条

                    // 显示数据统计信息
                    if (data.total_count > data.preview_count) {
                        showToast(`文件包含 ${data.total_count} 条记录，预览显示前 ${data.preview_count} 条`, 'info');
                    }
                } else {
                    showToast('文件处理失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('文件上传失败:', error);
                showToast('文件上传失败', 'error');
            });
        }

        // 显示数据预览
        function showDataPreview(data) {
            if (!data || data.length === 0) {
                showToast('文件中没有有效数据', 'warning');
                return;
            }

            const section = document.getElementById('dataPreviewSection');
            const table = document.getElementById('dataPreviewTable');
            const thead = table.querySelector('thead');
            const tbody = table.querySelector('tbody');

            // 清空表格
            thead.innerHTML = '';
            tbody.innerHTML = '';

            // 创建表头
            const headers = Object.keys(data[0]);
            const headerRow = document.createElement('tr');
            headers.forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);

            // 创建数据行（只显示前10行）
            const previewData = data.slice(0, 10);
            previewData.forEach(row => {
                const tr = document.createElement('tr');
                headers.forEach(header => {
                    const td = document.createElement('td');
                    td.textContent = row[header] || '';
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });

            // 显示预览区域
            section.style.display = 'block';
            section.scrollIntoView({ behavior: 'smooth' });
        }

        // 确认上传
        function confirmUpload() {
            if (!currentUploadData) {
                showToast('没有待上传的数据', 'warning');
                return;
            }

            const hospitalId = document.getElementById('hospitalSelect').value;

            fetch('/api/hospital-data/import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    hospital_id: hospitalId,
                    data: currentUploadData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`数据导入成功！共导入 ${data.imported_count} 条记录`);
                    cancelUpload();
                    document.getElementById('uploadForm').reset();
                } else {
                    showToast('数据导入失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('数据导入失败:', error);
                showToast('数据导入失败', 'error');
            });
        }

        // 取消上传
        function cancelUpload() {
            currentUploadData = null;
            document.getElementById('dataPreviewSection').style.display = 'none';
        }

        // 查看医院数据
        function viewHospitalData(hospitalId) {
            // 切换到数据分析标签页并显示该医院的数据
            const analysisTab = new bootstrap.Tab(document.getElementById('analysis-tab'));
            analysisTab.show();
            loadHospitalAnalysis(hospitalId);
        }

        // 生成医院规则推荐
        function generateHospitalRules(hospitalId) {
            // 切换到规则推荐标签页
            const rulesTab = new bootstrap.Tab(document.getElementById('rules-tab'));
            rulesTab.show();

            // 设置医院选择
            document.getElementById('ruleHospitalSelect').value = hospitalId;

            // 生成推荐
            generateRecommendations();
        }

        // 生成规则推荐
        function generateRecommendations() {
            const hospitalId = document.getElementById('ruleHospitalSelect').value;
            if (!hospitalId) {
                showToast('请先选择医院', 'warning');
                return Promise.reject('未选择医院');
            }

            const section = document.getElementById('recommendationsSection');
            section.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在分析医院数据，生成规则推荐...</p></div>';

            return fetch('/api/hospital-rules/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ hospital_id: hospitalId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示推荐数量信息
                    if (data.total_count && data.returned_count) {
                        console.log(`生成推荐完成：总共找到 ${data.total_count} 条规则，显示 ${data.returned_count} 条`);
                    }
                    renderRecommendations(data.recommendations, data.total_count, data.returned_count);
                    return data.recommendations;
                } else {
                    section.innerHTML = `<div class="alert alert-danger">生成推荐失败: ${data.error}</div>`;
                    throw new Error(data.error);
                }
            })
            .catch(error => {
                console.error('生成推荐失败:', error);
                section.innerHTML = '<div class="alert alert-danger">生成推荐失败，请稍后重试</div>';
                throw error;
            });
        }

        // 全局变量存储选中的规则和原始推荐数据
        let selectedRules = new Set();
        let originalRecommendations = [];
        let filteredRecommendations = [];

        // 渲染推荐结果
        function renderRecommendations(recommendations, totalCount, returnedCount) {
            const section = document.getElementById('recommendationsSection');

            if (!recommendations || recommendations.length === 0) {
                section.innerHTML = '<div class="alert alert-info">暂无适用的规则推荐，请确保已上传医院收费数据</div>';
                document.getElementById('batchToolbar').style.display = 'none';
                document.getElementById('filterSection').style.display = 'none';
                return;
            }

            // 对规则进行分组和排序
            const groupedAndSortedRecommendations = groupAndSortRecommendations(recommendations);

            // 保存原始数据和数量信息
            originalRecommendations = groupedAndSortedRecommendations;
            filteredRecommendations = groupedAndSortedRecommendations;

            // 显示数量信息
            if (totalCount && returnedCount) {
                const countInfo = document.createElement('div');
                countInfo.className = 'alert alert-info mb-3';
                countInfo.innerHTML = `
                    <i class="bi bi-info-circle"></i>
                    规则推荐生成完成：共找到 <strong>${totalCount}</strong> 条匹配规则，当前显示 <strong>${returnedCount}</strong> 条
                    ${totalCount > returnedCount ? `<br><small class="text-muted">如需查看更多规则，请使用过滤条件进行筛选</small>` : ''}
                `;
                section.appendChild(countInfo);
            }

            // 显示过滤器并初始化过滤选项
            document.getElementById('filterSection').style.display = 'block';
            initializeFilters(recommendations);

            renderFilteredRecommendations(filteredRecommendations);

            // 确保批量操作工具栏为推荐状态
            resetBatchToolbarForRecommendations();
        }

        // 分页相关变量
        let currentPage = 1;
        let pageSize = 10; // 每页显示10条规则
        let allFilteredRecommendations = [];

        // 渲染过滤后的推荐结果
        function renderFilteredRecommendations(recommendations) {
            allFilteredRecommendations = recommendations;
            currentPage = 1; // 重置到第一页
            renderCurrentPage();
        }

        // 渲染当前页
        function renderCurrentPage() {
            const section = document.getElementById('recommendationsSection');

            if (!allFilteredRecommendations || allFilteredRecommendations.length === 0) {
                section.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        暂无推荐规则，请先生成推荐或调整筛选条件。
                    </div>
                `;
                return;
            }

            // 计算分页
            const totalPages = Math.ceil(allFilteredRecommendations.length / pageSize);
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, allFilteredRecommendations.length);
            const currentRecommendations = allFilteredRecommendations.slice(startIndex, endIndex);

            let html = `
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">推荐规则列表 (第 ${currentPage}/${totalPages} 页，共 ${allFilteredRecommendations.length} 条)</h5>
                        <div>
                            <button class="btn btn-outline-primary btn-sm me-2" onclick="selectAllRules()">
                                <i class="bi bi-check-square"></i> 全选
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                <i class="bi bi-square"></i> 清除
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 分页控件 -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <select class="form-select form-select-sm" style="width: auto; display: inline-block;" onchange="changePageSize(this.value)">
                            <option value="10" ${pageSize === 10 ? 'selected' : ''}>每页 10 条</option>
                            <option value="20" ${pageSize === 20 ? 'selected' : ''}>每页 20 条</option>
                            <option value="50" ${pageSize === 50 ? 'selected' : ''}>每页 50 条</option>
                            <option value="100" ${pageSize === 100 ? 'selected' : ''}>每页 100 条</option>
                        </select>
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                                <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;">上一页</a>
                            </li>
                            ${generatePageNumbers(currentPage, totalPages)}
                            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                                <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">下一页</a>
                            </li>
                        </ul>
                    </nav>
                </div>

                <div class="row">
            `;

            currentRecommendations.forEach((rec, index) => {
                const matchClass = rec.匹配度 >= 0.8 ? 'match-high' : rec.匹配度 >= 0.5 ? 'match-medium' : 'match-low';
                const statusClass = rec.状态 === '已采用' ? 'tag-adopted' : rec.状态 === '已忽略' ? 'tag-ignored' : 'tag-recommended';

                // 检查是否是分组的第一个规则，如果是则添加分组标题
                let groupHeader = '';
                if (rec.是否分组首个) {
                    const groupId = `group_${rec.分组标识}`;

                    if (rec.分组总数 > 1) {
                        groupHeader = `
                            <div class="col-md-12 mb-2">
                                <div class="alert alert-info py-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-info-circle"></i>
                                            <strong>匹配项目：</strong>"${rec.匹配项目}" 有 ${rec.分组总数} 条相关规则，已按优先级排序（已采用 > 推荐 > 已忽略，匹配度高 > 低）
                                        </div>
                                        <button class="btn btn-sm btn-outline-info" onclick="toggleGroupDisplay('${groupId}')" id="toggle_${groupId}">
                                            <i class="bi bi-chevron-down"></i> 展开 (${rec.分组总数 > 2 ? rec.分组总数 - 2 : 0} 条)
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    } else {
                        groupHeader = `
                            <div class="col-md-12 mb-2">
                                <div class="alert alert-light py-1">
                                    <i class="bi bi-tag"></i>
                                    <strong>匹配项目：</strong>"${rec.匹配项目}"
                                </div>
                            </div>
                        `;
                    }
                }

                // 为分组规则添加特殊样式
                const isGrouped = rec.分组总数 > 1;
                const groupClass = isGrouped ? 'grouped-rule' : '';
                const borderClass = isGrouped ? 'border-info' : '';

                html += groupHeader + `
                    <div class="col-md-12 mb-2 group-item" data-group="${rec.分组标识}" data-item-index="${rec.分组序号}" style="${rec.分组总数 > 1 && rec.分组序号 > 2 ? 'display: none;' : ''}">
                        <div class="card rule-card ${groupClass} ${borderClass}" data-rule-id="${rec.适用ID}">
                            <div class="card-body py-2">
                                <div class="row align-items-center">
                                    <div class="col-md-1">
                                        ${rec.状态 === '推荐' || rec.状态 === '已采用' ? `
                                            <input type="checkbox" class="form-check-input rule-checkbox"
                                                   value="${rec.适用ID}" onchange="updateSelection()">
                                        ` : ''}
                                        <span class="rule-tag ${statusClass} ms-2">${rec.状态}</span>
                                        ${isGrouped ? `<br><small class="text-muted">规则 ${rec.分组序号}/${rec.分组总数}</small>` : ''}
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="mb-1 fw-bold">
                                            ${rec.规则名称}
                                            ${isGrouped ? `<span class="badge bg-info text-white ms-1">${rec.分组序号}/${rec.分组总数}</span>` : ''}
                                        </h6>
                                        <small class="text-muted">
                                            <i class="bi bi-geo-alt"></i> ${rec.城市 || '未知'} |
                                            <i class="bi bi-bookmark"></i> ${rec.规则来源 || '未知'}
                                            ${rec.合并规则数量 > 1 ? ` | <i class="bi bi-layers"></i> 合并了 ${rec.合并规则数量} 条规则` : ''}
                                        </small>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="mb-1 small"><strong class="text-success">行为认定：</strong>${rec.行为认定}</p>
                                        <p class="mb-0 small text-muted" style="line-height: 1.3;">
                                            <strong class="text-primary">规则内涵：</strong>${rec.规则内涵 || '未设置'}
                                        </p>
                                    </div>
                                    <div class="col-md-1 text-center">
                                        <span class="match-score ${matchClass} h6">${(rec.匹配度 * 100).toFixed(1)}%</span>
                                        <br>
                                        <small class="text-muted">匹配度</small>
                                    </div>
                                    <div class="col-md-2 text-end">
                                        <div class="btn-group btn-group-sm">
                                            ${rec.状态 === '推荐' ? `
                                                <button class="btn btn-outline-success btn-sm" onclick="adoptRule(${rec.适用ID})" title="采用">
                                                    <i class="bi bi-check"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary btn-sm" onclick="ignoreRule(${rec.适用ID})" title="忽略">
                                                    <i class="bi bi-x"></i>
                                                </button>
                                            ` : rec.状态 === '已采用' ? `
                                                <button class="btn btn-outline-warning btn-sm" onclick="ignoreRule(${rec.适用ID})" title="取消采用">
                                                    <i class="bi bi-x-circle"></i>
                                                </button>
                                            ` : ''}
                                            <button class="btn btn-outline-info btn-sm" onclick="viewRuleDetail(${rec.规则ID}, '${rec.规则名称}')" title="查看详情">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';

            // 添加底部分页控件
            if (totalPages > 1) {
                html += `
                    <div class="d-flex justify-content-center mt-4">
                        <nav>
                            <ul class="pagination">
                                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;">上一页</a>
                                </li>
                                ${generatePageNumbers(currentPage, totalPages)}
                                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">下一页</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                `;
            }

            section.innerHTML = html;

            // 显示批量操作工具栏并重置为推荐状态
            const toolbar = document.getElementById('batchToolbar');
            toolbar.style.display = 'block';
            resetBatchToolbarForRecommendations();
            updateSelection();
        }

        // 生成页码
        function generatePageNumbers(current, total) {
            let html = '';
            const maxVisible = 5; // 最多显示5个页码

            let start = Math.max(1, current - Math.floor(maxVisible / 2));
            let end = Math.min(total, start + maxVisible - 1);

            // 调整起始位置
            if (end - start + 1 < maxVisible) {
                start = Math.max(1, end - maxVisible + 1);
            }

            for (let i = start; i <= end; i++) {
                html += `
                    <li class="page-item ${i === current ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
                    </li>
                `;
            }

            return html;
        }

        // 切换页面
        function changePage(page) {
            const totalPages = Math.ceil(allFilteredRecommendations.length / pageSize);
            if (page < 1 || page > totalPages) return;

            currentPage = page;
            renderCurrentPage();

            // 滚动到顶部
            document.getElementById('recommendationsSection').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // 改变每页显示数量
        function changePageSize(newSize) {
            pageSize = parseInt(newSize);
            currentPage = 1; // 重置到第一页
            renderCurrentPage();
        }

        // 切换分组显示状态
        function toggleGroupDisplay(groupId) {
            const groupItems = document.querySelectorAll(`[data-group="${groupId}"]`);
            const toggleBtn = document.getElementById(`toggle_${groupId}`);

            if (!groupItems.length || !toggleBtn) return;

            // 检查当前状态（通过第3个及以后的项目是否隐藏来判断）
            const hiddenItems = Array.from(groupItems).filter(item =>
                parseInt(item.dataset.itemIndex) > 2 && item.style.display === 'none'
            );

            const isCollapsed = hiddenItems.length > 0;

            if (isCollapsed) {
                // 当前是收缩状态，展开所有项目
                groupItems.forEach(item => {
                    if (parseInt(item.dataset.itemIndex) > 2) {
                        item.style.display = '';
                    }
                });
                toggleBtn.innerHTML = '<i class="bi bi-chevron-up"></i> 收缩';
            } else {
                // 当前是展开状态，收缩到只显示前2个
                groupItems.forEach(item => {
                    if (parseInt(item.dataset.itemIndex) > 2) {
                        item.style.display = 'none';
                    }
                });
                toggleBtn.innerHTML = '<i class="bi bi-chevron-down"></i> 展开';
            }
        }

        // 采用规则
        function adoptRule(适用ID) {
            // 保存当前滚动位置
            saveScrollPosition();

            // 查找对应的推荐记录
            const rec = filteredRecommendations.find(r => r.适用ID == 适用ID);
            if (rec && rec.合并规则数量 > 1) {
                // 合并规则，提示用户
                if (confirm(`这是一个合并规则（包含 ${rec.合并规则数量} 条相同名称的规则），确定要采用吗？`)) {
                    updateRuleStatus(适用ID, '已采用');
                    // 操作完成后滚动到该规则位置
                    setTimeout(() => scrollToRule(适用ID), 100);
                }
            } else {
                // 单个规则
                updateRuleStatus(适用ID, '已采用');
                // 操作完成后滚动到该规则位置
                setTimeout(() => scrollToRule(适用ID), 100);
            }
        }

        // 忽略规则
        function ignoreRule(适用ID) {
            // 保存当前滚动位置
            saveScrollPosition();

            // 查找对应的推荐记录
            const rec = filteredRecommendations.find(r => r.适用ID == 适用ID);
            if (rec && rec.合并规则数量 > 1) {
                // 合并规则，提示用户
                if (confirm(`这是一个合并规则（包含 ${rec.合并规则数量} 条相同名称的规则），确定要忽略吗？`)) {
                    updateRuleStatus(适用ID, '已忽略');
                    // 操作完成后滚动到该规则位置
                    setTimeout(() => scrollToRule(适用ID), 100);
                }
            } else {
                // 单个规则
                updateRuleStatus(适用ID, '已忽略');
                // 操作完成后滚动到该规则位置
                setTimeout(() => scrollToRule(适用ID), 100);
            }
        }

        // 更新规则状态
        function updateRuleStatus(适用ID, status) {
            fetch('/api/hospital-rules/status', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    适用ID: 适用ID,
                    状态: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`规则状态已更新为：${status}`);

                    // 局部更新规则状态，不刷新整个页面
                    updateRuleCardStatus(适用ID, status);
                } else {
                    showToast('更新失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('更新规则状态失败:', error);
                showToast('更新规则状态失败', 'error');
            });
        }

        // 局部更新规则卡片状态
        function updateRuleCardStatus(适用ID, newStatus) {
            // 找到对应的规则卡片
            const ruleCard = document.querySelector(`[data-rule-id="${适用ID}"]`);
            if (!ruleCard) return;

            // 更新内存中的数据
            const ruleIndex = filteredRecommendations.findIndex(r => r.适用ID == 适用ID);
            if (ruleIndex !== -1) {
                filteredRecommendations[ruleIndex].状态 = newStatus;

                // 同时更新原始数据
                const originalIndex = originalRecommendations.findIndex(r => r.适用ID == 适用ID);
                if (originalIndex !== -1) {
                    originalRecommendations[originalIndex].状态 = newStatus;
                }
            }

            // 更新状态标签
            const statusTag = ruleCard.querySelector('.rule-tag');
            if (statusTag) {
                // 移除旧的状态类
                statusTag.classList.remove('tag-recommended', 'tag-adopted', 'tag-ignored');

                // 添加新的状态类和文本
                const statusClass = newStatus === '已采用' ? 'tag-adopted' :
                                  newStatus === '已忽略' ? 'tag-ignored' : 'tag-recommended';
                statusTag.classList.add(statusClass);
                statusTag.textContent = newStatus;
            }

            // 更新操作按钮
            const buttonGroup = ruleCard.querySelector('.btn-group');
            if (buttonGroup) {
                let newButtons = '';
                if (newStatus === '推荐') {
                    newButtons = `
                        <button class="btn btn-outline-success btn-sm" onclick="adoptRule(${适用ID})" title="采用">
                            <i class="bi bi-check"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="ignoreRule(${适用ID})" title="忽略">
                            <i class="bi bi-x"></i>
                        </button>
                    `;
                } else if (newStatus === '已采用') {
                    newButtons = `
                        <button class="btn btn-outline-warning btn-sm" onclick="ignoreRule(${适用ID})" title="取消采用">
                            <i class="bi bi-x-circle"></i>
                        </button>
                    `;
                } else if (newStatus === '已忽略') {
                    newButtons = `
                        <button class="btn btn-outline-success btn-sm" onclick="adoptRule(${适用ID})" title="重新采用">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    `;
                }

                // 保留查看详情按钮
                const rec = filteredRecommendations.find(r => r.适用ID == 适用ID);
                if (rec) {
                    newButtons += `
                        <button class="btn btn-outline-info btn-sm" onclick="viewRuleDetail(${rec.规则ID}, '${rec.规则名称}')" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                    `;
                }

                buttonGroup.innerHTML = newButtons;
            }

            // 更新复选框显示
            const checkbox = ruleCard.querySelector('.rule-checkbox');
            if (checkbox) {
                if (newStatus === '推荐' || newStatus === '已采用') {
                    checkbox.style.display = 'inline-block';
                } else {
                    checkbox.style.display = 'none';
                    checkbox.checked = false;
                }
            }

            // 更新统计信息
            updateFilterStats(filteredRecommendations);

            // 更新选择状态
            updateSelection();

            // 添加视觉反馈
            ruleCard.style.transition = 'all 0.3s ease';
            ruleCard.style.backgroundColor = '#e8f5e8';
            setTimeout(() => {
                ruleCard.style.backgroundColor = '';
            }, 1000);
        }

        // 更新选择状态
        function updateSelection() {
            const checkboxes = document.querySelectorAll('.rule-checkbox:checked');
            selectedRules.clear();

            // 收集所有选中的适用ID（包括合并规则的所有ID）
            checkboxes.forEach(cb => {
                const ruleCard = cb.closest('.rule-card');
                const ruleId = ruleCard.dataset.ruleId;

                // 查找对应的推荐记录
                const rec = filteredRecommendations.find(r => r.适用ID == ruleId);
                if (rec && rec.合并规则ID列表 && rec.合并规则ID列表.length > 1) {
                    // 如果是合并规则，需要找到所有相关的适用ID
                    // 这里简化处理，直接使用当前适用ID
                    selectedRules.add(ruleId);
                } else {
                    // 普通规则，只添加单个ID
                    selectedRules.add(ruleId);
                }
            });

            const count = selectedRules.size;
            const ruleCount = checkboxes.length;
            document.getElementById('selectedCount').textContent = `已选择 ${ruleCount} 条规则`;

            // 显示/隐藏批量操作工具栏
            const toolbar = document.getElementById('batchToolbar');
            if (count > 0) {
                toolbar.style.display = 'block';
            }
        }

        // 全选规则（当前页）
        function selectAllRules() {
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            checkboxes.forEach(cb => cb.checked = true);
            updateSelection();
        }

        // 清除选择（所有页面）
        function clearSelection() {
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            checkboxes.forEach(cb => cb.checked = false);
            selectedRules.clear();
            updateSelection();
        }

        // 批量采用规则
        function batchAdoptRules() {
            if (selectedRules.size === 0) {
                showToast('请先选择要采用的规则', 'warning');
                return;
            }

            if (confirm(`确定要采用选中的 ${selectedRules.size} 条规则吗？`)) {
                batchUpdateRuleStatus('已采用');
            }
        }

        // 批量忽略规则
        function batchIgnoreRules() {
            if (selectedRules.size === 0) {
                showToast('请先选择要忽略的规则', 'warning');
                return;
            }

            if (confirm(`确定要忽略选中的 ${selectedRules.size} 条规则吗？`)) {
                batchUpdateRuleStatus('已忽略');
            }
        }

        // 批量更新规则状态
        function batchUpdateRuleStatus(status) {
            const ruleIds = Array.from(selectedRules);

            fetch('/api/hospital-rules/batch-status', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    rule_ids: ruleIds,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`成功${status} ${ruleIds.length} 条规则`);

                    // 批量局部更新规则状态
                    ruleIds.forEach(ruleId => {
                        updateRuleCardStatus(ruleId, status);
                    });

                    clearSelection();
                } else {
                    showToast('批量操作失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('批量更新规则状态失败:', error);
                showToast('批量操作失败', 'error');
            });
        }

        // 批量取消采用
        function batchCancelAdoption() {
            if (confirm('确定要批量取消采用选中的规则吗？')) {
                batchUpdateRuleStatus('已忽略');
            }
        }

        // 重置批量操作工具栏为推荐页面状态
        function resetBatchToolbarForRecommendations() {
            const toolbar = document.getElementById('batchToolbar');
            const adoptBtn = toolbar.querySelector('button[onclick="batchAdoptRules()"]');
            const ignoreBtn = toolbar.querySelector('button[onclick="batchIgnoreRules()"]');

            if (adoptBtn) {
                adoptBtn.style.display = 'inline-block'; // 显示批量采用按钮
                adoptBtn.innerHTML = '<i class="bi bi-check-circle"></i> 批量采用';
                adoptBtn.onclick = function() { batchAdoptRules(); };
            }
            if (ignoreBtn) {
                ignoreBtn.innerHTML = '<i class="bi bi-x-circle"></i> 批量忽略';
                ignoreBtn.onclick = function() { batchIgnoreRules(); };
            }
        }

        // 设置批量操作工具栏为已采用页面状态
        function setBatchToolbarForAdopted() {
            const toolbar = document.getElementById('batchToolbar');
            const adoptBtn = toolbar.querySelector('button[onclick="batchAdoptRules()"]');
            const ignoreBtn = toolbar.querySelector('button[onclick="batchIgnoreRules()"]');

            if (adoptBtn) {
                adoptBtn.style.display = 'none'; // 隐藏批量采用按钮
            }
            if (ignoreBtn) {
                ignoreBtn.innerHTML = '<i class="bi bi-x-circle"></i> 批量取消采用';
                ignoreBtn.onclick = function() { batchCancelAdoption(); };
            }
        }

        // 规则分组和排序 - 按匹配项目名称分组
        function groupAndSortRecommendations(recommendations) {
            // 第一步：按匹配项目名称分组
            const itemGroups = {};

            recommendations.forEach(rec => {
                const matchedItem = rec.匹配项目 || '未知项目';
                if (!itemGroups[matchedItem]) {
                    itemGroups[matchedItem] = [];
                }
                itemGroups[matchedItem].push(rec);
            });

            // 第二步：对每个分组内的规则进行排序和处理
            const sortedRecommendations = [];

            // 按匹配项目名称排序分组
            const sortedItemNames = Object.keys(itemGroups).sort();

            sortedItemNames.forEach(itemName => {
                const group = itemGroups[itemName];

                // 对分组内的规则进行排序
                group.sort((a, b) => {
                    // 状态优先级：已采用 > 推荐 > 已忽略
                    const statusPriority = { '已采用': 3, '推荐': 2, '已忽略': 1 };
                    const statusDiff = (statusPriority[b.状态] || 0) - (statusPriority[a.状态] || 0);
                    if (statusDiff !== 0) return statusDiff;

                    // 匹配度降序
                    const matchDiff = (b.匹配度 || 0) - (a.匹配度 || 0);
                    if (matchDiff !== 0) return matchDiff;

                    // 规则名称升序
                    const nameA = a.规则名称 || '';
                    const nameB = b.规则名称 || '';
                    const nameDiff = nameA.localeCompare(nameB);
                    if (nameDiff !== 0) return nameDiff;

                    // 城市名称升序
                    const cityA = a.城市 || '未知';
                    const cityB = b.城市 || '未知';
                    const cityDiff = cityA.localeCompare(cityB);
                    if (cityDiff !== 0) return cityDiff;

                    // 规则来源升序
                    const sourceA = a.规则来源 || '未知';
                    const sourceB = b.规则来源 || '未知';
                    return sourceA.localeCompare(sourceB);
                });

                // 为每个分组添加分组信息
                if (group.length > 1) {
                    group.forEach((rec, index) => {
                        rec.分组序号 = index + 1;
                        rec.分组总数 = group.length;
                        rec.是否分组首个 = index === 0;
                        rec.分组标识 = `${itemName}_group`;
                        rec.分组类型 = '匹配项目';
                    });
                } else {
                    // 单个规则也标记分组信息，便于统一处理
                    group[0].分组序号 = 1;
                    group[0].分组总数 = 1;
                    group[0].是否分组首个 = true;
                    group[0].分组标识 = `${itemName}_single`;
                    group[0].分组类型 = '单项目';
                }

                // 添加到结果中
                sortedRecommendations.push(...group);
            });

            return sortedRecommendations;
        }

        // 保存页面滚动位置
        function saveScrollPosition() {
            sessionStorage.setItem('hospitalRulesScrollPosition', window.pageYOffset);
        }

        // 恢复页面滚动位置
        function restoreScrollPosition() {
            const scrollPosition = sessionStorage.getItem('hospitalRulesScrollPosition');
            if (scrollPosition) {
                window.scrollTo(0, parseInt(scrollPosition));
                sessionStorage.removeItem('hospitalRulesScrollPosition');
            }
        }

        // 滚动到指定规则
        function scrollToRule(适用ID) {
            const ruleCard = document.querySelector(`[data-rule-id="${适用ID}"]`);
            if (ruleCard) {
                ruleCard.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // 添加高亮效果
                ruleCard.style.transition = 'all 0.5s ease';
                ruleCard.style.boxShadow = '0 0 20px rgba(13, 202, 240, 0.5)';
                setTimeout(() => {
                    ruleCard.style.boxShadow = '';
                }, 2000);
            }
        }

        // 查看规则详情
        function viewRuleDetail(规则ID, 规则名称) {
            // 打开新窗口显示规则详情，并过滤到该规则
            const url = `/rule_knowledge_base?rule_id=${规则ID}&filter_name=${encodeURIComponent(规则名称)}`;
            window.open(url, '_blank');
        }

        // 初始化过滤器选项
        function initializeFilters(recommendations) {
            // 提取唯一值 - 城市需要拆分合并的城市名称
            const allCities = [];
            const allSources = [];

            recommendations.forEach(r => {
                // 拆分城市（可能是"西安、沈阳"这样的合并格式）
                if (r.城市 && r.城市 !== '未知') {
                    const cityList = r.城市.split('、').map(c => c.trim()).filter(c => c);
                    allCities.push(...cityList);
                }

                // 拆分规则来源
                if (r.规则来源 && r.规则来源 !== '未知') {
                    const sourceList = r.规则来源.split('、').map(s => s.trim()).filter(s => s);
                    allSources.push(...sourceList);
                }
            });

            const cities = [...new Set(allCities)].sort();
            const sources = [...new Set(allSources)].sort();
            const types = [...new Set(recommendations.map(r => r.类型).filter(t => t && t !== '未知'))].sort();
            const ruleCategories = [...new Set(recommendations.map(r => r.规则类型).filter(t => t && t !== '未知'))].sort();

            // 填充城市选项
            const citySelect = document.getElementById('filterCity');
            citySelect.innerHTML = '<option value="">全部城市</option>';
            cities.forEach(city => {
                const option = document.createElement('option');
                option.value = city;
                option.textContent = city;
                citySelect.appendChild(option);
            });

            // 填充规则来源选项
            const sourceSelect = document.getElementById('filterSource');
            sourceSelect.innerHTML = '<option value="">全部来源</option>';
            sources.forEach(source => {
                const option = document.createElement('option');
                option.value = source;
                option.textContent = source;
                sourceSelect.appendChild(option);
            });

            // 填充类型选项
            const typeSelect = document.getElementById('filterType');
            typeSelect.innerHTML = '<option value="">全部类型</option>';
            types.forEach(type => {
                const option = document.createElement('option');
                option.value = type;
                option.textContent = type;
                typeSelect.appendChild(option);
            });

            // 填充规则类型选项
            const ruleCategorySelect = document.getElementById('filterRuleCategory');
            ruleCategorySelect.innerHTML = '<option value="">全部规则类型</option>';
            ruleCategories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                ruleCategorySelect.appendChild(option);
            });

            // 更新统计信息
            updateFilterStats(recommendations);
        }

        // 应用过滤器
        function applyFilters() {
            const cityFilter = document.getElementById('filterCity').value;
            const sourceFilter = document.getElementById('filterSource').value;
            const typeFilter = document.getElementById('filterType').value;
            const ruleCategoryFilter = document.getElementById('filterRuleCategory').value;
            const nameFilter = document.getElementById('filterRuleName').value.toLowerCase();
            const statusFilter = document.getElementById('filterStatus').value;
            const matchScoreFilter = document.getElementById('filterMatchScore').value;

            filteredRecommendations = originalRecommendations.filter(rec => {
                // 城市过滤 - 使用包含匹配
                if (cityFilter && (!rec.城市 || !rec.城市.includes(cityFilter))) return false;

                // 来源过滤 - 使用包含匹配
                if (sourceFilter && (!rec.规则来源 || !rec.规则来源.includes(sourceFilter))) return false;

                // 类型过滤
                if (typeFilter && rec.类型 !== typeFilter) return false;

                // 规则类型过滤
                if (ruleCategoryFilter && rec.规则类型 !== ruleCategoryFilter) return false;

                // 名称过滤
                if (nameFilter && !rec.规则名称.toLowerCase().includes(nameFilter)) return false;

                // 状态过滤
                if (statusFilter && rec.状态 !== statusFilter) return false;

                // 匹配度过滤
                if (matchScoreFilter) {
                    const score = rec.匹配度;
                    if (matchScoreFilter === 'high' && score < 0.8) return false;
                    if (matchScoreFilter === 'medium' && (score < 0.5 || score >= 0.8)) return false;
                    if (matchScoreFilter === 'low' && score >= 0.5) return false;
                }

                return true;
            });

            // 重新渲染
            renderFilteredRecommendations(filteredRecommendations);
            updateFilterStats(filteredRecommendations);

            // 显示批量操作工具栏并重置按钮
            const toolbar = document.getElementById('batchToolbar');
            toolbar.style.display = 'block';

            // 重置批量操作按钮为推荐页面状态
            resetBatchToolbarForRecommendations();
            updateSelection();
        }

        // 清除过滤器
        function clearFilters() {
            document.getElementById('filterCity').value = '';
            document.getElementById('filterSource').value = '';
            document.getElementById('filterType').value = '';
            document.getElementById('filterRuleCategory').value = '';
            document.getElementById('filterRuleName').value = '';
            document.getElementById('filterStatus').value = '';
            document.getElementById('filterMatchScore').value = '';

            filteredRecommendations = originalRecommendations;
            renderFilteredRecommendations(filteredRecommendations);
            updateFilterStats(filteredRecommendations);
        }

        // 更新过滤统计信息
        function updateFilterStats(recommendations) {
            const stats = document.getElementById('filterStats');
            const total = recommendations.length;
            const recommended = recommendations.filter(r => r.状态 === '推荐').length;
            const adopted = recommendations.filter(r => r.状态 === '已采用').length;
            const ignored = recommendations.filter(r => r.状态 === '已忽略').length;

            stats.innerHTML = `
                总计：${total} 条规则 |
                推荐：${recommended} 条 |
                已采用：${adopted} 条 |
                已忽略：${ignored} 条
            `;
        }

        // 加载医院已采用规则
        function loadHospitalAdoptedRules(hospitalId) {
            fetch(`/api/hospital-rules/adopted/${hospitalId}`)
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById(`adoptedRules_${hospitalId}`);
                    if (data.success && data.rules && data.rules.length > 0) {
                        // 去重规则名称（因为可能有相同规则名称的不同记录）
                        const uniqueRuleNames = [...new Set(data.rules.map(r => r.规则名称))];
                        const displayNames = uniqueRuleNames.slice(0, 2); // 显示前2个规则名称
                        const moreCount = uniqueRuleNames.length - 2;

                        let html = `<small class="text-success">
                            <i class="bi bi-check-circle"></i> 已采用 ${uniqueRuleNames.length} 条规则`;

                        if (displayNames.length > 0) {
                            html += `：${displayNames.join('、')}`;
                            if (moreCount > 0) {
                                html += ` 等${moreCount}条`;
                            }
                        }

                        html += '</small>';
                        container.innerHTML = html;
                    } else {
                        container.innerHTML = '<small class="text-muted"><i class="bi bi-info-circle"></i> 暂无已采用规则</small>';
                    }
                })
                .catch(error => {
                    console.error('加载已采用规则失败:', error);
                    const container = document.getElementById(`adoptedRules_${hospitalId}`);
                    container.innerHTML = '<small class="text-muted">加载失败</small>';
                });
        }

        // 查看医院已采用规则
        function viewAdoptedRules(hospitalId) {
            // 切换到规则推荐标签页
            const rulesTab = new bootstrap.Tab(document.getElementById('rules-tab'));
            rulesTab.show();

            // 设置医院选择
            document.getElementById('ruleHospitalSelect').value = hospitalId;

            // 直接加载已采用规则
            loadAdoptedRulesDirectly(hospitalId);
        }

        // 直接加载已采用规则
        function loadAdoptedRulesDirectly(hospitalId) {
            const section = document.getElementById('recommendationsSection');
            section.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在加载已采用规则...</p></div>';

            fetch(`/api/hospital-rules/adopted/${hospitalId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 转换为推荐格式
                        const adoptedRules = data.rules.map(rule => {
                            // 从推荐原因中提取匹配项目名称
                            let matchedItem = '已采用规则';
                            if (rule.推荐原因) {
                                const match = rule.推荐原因.match(/医院收费项目'([^']+)'/);
                                if (match) {
                                    matchedItem = match[1];
                                }
                            }

                            return {
                                '适用ID': rule.适用ID,
                                '规则ID': rule.规则ID,
                                '规则名称': rule.规则名称,
                                '行为认定': rule.行为认定,
                                '类型': rule.类型 || '未知',
                                '规则类型': rule.规则类型 || '未知',
                                '规则内涵': rule.规则内涵 || '未设置',
                                '匹配度': rule.匹配度 || 1.0,
                                '推荐原因': rule.推荐原因,
                                '状态': '已采用',
                                '匹配项目': matchedItem,
                                '城市': rule.城市 || '未知',
                                '规则来源': rule.规则来源 || '未知'
                            };
                        });

                        // 显示已采用规则
                        originalRecommendations = adoptedRules;
                        filteredRecommendations = adoptedRules;

                        if (adoptedRules.length > 0) {
                            // 显示过滤器并初始化
                            document.getElementById('filterSection').style.display = 'block';
                            initializeFilters(adoptedRules);

                            // 设置状态过滤器为"已采用"
                            document.getElementById('filterStatus').value = '已采用';

                            renderFilteredRecommendations(adoptedRules);

                            // 显示批量操作工具栏，并设置为已采用页面状态
                            const toolbar = document.getElementById('batchToolbar');
                            toolbar.style.display = 'block';
                            setBatchToolbarForAdopted();
                        } else {
                            section.innerHTML = '<div class="alert alert-info">该医院暂无已采用规则</div>';
                            document.getElementById('filterSection').style.display = 'none';
                            document.getElementById('batchToolbar').style.display = 'none';
                        }
                    } else {
                        section.innerHTML = `<div class="alert alert-danger">加载已采用规则失败: ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    console.error('加载已采用规则失败:', error);
                    section.innerHTML = '<div class="alert alert-danger">加载已采用规则失败，请稍后重试</div>';
                });
        }

        // 加载医院数据分析
        function loadHospitalAnalysis(hospitalId) {
            const section = document.getElementById('hospitalDataSection');
            section.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在加载医院数据...</p></div>';

            fetch(`/api/hospital-data/${hospitalId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderHospitalData(data);
                    } else {
                        section.innerHTML = `<div class="alert alert-danger">加载医院数据失败: ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    console.error('加载医院数据失败:', error);
                    section.innerHTML = '<div class="alert alert-danger">加载医院数据失败，请稍后重试</div>';
                });
        }

        // 渲染医院数据
        function renderHospitalData(data) {
            const section = document.getElementById('hospitalDataSection');
            const hospital = data.hospital_info;
            const stats = data.statistics;
            const summary = data.summary;
            const dataList = data.data;

            let html = `
                <!-- 医院基本信息 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5><i class="bi bi-hospital"></i> ${hospital.医院名称} - 收费数据分析</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>医院编码：</strong>${hospital.医院编码 || '未设置'}
                            </div>
                            <div class="col-md-3">
                                <strong>所在城市：</strong>${hospital.所在城市 || '未设置'}
                            </div>
                            <div class="col-md-3">
                                <strong>医院等级：</strong>${hospital.医院等级 || '未设置'}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据统计 -->
                <div class="row mb-3">
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-primary">${stats.总记录数.toLocaleString()}</h4>
                                <small class="text-muted">总记录数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-success">${stats.项目种类数.toLocaleString()}</h4>
                                <small class="text-muted">项目种类</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-warning">${stats.总收费金额.toLocaleString()}</h4>
                                <small class="text-muted">总收费金额(元)</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-info">${stats.总收费次数.toLocaleString()}</h4>
                                <small class="text-muted">总收费次数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <small class="text-muted">数据时间范围</small>
                                <div>${stats.最早数据时间 || '无'} 至 ${stats.最新数据时间 || '无'}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 收费项目汇总（分类显示） -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6>收费项目汇总 (按金额排序前20)</h6>
                    </div>
                    <div class="card-body">
                        <!-- 分类标签页 -->
                        <ul class="nav nav-tabs mb-3" id="summaryTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="total-tab" data-bs-toggle="tab" data-bs-target="#total-panel" type="button" role="tab">
                                    总体前20
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="drugs-tab" data-bs-toggle="tab" data-bs-target="#drugs-panel" type="button" role="tab">
                                    药品前20
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="items-tab" data-bs-toggle="tab" data-bs-target="#items-panel" type="button" role="tab">
                                    项目前20
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials-panel" type="button" role="tab">
                                    耗材前20
                                </button>
                            </li>
                        </ul>

                        <!-- 分类内容 -->
                        <div class="tab-content" id="summaryTabContent">
                            <!-- 总体前20 -->
                            <div class="tab-pane fade show active" id="total-panel" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>医保项目编码</th>
                                                <th>医保项目名称</th>
                                                <th>总金额(元)</th>
                                                <th>总次数</th>
                                                <th>记录数</th>
                                                <th>平均单价</th>
                                            </tr>
                                        </thead>
                                        <tbody id="total-tbody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 药品前20 -->
                            <div class="tab-pane fade" id="drugs-panel" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>医保项目编码</th>
                                                <th>医保项目名称</th>
                                                <th>总金额(元)</th>
                                                <th>总次数</th>
                                                <th>记录数</th>
                                                <th>平均单价</th>
                                            </tr>
                                        </thead>
                                        <tbody id="drugs-tbody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 项目前20 -->
                            <div class="tab-pane fade" id="items-panel" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>医保项目编码</th>
                                                <th>医保项目名称</th>
                                                <th>总金额(元)</th>
                                                <th>总次数</th>
                                                <th>记录数</th>
                                                <th>平均单价</th>
                                            </tr>
                                        </thead>
                                        <tbody id="items-tbody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 耗材前20 -->
                            <div class="tab-pane fade" id="materials-panel" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>医保项目编码</th>
                                                <th>医保项目名称</th>
                                                <th>总金额(元)</th>
                                                <th>总次数</th>
                                                <th>记录数</th>
                                                <th>平均单价</th>
                                            </tr>
                                        </thead>
                                        <tbody id="materials-tbody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 填充各分类的数据
            fillSummaryTable('total', summary.total || []);
            fillSummaryTable('drugs', summary.drugs || []);
            fillSummaryTable('items', summary.items || []);
            fillSummaryTable('materials', summary.materials || []);

            html += `

                <!-- 详细数据 -->
                <div class="card">
                    <div class="card-header">
                        <h6>详细收费记录 (最新20条)</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>医保项目编码</th>
                                        <th>医保项目名称</th>
                                        <th>收费金额</th>
                                        <th>收费次数</th>
                                        <th>科室名称</th>
                                        <th>医生姓名</th>
                                        <th>患者年龄</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;

            dataList.forEach(item => {
                html += `
                    <tr>
                        <td>${item.医保项目编码 || ''}</td>
                        <td>${item.医保项目名称 || ''}</td>
                        <td class="text-end">${item.收费金额 || '0'}</td>
                        <td class="text-end">${item.收费次数 || '0'}</td>
                        <td>${item.科室名称 || ''}</td>
                        <td>${item.医生姓名 || ''}</td>
                        <td>${item.患者年龄 || ''}</td>
                        <td>${item.创建时间 || ''}</td>
                    </tr>
                `;
            });

            html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            section.innerHTML = html;
        }

        // 填充汇总表格数据
        function fillSummaryTable(type, data) {
            const tbody = document.getElementById(`${type}-tbody`);
            if (!tbody) return;

            let html = '';
            data.forEach((item, index) => {
                const avgPrice = item.总次数 > 0 ? (item.总金额 / item.总次数).toFixed(2) : '0.00';
                html += `
                    <tr>
                        <td class="text-center">${index + 1}</td>
                        <td>${item.医保项目编码}</td>
                        <td>${item.医保项目名称}</td>
                        <td class="text-end">${item.总金额.toLocaleString()}</td>
                        <td class="text-end">${item.总次数.toLocaleString()}</td>
                        <td class="text-end">${item.记录数.toLocaleString()}</td>
                        <td class="text-end">${avgPrice}</td>
                    </tr>
                `;
            });

            if (data.length === 0) {
                html = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">暂无数据</td>
                    </tr>
                `;
            }

            tbody.innerHTML = html;
        }
    </script>
</body>
</html>
