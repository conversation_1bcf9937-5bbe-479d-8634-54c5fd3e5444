import pandas as pd
import os
from datetime import datetime

# 定义文件路径
summary_file = r"C:\Users\<USER>\Desktop\工作个人\2025重庆飞检\重庆市_重庆大学附属涪陵医院_医院_20250512134840_多数据源导出-汇总结果.xlsx"
base_path = r"C:\Users\<USER>\Desktop\工作个人\2025重庆飞检\重庆市_重庆大学附属涪陵医院_医院_20250512134840_多数据源导出"  # 替换为实际的文件路径
error_log_file = "ERROR.log"  # 定义错误日志文件路径

# 读取汇总文件
summary_df = pd.read_excel(summary_file)

# 打开错误日志文件（追加模式）
with open(error_log_file, "a", encoding="utf-8") as error_log:
    # 遍历汇总文件中的每一行
    for index, row in summary_df.iterrows():
        try:
            file_name = row["文件名"]
            # 构造完整文件路径
            file_path = os.path.join(base_path, file_name)

            # 检查文件是否存在
            if os.path.exists(file_path):
                # 打印当前处理的文件名
                print(f"正在处理文件: {file_name}")

                # 读取指定路径下的Excel文件
                data_df = pd.read_excel(file_path)

                # 检查“违规数量”和“违规金额”列是否存在
                if "违规数量" not in data_df.columns or "违规金额" not in data_df.columns:
                    error_message = f"文件 {file_name} 缺少必要的列"
                    print(error_message)
                    error_log.write(f"{datetime.now()} - {error_message}\n")
                    continue

                # 计算违规数量的总和
                total_violation_count = data_df["违规数量"].sum()

                # 计算违规金额的总和
                total_violation_amount = data_df["违规金额"].sum()

                # 将结果回填到汇总文件的“违规数量”列和“违规金额(元)”列
                summary_df.at[index, "违规数量"] = total_violation_count
                summary_df.at[index, "违规金额(元)"] = total_violation_amount

                # 打印处理完成的日志
                print(f"处理完成: 文件 {file_name}，违规数量 {total_violation_count} 已回填，违规金额 {total_violation_amount} 已回填")
            else:
                # 打印文件不存在的日志
                error_message = f"文件 {file_name} 不存在"
                print(error_message)
                error_log.write(f"{datetime.now()} - {error_message}\n")
        except Exception as e:
            # 捕获其他异常
            error_message = f"处理行 {index} 时发生错误: {e}，跳过该行"
            print(error_message)
            error_log.write(f"{datetime.now()} - {error_message}\n")

# 保存修改后的汇总文件
summary_df.to_excel(summary_file, index=False)

print("所有处理完成")