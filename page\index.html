<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞检数据处理工具箱</title>
    <style>
        :root {
            --primary-color: #0078D4;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --hover-bg: #f1f5f9;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background-color: var(--card-bg);
            padding: 2rem 1.5rem;
            height: 100vh;
            position: fixed;
            box-shadow: var(--shadow-sm);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(0,0,0,0.06);
        }

        .sidebar h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--text-primary);
            padding: 0 0.5rem;
        }

        .sidebar ul {
            list-style-type: none;
        }

        .sidebar li {
            margin-bottom: 0.5rem;
        }

        .sidebar a {
            text-decoration: none;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .sidebar a:hover {
            background-color: var(--hover-bg);
            color: var(--text-primary);
        }

        .sidebar a i {
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }

        .main-content {
            margin-left: 280px;
            flex-grow: 1;
            padding: 2rem;
        }

        h1 {
            font-size: 1.875rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--text-primary);
        }

        .tool-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .tool-item {
            background-color: var(--card-bg);
            border-radius: var(--radius);
            padding: 1.5rem;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.2s ease;
            border: 1px solid rgba(0,0,0,0.06);
            position: relative;
            overflow: hidden;
        }

        .tool-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
            border-color: var(--primary-color);
        }

        .tool-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background-color: var(--primary-color);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .tool-item:hover::before {
            opacity: 1;
        }

        .tool-item i {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .tool-item h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .tool-item p {
            color: var(--text-secondary);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 80px;
                padding: 1.5rem 0.75rem;
            }
            
            .sidebar h2 {
                display: none;
            }
            
            .sidebar a span {
                display: none;
            }
            
            .main-content {
                margin-left: 80px;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <div class="sidebar">
        <h2>数据处理工具箱</h2>
        <nav>
            <ul>
                <li><a href=""><i class="fas fa-home"></i><span>主页</span></a></li>
                <li><a href="rule_knowledge_base"><i class="fas fa-book"></i><span>飞检规则知识库</span></a></li>
                <li><a href="hospital_rules"><i class="fas fa-hospital"></i><span>医院规则管理</span></a></li>
                <li><a href="violation_feedback"><i class="fas fa-shield-exclamation"></i><span>飞检违规反馈</span></a></li>
                <li><a href="rule_sql_generator"><i class="fas fa-code"></i><span>规则SQL生成器</span></a></li>
                <li><a href="system_rules"><i class="fas fa-cogs"></i><span>系统规则语句</span></a></li>
                <li><a href="sql_generator"><i class="fas fa-database"></i><span>SQL生成器</span></a></li>
                <li><a href="db_query"><i class="fas fa-table"></i><span>数据库查询生成Excel</span></a></li>
                <li><a href="batch_query"><i class="fas fa-tasks"></i><span>批量SQL查询生成Excel</span></a></li>
                <li><a href="sql_performance"><i class="fas fa-tachometer-alt"></i><span>SQL性能测试</span></a></li>
                <li><a href="excel_splitter"><i class="far fa-file-excel"></i><span>Excel文件拆分</span></a></li>
                <li><a href="excel_delete"><i class="fas fa-eraser"></i><span>Excel内容删除</span></a></li>
                <li><a href="excel_compare"><i class="fas fa-file-excel"></i><span>Excel比对工具</span></a></li>
                <li><a href="excel_to_sql"><i class="fas fa-file-code"></i><span>Excel转SQL工具</span></a></li>
                <li><a href="find_duplicates"><i class="fas fa-file-alt"></i><span>查找重复文件</span></a></li>
                <li><a href="data_validator"><i class="fas fa-check-circle"></i><span>数据校验</span></a></li>
                <li><a href="data_standardization"><i class="fas fa-cogs"></i><span>数据标准化</span></a></li>
            </ul>
        </nav>
    </div>
    <div class="main-content">
        <h1>工具列表</h1>
        <div class="tool-grid">
            <a href="rule_knowledge_base" class="tool-item">
                <i class="fas fa-book"></i>
                <h3>飞检规则知识库</h3>
                <p>维护和管理飞检规则知识库，支持规则的增删改查</p>
            </a>
            <a href="hospital_rules" class="tool-item">
                <i class="fas fa-hospital"></i>
                <h3>医院规则管理</h3>
                <p>智能推荐医院适用规则，支持规则采用、忽略、SQL生成和数据库导入</p>
            </a>
            <a href="violation_feedback" class="tool-item">
                <i class="fas fa-shield-exclamation"></i>
                <h3>飞检违规反馈</h3>
                <p>汇总飞检Excel文件规则详情，统计违规数量和金额，生成违规反馈报告</p>
            </a>
            <a href="rule_sql_generator" class="tool-item">
                <i class="fas fa-code"></i>
                <h3>规则SQL生成器</h3>
                <p>根据规则模板快速生成SQL查询语句，支持多种规则类型和变量替换</p>
            </a>
            <a href="system_rules" class="tool-item">
                <i class="fas fa-cogs"></i>
                <h3>系统规则语句</h3>
                <p>内置通用规则，支持规则配置和管理</p>
            </a>
            <a href="sql_generator" class="tool-item">
                <i class="fas fa-database"></i>
                <h3>SQL生成器</h3>
                <p>快速生成SQL查询语句，支持多种数据库类型，简化查询操作</p>
            </a>
            <a href="db_query" class="tool-item">
                <i class="fas fa-table"></i>
                <h3>数据库查询生成Excel</h3>
                <p>将数据库查询结果快速导出为Excel格式，支持多种导出模板</p>
            </a>
            <a href="batch_query" class="tool-item">
                <i class="fas fa-tasks"></i>
                <h3>批量SQL查询生成Excel</h3>
                <p>一次执行多个SQL查询并导出Excel，提高工作效率，支持并行处理</p>
            </a>
            <a href="sql_performance" class="tool-item">
                <i class="fas fa-tachometer-alt"></i>
                <h3>SQL性能测试</h3>
                <p>测试SQL查询执行时间，分析查询性能并生成报告</p>
            </a>
            <a href="excel_splitter" class="tool-item">
                <i class="far fa-file-excel"></i>
                <h3>Excel文件拆分</h3>
                <p>智能拆分大型Excel文件，支持多种拆分规则和自定义选项</p>
            </a>
            <a href="excel_delete" class="tool-item">
                <i class="fas fa-eraser"></i>
                <h3>Excel内容删除</h3>
                <p>根据指定条件批量删除Excel文件中的内容，支持多文件处理</p>
            </a>
            <a href="excel_compare" class="tool-item">
                <i class="fas fa-file-excel"></i>
                <h3>Excel比对工具</h3>
                <p>比较两个Excel文件，找出差异并生成报告</p>
            </a>
            <a href="excel_to_sql" class="tool-item">
                <i class="fas fa-file-code"></i>
                <h3>Excel转SQL工具</h3>
                <p>将Excel数据转换为SQL语句，支持批量生成和自定义模板</p>
            </a>
            <a href="find_duplicates" class="tool-item">
                <i class="fas fa-file-alt"></i>
                <h3>查找重复文件</h3>
                <p>查找指定目录中的重复文件</p>
            </a>
            <a href="data_validator" class="tool-item">
                <i class="fas fa-check-circle"></i>
                <h3>数据校验</h3>
                <p>检查Excel文件中的数据完整性和格式，支持多种验证规则</p>
            </a>
            <a href="data_standardization" class="tool-item">
                <i class="fas fa-cogs"></i>
                <h3>数据标准化</h3>
                <p>对数据进行标准化处理，确保数据一致性</p>
            </a>
        </div>
    </div>
</body>
</html>
