from flask import Flask, render_template, render_template_string, request, redirect, url_for, flash, send_file, jsonify, session, make_response
import os
import pandas as pd
import io
import zipfile
from sql_query import *
import oracledb
import psycopg2
import pymysql
import pyodbc
import psycopg_pool
from contextlib import contextmanager
import configparser
import re
import logging
import unicodedata
from typing import List, Tuple, Optional
from werkzeug.datastructures import FileStorage
import uuid
import shutil  # 用于删除临时目录
import hashlib
import concurrent.futures
from multiprocessing import Pool
import functools
from multiprocessing import cpu_count, Process, Queue
import tempfile
import atexit
import threading
from pathlib import Path
import logging.handlers
from typing import Dict, Any, Optional, Union, Tuple
from functools import wraps
import traceback
import json
import math
from functools import lru_cache
import redis
import pickle
import time
from configparser import ConfigParser
from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.responses import FileResponse
from datetime import datetime, timedelta  # 添加 timedelta 以备后用
import openpyxl
from difflib import SequenceMatcher
import jieba
import requests
try:
    from google import genai
    GENAI_AVAILABLE = True
except ImportError:
    GENAI_AVAILABLE = False

from gemini_config import GEMINI_API_KEY, GEMINI_API_URL, GEMINI_RULE_ANALYSIS_PROMPT, GEMINI_TIMEOUT
# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 创建应用实例时指定模板文件夹
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
app = Flask(__name__, 
    template_folder=os.path.join(BASE_DIR, 'page'))  # 将模板文件夹指向 page 目录

app.secret_key = 'your-secret-key-here'

# 设置上传文件夹和输出文件夹
app.config['UPLOAD_FOLDER'] = os.path.join(BASE_DIR, 'uploads')
app.config['OUTPUT_FOLDER'] = os.path.join(BASE_DIR, 'outputs')

# 确保文件夹存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)

# 设置日志
logging.basicConfig(level=logging.INFO)

# 添加这些配置来禁用模板缓存
app.config['TEMPLATES_AUTO_RELOAD'] = True
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0

# 设置上传文件夹
UPLOAD_FOLDER = os.path.join(os.path.dirname(__file__), 'uploads')
OUTPUT_FOLDER = os.path.join(os.path.dirname(__file__), 'output')
SQL_TEMPLATES_FOLDER = os.path.join(os.path.dirname(__file__), 'templates')

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['OUTPUT_FOLDER'] = OUTPUT_FOLDER
app.config['SQL_TEMPLATES_FOLDER'] = SQL_TEMPLATES_FOLDER

# 确保必要的文件夹存在
for folder in [UPLOAD_FOLDER, OUTPUT_FOLDER, SQL_TEMPLATES_FOLDER]:
    os.makedirs(folder, exist_ok=True)

# 读取配置文件
config = configparser.ConfigParser()
config.read('config.ini')

# 全局变量用于跟踪临时文件
temp_files = set()
temp_files_lock = threading.Lock()

def cleanup_temp_files():
    """清理所有临时文件"""
    with temp_files_lock:
        for file_path in temp_files:
            try:
                if os.path.exists(file_path):
                    if os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                    else:
                        os.remove(file_path)
            except Exception as e:
                logging.error(f"Error cleaning up temp file {file_path}: {str(e)}")
        temp_files.clear()

# 注册退出时的清理函数
atexit.register(cleanup_temp_files)

def create_temp_dir() -> str:
    """创建临时目录并跟踪它"""
    temp_dir = tempfile.mkdtemp()
    with temp_files_lock:
        temp_files.add(temp_dir)
    return temp_dir

def create_temp_file(suffix: Optional[str] = None) -> str:
    """创建临时文件并跟踪它"""
    fd, temp_path = tempfile.mkstemp(suffix=suffix)
    os.close(fd)
    with temp_files_lock:
        temp_files.add(temp_path)
    return temp_path

def safe_file_path(base_dir: str, filename: str) -> str:
    """安全地构建文件路径，防止目录遍历攻击"""
    try:
        base_path = Path(base_dir).resolve()
        file_path = (base_path / filename).resolve()
        if base_path in file_path.parents:
            return str(file_path)
        raise ValueError("Attempted path traversal")
    except Exception as e:
        logging.error(f"Error in safe_file_path: {str(e)}")
        raise ValueError("Invalid file path")

def process_large_file(file_path: str, chunk_size: int = 8192):
    """以块的方式处理大文件"""
    try:
        with open(file_path, 'rb') as f:
            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                yield chunk
    except Exception as e:
        logging.error(f"Error processing large file {file_path}: {str(e)}")
        raise

def calculate_file_hash(file_path: str) -> str:
    """计算文件的SHA-256哈希值"""
    sha256_hash = hashlib.sha256()
    try:
        for chunk in process_large_file(file_path):
            sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    except Exception as e:
        logging.error(f"Error calculating file hash for {file_path}: {str(e)}")
        raise

class FileManager:
    def __init__(self, base_dir: str):
        self.base_dir = Path(base_dir).resolve()
        os.makedirs(self.base_dir, exist_ok=True)

    def safe_save_file(self, file: FileStorage, filename: str) -> str:
        """安全地保存上传的文件"""
        try:
            safe_filename = secure_filename(filename)
            file_path = safe_file_path(str(self.base_dir), safe_filename)
            file.save(file_path)
            return file_path
        except Exception as e:
            logging.error(f"Error saving file {filename}: {str(e)}")
            raise

    def safe_read_file(self, filename: str) -> bytes:
        """安全地读取文件"""
        try:
            file_path = safe_file_path(str(self.base_dir), filename)
            return Path(file_path).read_bytes()
        except Exception as e:
            logging.error(f"Error reading file {filename}: {str(e)}")
            raise

    def safe_delete_file(self, filename: str) -> bool:
        """安全地删除文件"""
        try:
            file_path = safe_file_path(str(self.base_dir), filename)
            os.remove(file_path)
            return True
        except Exception as e:
            logging.error(f"Error deleting file {filename}: {str(e)}")
            return False
        
class DatabasePoolManager:
    """数据库连接池管理器"""
    def __init__(self):
        self.pools: Dict[str, Dict[str, any]] = {
            'oracle': {},  # 可以包含多个 Oracle 连接池
            'postgresql': {},
            'mysql': {},
            'sqlserver': {}
        }
        self.default_pool = None

    def init_pools(self, config):
        """初始化所有配置的数据库连接池"""
        try:
            # 初始化默认连接池（Oracle）
            if 'datachange' in config.sections():
                default_pool = self.create_oracle_pool(
                    'default',
                    username=config.get('datachange', 'username'),
                    password=config.get('datachange', 'password'),
                    dsn=config.get('datachange', 'dsn')
                )
                self.default_pool = default_pool

            # 初始化其他 Oracle 连接池
            if 'oracle_pools' in config.sections():
                for pool_name in config.options('oracle_pools'):
                    pool_config = eval(config.get('oracle_pools', pool_name))
                    self.create_oracle_pool(pool_name, **pool_config)

            # 初始化 PostgreSQL 连接池
            if 'postgresql_pools' in config.sections():
                for pool_name in config.options('postgresql_pools'):
                    pool_config = eval(config.get('postgresql_pools', pool_name))
                    self.create_postgresql_pool(pool_name, **pool_config)

            # 初始化 MySQL 连接池
            if 'mysql_pools' in config.sections():
                for pool_name in config.options('mysql_pools'):
                    pool_config = eval(config.get('mysql_pools', pool_name))
                    self.create_mysql_pool(pool_name, **pool_config)

            # 初始化 SQL Server 连接池
            if 'sqlserver_pools' in config.sections():
                for pool_name in config.options('sqlserver_pools'):
                    pool_config = eval(config.get('sqlserver_pools', pool_name))
                    self.create_sqlserver_pool(pool_name, **pool_config)

        except Exception as e:
            logging.error(f"Failed to initialize database pools: {str(e)}")
            raise

    def create_oracle_pool(self, pool_name: str, **kwargs) -> oracledb.ConnectionPool:
        """创建 Oracle 连接池"""
        pool = oracledb.create_pool(
            user=kwargs.get('username'),
            password=kwargs.get('password'),
            dsn=kwargs.get('dsn'),
            min=kwargs.get('min', 2),
            max=kwargs.get('max', 5),
            increment=kwargs.get('increment', 1),
            getmode=oracledb.POOL_GETMODE_WAIT,
            wait_timeout=kwargs.get('wait_timeout', 10000),
            timeout=kwargs.get('timeout', 300),
            retry_count=kwargs.get('retry_count', 3),
            retry_delay=kwargs.get('retry_delay', 2),
            max_lifetime_session=kwargs.get('max_lifetime_session', 28800)
        )
        self.pools['oracle'][pool_name] = pool
        return pool

    def create_postgresql_pool(self, pool_name: str, **kwargs):
        """创建 PostgreSQL 连接池"""
        # 使用 psycopg2 的连接池实现
        pass

    def create_mysql_pool(self, pool_name: str, **kwargs):
        """创建 MySQL 连接池"""
        # 使用 PyMySQL 的连接池实现
        pass

    def create_sqlserver_pool(self, pool_name: str, **kwargs):
        """创建 SQL Server 连接池"""
        # 使用 pyodbc 的连接池实现
        pass

    @contextmanager
    def get_connection(self, db_type: str = 'oracle', pool_name: str = 'default'):
        """获取数据库连接的上下文管理器"""
        pool = self.pools.get(db_type, {}).get(pool_name) or self.default_pool
        if not pool:
            raise ValueError(f"No pool found for {db_type}:{pool_name}")
            
        conn = pool.acquire()
        try:
            yield conn
        finally:
            pool.release(conn)

# 创建全局连接池管理器实例
db_manager = DatabasePoolManager()
def connect_to_oracle(username, password, dsn):
    """
    Establish a connection to Oracle database
    
    Args:
        username (str): Database username
        password (str): Database password
        dsn (str): Database connection string
    
    Returns:
        oracledb.Connection: Database connection
    """
    try:
        connection = oracledb.connect(
            user=username,
            password=password,
            dsn=dsn
        )
        return connection
    except oracledb.Error as e:
        logging.error(f"Database connection error: {str(e)}")
        raise
def execute_query(connection_params, sql_filename, sql_content, results_dir, no_results_dir, output_files, error_messages):
    try:
        connection = oracledb.connect(**connection_params)
        cursor = connection.cursor()
        
        # 分割多个SQL语句
        sql_statements = sql_content.split(';')
        
        results = None
        for statement in sql_statements:
            statement = statement.strip()
            if statement:
                try:
                    cursor.execute(statement)
                    if cursor.description:  # 如果有结果集
                        results = cursor.fetchall()
                        break  # 获取到结果后就停止执行后续语句
                except oracledb.DatabaseError as e:
                    error_code, error_message = e.args[0].code, e.args[0].message
                    error_messages.append(f"执行SQL语句时出错 (文件: {sql_filename}):\n错误代码: {error_code}\n错误信息: {error_message}\nSQL语句:\n{statement}")
                    continue  # 继续执行下一条语句
        
        if results:
            # 查询有结果
            df = pd.DataFrame(results, columns=[col[0] for col in cursor.description])
            output_filename = f"{os.path.splitext(sql_filename)[0]}_result.xlsx"
            output_path = os.path.join(results_dir, output_filename)
            df.to_excel(output_path, index=False)
            output_files.append((output_path, output_filename))
            
            # 保存SQL文件到结果目录
            with open(os.path.join(results_dir, sql_filename), 'w', encoding='utf-8') as f:
                f.write(sql_content)
        else:
            # 查询无结果
            with open(os.path.join(no_results_dir, sql_filename), 'w', encoding='utf-8') as f:
                f.write(sql_content)
        
        cursor.close()
        connection.close()
    except Exception as e:
        error_messages.append(f"处理文件 {sql_filename} 时出错: {str(e)}\nSQL内容:\n{sql_content}")
def split_and_save_to_excel(df, group_columns, output_dir, prefix, suffix):
    output_files = []
    unique_values = df[group_columns].drop_duplicates()
    
    for _, row in unique_values.iterrows():
        subset = df
        for column in group_columns:
            subset = subset[subset[column] == row[column]]
        
        filename_parts = [str(row[col]) for col in group_columns]
        filename = '_'.join(filter(None, [prefix] + filename_parts + [suffix])) + '.xlsx'
        filename = sanitize_filename(filename)

        output_file = os.path.join(output_dir, filename)
        subset.to_excel(output_file, index=False)
        output_files.append((output_file, filename))
        print(f"数据已保存至文件: {filename}")
    
    return output_files
def execute_query_in_chunks(connection_params, query, chunk_size=50000):
    try:
        with oracledb.connect(**connection_params) as connection:
            cursor = connection.cursor()
            cursor.execute(query)
            columns = [col[0] for col in cursor.description]
            
            while True:
                rows = cursor.fetchmany(chunk_size)
                if not rows:
                    break
                yield pd.DataFrame(rows, columns=columns)
    except Exception as e:
        raise Exception(f"执行查询时出错: {str(e)}")

def process_chunk(df, group_columns, output_dir, prefix, suffix):
    output_files = split_and_save_to_excel(df, group_columns, output_dir, prefix, suffix)
    return output_files

def thread_worker(connection_params, sql_files, results_dir, no_results_dir, output_files, error_messages, num_threads=2):
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [executor.submit(execute_query, connection_params, sql_filename, sql_content, results_dir, no_results_dir, output_files, error_messages) for sql_filename, sql_content in sql_files]
        concurrent.futures.wait(futures)

def process_worker(connection_params, sql_files, results_dir, no_results_dir, output_queue, error_queue, num_threads=2):
    output_files = []
    error_messages = []
    thread_worker(connection_params, sql_files, results_dir, no_results_dir, output_files, error_messages, num_threads)
    output_queue.put(output_files)
    error_queue.put(error_messages)   

# 初始化文件管理器
upload_manager = FileManager(app.config['UPLOAD_FOLDER'])
output_manager = FileManager(app.config['OUTPUT_FOLDER'])

def query_medical_codes(medical_name1, medical_name2, city='沈阳'):
    """
    查询医保三目表获取医保项目编码

    Args:
        medical_name1 (str): 医保名称1，多个名称用逗号分隔
        medical_name2 (str): 医保名称2，多个名称用逗号分隔
        city (str): 城市名称，默认为沈阳

    Returns:
        dict: 包含查询结果的字典
    """
    try:
        # 分别处理医保名称1和医保名称2
        names1 = []
        names2 = []

        if medical_name1:
            names1 = [name.strip() for name in medical_name1.split(',') if name.strip()]

        if medical_name2:
            names2 = [name.strip() for name in medical_name2.split(',') if name.strip()]

        if not names1 and not names2:
            return {
                'success': True,
                'results': [],
                'medical_codes1': '',
                'medical_codes2': '',
                'message': '没有提供医保名称'
            }

        # 分别查询医保名称1和医保名称2对应的编码
        def query_codes_for_names(names, name_type):
            if not names:
                return []

            # 构建查询条件 - 使用精确匹配
            name_conditions = []
            params = {'city': city}

            for i, name in enumerate(names):
                param_name = f'name_{i}'
                name_conditions.append(f"c.医保项目名称 = :{param_name}")
                params[param_name] = name

            # 构建SQL查询
            query = f"""
            SELECT c.医保项目编码, c.医保项目名称, c.城市, c.国家编码
            FROM 医保三目表 c
            WHERE c.城市 = :city
            AND ({' OR '.join(name_conditions)})
            ORDER BY c.医保项目名称
            """

            app.logger.info(f"查询{name_type} - 城市: {city}, 医保名称: {names}")
           #app.logger.info(f"SQL: {query}")
           #app.logger.info(f"参数: {params}")

            with get_connection(pool) as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, params)
                    results = cursor.fetchall()

                    # 收集编码
                    codes = []
                    for row in results:
                        if row[0]:  # 确保编码不为空
                            codes.append(row[0])

                    return codes

        # 分别查询医保名称1和医保名称2的编码
        codes1 = query_codes_for_names(names1, "医保名称1")
        codes2 = query_codes_for_names(names2, "医保名称2")

        # 用逗号拼接编码
        joined_codes1 = ','.join(codes1)
        joined_codes2 = ','.join(codes2)

        # 合并所有结果用于详细信息
        all_names = names1 + names2
        all_codes = codes1 + codes2

        app.logger.info(f"医保名称1查询到 {len(codes1)} 条编码: {joined_codes1}")
        app.logger.info(f"医保名称2查询到 {len(codes2)} 条编码: {joined_codes2}")

        return {
            'success': True,
            'medical_codes1': joined_codes1,  # 医保名称1对应的编码
            'medical_codes2': joined_codes2,  # 医保名称2对应的编码
            'query_names1': names1,
            'query_names2': names2,
            'city': city,
            'message': f'医保名称1查询到 {len(codes1)} 条记录，医保名称2查询到 {len(codes2)} 条记录'
        }

    except Exception as e:
        app.logger.error(f"查询医保三目表失败: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'results': []
        }

def call_gemini_api(rule_content):
    """
    调用Gemini API分析规则内容，提取医保名称

    Args:
        rule_content (str): 规则内容

    Returns:
        dict: 包含医保名称1、医保名称2等信息的字典
    """
    try:
        # 构建完整的提示词
        prompt = GEMINI_RULE_ANALYSIS_PROMPT + rule_content

        # 构建请求数据
        request_data = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt
                        }
                    ]
                }
            ],
            "generationConfig": {
                "temperature": 0.1,
                "topK": 1,
                "topP": 0.8,
                "maxOutputTokens": 4096,
                "responseMimeType": "application/json",
                "thinkingConfig": {
                    "thinkingBudget": 0  # 禁用thinking模式以提高速度
                }
            },
            "safetySettings": [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_HATE_SPEECH",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]
        }

        # 设置请求头
        headers = {
            'Content-Type': 'application/json',
        }

        # 构建完整的URL
        url = f"{GEMINI_API_URL}?key={GEMINI_API_KEY}"

        # 发送请求
        response = requests.post(
            url,
            headers=headers,
            json=request_data,
            timeout=GEMINI_TIMEOUT
        )

        if response.status_code == 200:
            result = response.json()
            logging.info(f"Gemini API原始响应: {result}")

            # 解析响应
            if 'candidates' in result and len(result['candidates']) > 0:
                candidate = result['candidates'][0]

                # 检查是否因为MAX_TOKENS而被截断
                if candidate.get('finishReason') == 'MAX_TOKENS':
                    logging.warning("Gemini响应被截断，原因：MAX_TOKENS")

                # 尝试获取文本响应
                text_response = None
                if 'content' in candidate and 'parts' in candidate['content']:
                    text_response = candidate['content']['parts'][0]['text']
                elif 'content' in candidate and 'text' in candidate['content']:
                    text_response = candidate['content']['text']
                elif 'text' in candidate:
                    text_response = candidate['text']

                if text_response:

                    # 尝试解析JSON响应
                    try:
                        # 清理响应文本，移除可能的markdown格式
                        clean_text = text_response.strip()
                        if clean_text.startswith('```json'):
                            clean_text = clean_text[7:]
                        if clean_text.endswith('```'):
                            clean_text = clean_text[:-3]
                        clean_text = clean_text.strip()

                        parsed_result = json.loads(clean_text)

                        # 处理可能的列表响应
                        if isinstance(parsed_result, list):
                            if len(parsed_result) > 0:
                                parsed_result = parsed_result[0]  # 取第一个元素
                            else:
                                parsed_result = {}  # 空列表的情况

                        # 确保parsed_result是字典
                        if not isinstance(parsed_result, dict):
                            logging.error(f"解析结果不是字典类型: {type(parsed_result)}, 内容: {parsed_result}")
                            return {
                                'success': False,
                                'error': f'AI响应格式错误: 期望字典，得到{type(parsed_result)}',
                                'raw_response': clean_text
                            }

                        return {
                            'success': True,
                            'medical_name1': parsed_result.get('medical_name1', ''),
                            'medical_name2': parsed_result.get('medical_name2', ''),
                            'type': parsed_result.get('violation_type', parsed_result.get('type', '')),  # 支持新旧字段名
                            'violation_count': parsed_result.get('violation_count', ''),
                            'exclude_diagnosis': parsed_result.get('exclude_diagnosis', ''),
                            'exclude_departments': parsed_result.get('exclude_departments', ''),
                            'include_diagnosis': parsed_result.get('include_diagnosis', ''),
                            'include_departments': parsed_result.get('include_departments', ''),
                            'time_type': parsed_result.get('time_type', ''),
                            'violation_amount': parsed_result.get('violation_amount', ''),
                            'age_limit': parsed_result.get('age_limit', ''),
                            'gender_limit': parsed_result.get('gender_limit', ''),
                            'confidence': parsed_result.get('confidence', 0.0),
                            'reasoning': parsed_result.get('reasoning', '')
                        }
                    except json.JSONDecodeError as e:
                        logging.error(f"JSON解析失败: {str(e)}, 响应内容: {clean_text}")

                        # 尝试修复不完整的JSON
                        try:
                            # 如果JSON不完整，尝试添加缺失的结束符
                            fixed_text = clean_text.strip()

                            # 处理未闭合的字符串
                            if '"' in fixed_text and not fixed_text.endswith('"') and not fixed_text.endswith('}'):
                                # 查找最后一个未闭合的引号
                                last_quote_pos = fixed_text.rfind('"')
                                if last_quote_pos > 0:
                                    # 检查这个引号前面是否有冒号，说明是值的开始
                                    before_quote = fixed_text[:last_quote_pos].strip()
                                    if before_quote.endswith(':'):
                                        fixed_text += '"'

                            # 处理未闭合的JSON对象
                            if not fixed_text.endswith('}'):
                                fixed_text += '}'

                            # 尝试解析修复后的JSON
                            parsed_result = json.loads(fixed_text)
                            logging.info(f"JSON修复成功: {fixed_text}")

                            # 处理可能的列表响应
                            if isinstance(parsed_result, list):
                                if len(parsed_result) > 0:
                                    parsed_result = parsed_result[0]  # 取第一个元素
                                else:
                                    parsed_result = {}  # 空列表的情况

                            # 确保parsed_result是字典
                            if not isinstance(parsed_result, dict):
                                logging.error(f"修复后的解析结果不是字典类型: {type(parsed_result)}")
                                return {
                                    'success': False,
                                    'error': f'AI响应格式错误: 期望字典，得到{type(parsed_result)}',
                                    'raw_response': fixed_text
                                }

                            return {
                                'success': True,
                                'medical_name1': parsed_result.get('medical_name1', ''),
                                'medical_name2': parsed_result.get('medical_name2', ''),
                                'type': parsed_result.get('violation_type', parsed_result.get('type', '')),  # 支持新旧字段名
                                'violation_count': parsed_result.get('violation_count', ''),
                                'exclude_diagnosis': parsed_result.get('exclude_diagnosis', ''),
                                'exclude_departments': parsed_result.get('exclude_departments', ''),
                                'include_diagnosis': parsed_result.get('include_diagnosis', ''),
                                'include_departments': parsed_result.get('include_departments', ''),
                                'time_type': parsed_result.get('time_type', ''),
                                'violation_amount': parsed_result.get('violation_amount', ''),
                                'age_limit': parsed_result.get('age_limit', ''),
                                'gender_limit': parsed_result.get('gender_limit', ''),
                                'confidence': parsed_result.get('confidence', 0.0),
                                'reasoning': parsed_result.get('reasoning', '')
                            }
                        except Exception as fix_error:
                            logging.error(f"JSON修复也失败: {str(fix_error)}")

                        return {
                            'success': False,
                            'error': f'AI响应格式错误: {str(e)}',
                            'raw_response': clean_text
                        }

            return {
                'success': False,
                'error': 'API响应格式不正确',
                'raw_response': result
            }
        else:
            logging.error(f"Gemini API请求失败: {response.status_code}, {response.text}")
            return {
                'success': False,
                'error': f'API请求失败: {response.status_code}',
                'details': response.text
            }

    except requests.exceptions.Timeout:
        logging.error("Gemini API请求超时")
        return {
            'success': False,
            'error': 'API请求超时'
        }
    except requests.exceptions.RequestException as e:
        logging.error(f"Gemini API请求异常: {str(e)}")
        return {
            'success': False,
            'error': f'API请求异常: {str(e)}'
        }
    except Exception as e:
        logging.error(f"调用Gemini API时发生未知错误: {str(e)}")
        return {
            'success': False,
            'error': f'未知错误: {str(e)}'
        }

@app.route('/')
def index():
    return render_template('index.html')  # 这里会使用 page/index.html


@app.route('/sql_generator')
def sql_generator():
    """SQL生成器页面"""
    try:
        # 获取模板类型参数
        template_type = request.args.get('template_type', 'rule')
        templates = list_sql_templates(template_type)
        selected_template = request.args.get('template', '')
        
        # 初始化变量列表
        variables = []
        
        # 如果选择了模板，则获取变量
        if selected_template:
            template_path = os.path.join('templates', template_type, selected_template)
            if os.path.exists(template_path):
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 根据模板类型提取变量
                    if template_type == 'manual':
                        variables = list(set(re.findall(r'\{(\w+)\}', content)))
                    elif template_type in ['rule', 'excel']:
                        # 对于rule和excel类型，也需要返回variables以保持模板正常工作
                        variables = ['placeholder']  # 或其他适当的默认值
        
        return render_template('sql_query.html', 
                             templates=templates,
                             template_type=template_type,
                             selected_template=selected_template,
                             variables=variables)
                             
    except Exception as e:
        flash(str(e))
        return render_template('sql_query.html', 
                             templates=[], 
                             template_type=template_type,
                             variables=[])

@app.route('/excel_splitter')
def excel_splitter():
    return render_template('excel_splitter.html')

@app.route('/db_query')
def db_query():
    db_username = config.get('database', 'username', fallback='')
    db_password = config.get('database', 'password', fallback='')
    db_dsn = config.get('database', 'dsn', fallback='')
    return render_template('db_query.html', db_username=db_username, db_password=db_password, db_dsn=db_dsn)

@app.route('/batch_query',methods=['GET', 'POST'])
def batch_query():
    if request.method == 'GET':
        config.read('config.ini')
        db_username = config.get('database', 'username', fallback='')
        db_password = config.get('database', 'password', fallback='')
        db_dsn = config.get('database', 'dsn', fallback='')
        return render_template('batch_query.html', db_username=db_username, db_password=db_password, db_dsn=db_dsn)
    
    elif request.method == 'POST':
        db_username = request.form['db_username']
        db_password = request.form['db_password']
        db_dsn = request.form['db_dsn']
        num_processes = max(1, int(request.form.get('num_processes', 5)))
        num_threads = max(1, int(request.form.get('num_threads', 8)))
        if 'sql_files' not in request.files:
            return render_template('batch_query.html', message='没有选择SQL文件')
        
        sql_files = request.files.getlist('sql_files')
        
        if not sql_files or sql_files[0].filename == '':
            return render_template('batch_query.html', message='没有选择SQL文件')
        
        try:
            connection_params = {
                'user': db_username,
                'password': db_password,
                'dsn': db_dsn
            }
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_output_dir = os.path.join(app.config['OUTPUT_FOLDER'], f'batch_query_results_{timestamp}')
            results_dir = os.path.join(base_output_dir, 'results')
            no_results_dir = os.path.join(base_output_dir, 'no_results')
            os.makedirs(results_dir, exist_ok=True)
            os.makedirs(no_results_dir, exist_ok=True)
            
            output_queue = Queue()
            error_queue = Queue()
            
            # 读取SQL文件内容
            sql_file_contents = []
            for sql_file in sql_files:
                content = sql_file.read().decode('utf-8')
                sql_file_contents.append((sql_file.filename, content))
            
            # 根据文件数量决定处理方式
            if len(sql_file_contents) >= 5:
                # 多进程处理
                num_processes = min(num_processes, len(sql_file_contents))
                # 计算每个进程处理的SQL文件数量
                # 确保至少为1，并且尽量平均分配给所有进程
                batch_size = max(1, len(sql_file_contents) // num_processes)
                sql_file_batches = [sql_file_contents[i:i + batch_size] for i in range(0, len(sql_file_contents), batch_size)]
                
                processes = []
                for batch in sql_file_batches:
                    process = Process(target=process_worker, args=(connection_params, batch, results_dir, no_results_dir, output_queue, error_queue, num_threads))
                    processes.append(process)
                    process.start()
                
                for process in processes:
                    process.join()
            else:
                # 单进程处理
                process_worker(connection_params, sql_file_contents, results_dir, no_results_dir, output_queue, error_queue, num_threads)
            
            output_files = []
            error_messages = []
            while not output_queue.empty():
                output_files.extend(output_queue.get())
            while not error_queue.empty():
                error_messages.extend(error_queue.get())
            if output_files:
                # 创建内存中的ZIP文件
                memory_file = io.BytesIO()
                with zipfile.ZipFile(memory_file, 'w', zipfile.ZIP_DEFLATED) as zf:
                    # 遍历 base_output_dir 下的所有文件和子文件夹
                    for root, dirs, files in os.walk(base_output_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            # 计算在zip文件中的相对路径
                            arcname = os.path.relpath(file_path, base_output_dir)
                            # 将文件添加到zip
                            with open(file_path, 'rb') as f:
                                zf.writestr(arcname, f.read())
                           
                memory_file.seek(0)
                # 使用创建文件夹时的时间戳
                folder_timestamp = os.path.basename(base_output_dir).split('_')[-1]
                
                return send_file(
                    memory_file,
                    mimetype='application/zip',
                    as_attachment=True,
                    download_name=f'batch_query_results_{folder_timestamp}.zip'
                )            
            if error_messages:
                error_html = '<br>'.join(error_messages).replace('\n', '<br>')
                return render_template('batch_query.html', message=error_html, db_username=db_username, db_password=db_password, db_dsn=db_dsn)

            else: 
                return render_template('batch_query.html', 
                                    message='完成查询', 
                                    db_username=db_username, 
                                    db_password=db_password, 
                                    db_dsn=db_dsn)

        except Exception as e:
            return render_template('batch_query.html', message=f'执行查询时出错: {str(e)}')

@app.route('/query_db', methods=['POST'])
def query_db():
    query = request.form['query']
    group_columns = request.form.getlist('group_columns[]')
    db_username = request.form['db_username']
    db_password = request.form['db_password']
    db_dsn = request.form['db_dsn']
    file_prefix = request.form.get('file_prefix', '').strip()
    file_suffix = request.form.get('file_suffix', '').strip()
    num_processes = int(request.form.get('num_processes', cpu_count()))  # 默认使用CPU核心数
    
    try:
        connection_params = {
            'user': db_username,
            'password': db_password,
            'dsn': db_dsn
        }
        
        with connect_to_oracle(**connection_params) as connection:  
            output_dir = os.path.join(app.config['OUTPUT_FOLDER'], 'db_query_results')  
            os.makedirs(output_dir, exist_ok=True)  
            
            # 首先获取所有唯一的组合
            group_query = f"SELECT DISTINCT {', '.join(group_columns)} FROM ({query})"
            df_groups = fetch_data(connection, group_query)
        
        # 使用多进程处理每个组
        with Pool(processes=num_processes) as pool:
            process_func = functools.partial(
                process_group, 
                connection_params, 
                query, 
                group_columns=group_columns, 
                output_dir=output_dir, 
                file_prefix=file_prefix, 
                file_suffix=file_suffix
            )
            results = pool.map(process_func, df_groups.to_dict('records'))
        
        # 合并所有结果
        output_files = [item for sublist in results for item in sublist]
        
        # 处理结果...
        # (The rest of your code for creating ZIP file and returning response)

    except Exception as e:  
        app.logger.error(f"An error occurred: {str(e)}")  # 记录错误日志  
        return jsonify({'error': 'An error occurred while processing your request.'}), 500
    
def fetch_data(connection, query, params=None):
    """执行查询并返回DataFrame结果"""
    try:
        return pd.read_sql(query, connection, params=params)
    except Exception as e:
        logging.error(f"获取数据时出错: {str(e)}")
        raise

def process_group(connection_params, query, group_values, group_columns=None, output_dir=None, file_prefix='', file_suffix=''):
    """处理单个数据组并生成Excel文件"""
    try:
        # 构建WHERE子句
        where_conditions = []
        params = {}
        for i, col in enumerate(group_columns):
            param_name = f"param_{i}"
            where_conditions.append(f"{col} = :{param_name}")
            params[param_name] = group_values[col]
        
        where_clause = " AND ".join(where_conditions)
        filtered_query = f"{query} WHERE {where_clause}"
        
        # 执行查询获取数据
        with connect_to_oracle(**connection_params) as connection:
            df = fetch_data(connection, filtered_query, params)
        
        if df.empty:
            return []
        
        # 生成文件名
        group_values_str = "_".join(str(v) for v in group_values.values())
        filename = f"{file_prefix}_{group_values_str}_{file_suffix}.xlsx".strip("_")
        file_path = os.path.join(output_dir, filename)
        
        # 保存到Excel
        df.to_excel(file_path, index=False)
        return [(file_path, filename)]
        
    except Exception as e:
        logging.error(f"处理数据组时出错: {str(e)}")
        return []

def process_data_parallel(data, num_processes, process_func):
    """并行处理数据"""
    with Pool(processes=num_processes) as pool:
        results = pool.map(process_func, data)
    return results

def split_dataframe(df, split_columns):
    output_files = []
    for _, group in df.groupby(split_columns):
        file_name = '_'.join([str(val) for val in group[split_columns].iloc[0]]) + '.xlsx'
        file_path = os.path.join(app.config['OUTPUT_FOLDER'], file_name)
        group.to_excel(file_path, index=False)
        output_files.append((file_path, file_name))
    return output_files

# 修改 list_sql_templates 函数以使用新的 SQL_TEMPLATES_FOLDER
def list_sql_templates(template_type='rule'):
    """列出指定类型的SQL模板文件"""
    try:
        # 根据模板类型确定目录
        template_dirs = {
            'rule': os.path.join('templates', 'rule'),
            'manual': os.path.join('templates', 'manual'),
            'excel': os.path.join('templates', 'excel')
        }
        
        template_dir = template_dirs.get(template_type, os.path.join('templates', 'rule'))
        
        # 确保目录存在
        os.makedirs(template_dir, exist_ok=True)
        
        # 获取目录中的所有.sql文件
        templates = []
        if os.path.exists(template_dir):
            templates = [f for f in os.listdir(template_dir) if f.endswith('.sql')]
            templates.sort()  # 按字母顺序排序
        
        return templates
    except Exception as e:
        app.logger.error(f"获取模板列表失败: {str(e)}")
        return []

@app.route('/split_excel', methods=['POST'])
def split_excel():
    if 'file' not in request.files:
        flash('没有选择文件')
        return redirect(url_for('excel_splitter'))
        
    file = request.files['file']
    if file.filename == '':
        flash('没有选择文件')
        return redirect(url_for('excel_splitter'))
        
    if file and allowed_file(file.filename):
        try:
            # 保存上传的文件
            filename = secure_filename(file.filename)
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(file_path)
            
            # 获取分组列名
            group_column = request.form['group_column']
            
            # 生成时间戳文件夹
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_subfolder = os.path.join(app.config['OUTPUT_FOLDER'], f'split_excel_{timestamp}')
            os.makedirs(output_subfolder, exist_ok=True)
            
            # 调用 excel_splitter.py 中的 split_excel 函数
            output_files = split_excel(
                input_file=file_path,
                output_dir=output_subfolder,
                group_column=group_column,
                original_filename=filename
            )
            
            # 检查是否返回了错误消息
            if isinstance(output_files, str):
                flash(output_files)
                return redirect(url_for('excel_splitter'))
            
            # 创建 ZIP 文件
            memory_file = io.BytesIO()
            with zipfile.ZipFile(memory_file, 'w') as zf:
                for file_path, file_name in output_files:
                    zf.write(file_path, file_name)
            memory_file.seek(0)
            
            # 添加成功消息
            flash(f'文件拆分成功！共生成 {len(output_files)} 个文件')
            flash(f'文件保存路径: {output_subfolder}')
            
            return send_file(
                memory_file,
                mimetype='application/zip',
                as_attachment=True,
                download_name=f'split_excel_results_{timestamp}.zip'
            )
            
        except Exception as e:
            flash(f'拆分Excel时出错: {str(e)}')
            return redirect(url_for('excel_splitter'))
    else:
        flash('不允许的文件型')
        return redirect(url_for('excel_splitter'))

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in {'xlsx', 'xls'}

@app.route('/template_variables', methods=['POST'])
def template_variables():
    """获取模板变量"""
    try:
        template = request.form.get('template', '')
        template_type = request.form.get('template_type', 'rule')
        
        if not template:
            return render_template('sql_query.html',
                                templates=list_sql_templates(template_type),
                                template_type=template_type,
                                variables=[])
            
        # 获取所有模板
        templates = list_sql_templates(template_type)
        
        # 根据模板类型选择目录
        template_dirs = {
            'rule': os.path.join('templates', 'rule'),
            'manual': os.path.join('templates', 'manual'),
            'excel': os.path.join('templates', 'excel')
        }
        
        template_dir = template_dirs.get(template_type)
        if not template_dir:
            return render_template('sql_query.html',
                                templates=templates,
                                template_type=template_type,
                                variables=[])
            
        # 读取模板文件
        template_path = os.path.join(template_dir, template)
        if not os.path.exists(template_path):
            return render_template('sql_query.html',
                                templates=templates,
                                template_type=template_type,
                                variables=[])
            
        # 只在手动模式下显示变量输入表单
        variables = []
        if template_type == 'manual':
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 使用正则表达式匹配 {variable} 格式的变量
                variables = re.findall(r'\{(\w+)\}', content)
                variables = list(set(variables))  # 去重
        if template_type == 'rule':
            with get_connection(pool) as conn:
                query = """
                SELECT 
                ID,
                序号,
                行为认定,
                适用范围,
                规则名称,
                城市,
                规则来源,
                规则内涵
                FROM 飞检规则知识库
                ORDER BY ID
                """

                df = execute_rules_query(conn, query)
                variables = df['ID'].tolist()
                # 将DataFrame转换为字典列表
                rules_data = df.to_dict('records')
                
                return render_template('sql_query.html', 
                          templates=templates,
                          selected_template=template,
                          template_type=template_type,
                          variables=variables,
                          rules=rules_data)  # 添加rules数据传递到模板
        elif template_type == 'excel':
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 使用正则表达式匹配 {variable} 格式的变量
                variables = re.findall(r'\{(\w+)\}', content)
                variables = list(set(variables))  # 去重
            
        return render_template('sql_query.html', 
                          templates=templates,
                          selected_template=template,
                          template_type=template_type,
                          variables=variables)
    except Exception as e:
        logging.error(f"Error in template_variables: {str(e)}")
        return render_template('sql_query.html',
                             templates=list_sql_templates(template_type),
                             template_type=template_type,
                             variables=[])

@app.route('/generate_sql', methods=['POST'])
def generate_sql():
    try:
        template_name = request.form.get('template')
        template_type = request.form.get('template_type', 'rule')
        if not template_name:
            flash('请选择模板文件')
            return redirect(url_for('sql_generator'))
            
        requirements = request.form.to_dict(flat=False)  # 获取所有输入的要求
        
        # 根据模板类型选择目录
        template_dirs = {
            'rule': os.path.join('templates', 'rule'),
            'manual': os.path.join('templates', 'manual'),
            'excel': os.path.join('templates', 'excel')
        }
        
        template_dir = template_dirs.get(template_type)
        if not template_dir:
            flash('无效的模板类型')
            return redirect(url_for('sql_generator'))
        
        # 读取模板文件
        template_path = os.path.join(template_dir, template_name)
        if not os.path.exists(template_path):
            flash('模板文件不存在')
            return redirect(url_for('sql_generator'))
            
        with open(template_path, 'r', encoding='utf-8') as file:
            template = file.read()
        
        # 根据要求生成SQL
        sql_statements = generate_sql_from_template(template, requirements)
        
        # 保存 SQL 到 output 文件夹
        generated_files = save_sql_to_file(sql_statements, template_name, requirements)
        
        message = "SQL 生成成功！"
        # 将生成的SQL内容传递给模板
        generated_sql = sql_statements[0] if sql_statements else ""
        
        # 获取所有模板
        templates = list_sql_templates(template_type)
        
        return render_template('sql_query.html', 
                             templates=templates,
                             message=message, 
                             generated_files=generated_files, 
                             selected_template=template_name,
                             template_type=template_type,
                             generated_sql=generated_sql, 
                             variables=requirements.keys())
    except Exception as e:
        flash(str(e))
        return redirect(url_for('sql_generator'))


@app.route('/upload_template', methods=['POST'])
def upload_template():
    if 'template_file' not in request.files:
        flash('No file part')
        return redirect(url_for('sql_generator'))
    file = request.files['template_file']
    if file.filename == '':
        flash('No selected file')
        return redirect(url_for('sql_generator'))
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['SQL_TEMPLATES_FOLDER'], filename)
        file.save(file_path)
        flash(f'File {filename} uploaded successfully')
    else:
        flash('Invalid file type. Please upload a .sql file.')
    return redirect(url_for('sql_generator'))

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in {'sql'}

@app.route('/batch_generate', methods=['POST'])
def batch_generate():
    try:
        if 'excel_file' not in request.files:
            flash('请选择Excel文件')
            return redirect(url_for('sql_generator'))
        
        file = request.files['excel_file']
        if not file or file.filename == '':
            flash('未选文件')
            return redirect(url_for('sql_generator'))
            
        if not file.filename.endswith(('.xlsx', '.xls')):
            flash('请上传正确的Excel文件格式(.xlsx或.xls)')
            return redirect(url_for('sql_generator'))
            
        template_name = request.form.get('template')
        action = request.form.get('action')
        
        # 读取数据
        df = pd.read_excel(file)
        template_path = os.path.join(app.config['SQL_TEMPLATES_FOLDER'], template_name)
        
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
            
        # 获取模板变
        template_variables = get_template_variables(template_name)
        
        # 检查必要的列
        missing_columns = [var for var in template_variables if var not in df.columns]
        if missing_columns:
            flash(f'Excel 文件缺少以下列: {", ".join(missing_columns)}')
            return redirect(url_for('sql_generator'))
        
        # 生成时间戳
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if action == '合成一个SQL':
            return generate_combined_sql(df, template_content, file.filename, timestamp, template_name)
        else:
            return generate_separate_sql(df, template_content, file.filename, timestamp, template_name)
            
    except Exception as e:
        flash(f'生成SQL时出错: {str(e)}')
        return redirect(url_for('sql_generator'))

def generate_combined_sql(df: pd.DataFrame, template: str, excel_filename: str, timestamp: str, template_name: str):
    """生成合并的SQL文件"""
    sql_statements = []
    for _, row in df.iterrows():
        requirements = row.to_dict()
        sql = generate_sql_from_template(template, requirements)[0]
        sql_statements.append(sql)
    
    combined_sql = ';\n\n'.join(sql_statements) + ';'
    
    # 保存生成的SQL
    output_filename = f'batch_output_{os.path.splitext(excel_filename)[0]}_{timestamp}.sql'
    output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(combined_sql)
    
    # 创建ZIP文件
    memory_file = io.BytesIO()
    with zipfile.ZipFile(memory_file, 'w') as zf:
        zf.write(output_path, output_filename)
    memory_file.seek(0)
    
    return send_file(
        memory_file,
        mimetype='application/zip',
        as_attachment=True,
        download_name=f'batch_sql_{timestamp}.zip'
    )

def generate_separate_sql(df: pd.DataFrame, template: str, excel_filename: str, timestamp: str, template_name: str):
    """生成单独的SQL文件"""
    # 创建输出目录
    output_folder = os.path.join(
        app.config['OUTPUT_FOLDER'], 
        f'batch_{os.path.splitext(excel_filename)[0]}_{timestamp}'
    )
    os.makedirs(output_folder, exist_ok=True)
    
    template_prefix = os.path.splitext(template_name)[0]
    template_variables = get_template_variables(template_name)
    filename_columns = request.form.get('filename_columns', '').split(',')
    
    generated_files = []
    for index, row in df.iterrows():
        # 生成SQL
        requirements = row.to_dict()
        sql = generate_sql_from_template(template, requirements)[0]
        
        # 生成文件名
        variable_names = "_".join([
            str(requirements.get(var, '')).strip() 
            for var in template_variables 
            if str(requirements.get(var, '')).strip()
        ])
        
        # 添加户指定的到文件名
        additional_name_parts = "_".join([
            str(row.get(col, '')).strip() 
            for col in filename_columns 
            if col.strip() and col.strip() in df.columns
        ])
        
        # 组合文名
        if additional_name_parts:
            variable_names = f"{additional_name_parts}_{variable_names}"
        
        # 处理文件名
        sanitized_variable_names = sanitize_filename(variable_names)
        if len(sanitized_variable_names) > 100:
            sanitized_variable_names = sanitized_variable_names[:100]
        
        # 添加Excel行号
        excel_row_number = index + 2
        filename = f'{excel_row_number:03d}_{template_prefix}_{sanitized_variable_names}.sql'
        
        # 保存文件
        file_path = os.path.join(output_folder, filename)
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(sql)
            generated_files.append(filename)
        except OSError as e:
            flash(f'Error saving file {filename}: {str(e)}')
            continue
    
    # 创建ZIP文件
    memory_file = io.BytesIO()
    with zipfile.ZipFile(memory_file, 'w') as zf:
        for filename in generated_files:
            zf.write(os.path.join(output_folder, filename), filename)
    memory_file.seek(0)
    
    return send_file(
        memory_file,
        mimetype='application/zip',
        as_attachment=True,
        download_name=f'batch_separate_sql_{timestamp}.zip'
    )

def generate_sql_from_template(template, requirements):
    for variable in re.findall(r'\{(.*?)\}', template):
        # 获取对应的输入值
        value = requirements.get(variable, [""])[0] if isinstance(requirements.get(variable), list) else requirements.get(variable, "")
        template = template.replace(f'{{{variable}}}', str(value).strip())
    
    return [template]

def sanitize_filename(filename):
    # 使用 NFKC 标准化 Unicode 字符
    filename = unicodedata.normalize('NFKC', filename)
    # 移除不允许的字符，保留中文、字母、数字、下划线、连字符和点
    filename = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9_\-\.]', '_', filename)
    # 确保文件名不超过 255 字符
    return filename[:255]

def save_sql_to_file(sql_statements, template_name, requirements):
    os.makedirs('output', exist_ok=True)
    generated_files = []
    
    template_prefix = os.path.splitext(template_name)[0]
    
    variable_names = "_".join([str(req[0]).strip() for req in requirements.values() if str(req[0]).strip()])
    
    base_filename = f'{variable_names}' if variable_names else template_prefix
    
    for i, sql in enumerate(sql_statements):
        filename = f'{i + 1}.sql' if len(sql_statements) > 1 else f'{base_filename}.sql'
        with open(f'output/{filename}', 'w', encoding='utf-8') as f:
            f.write(sql)
        generated_files.append(filename)
    
    return generated_files

@app.route('/system_rules')
def system_rules():
    # 示例数据，实际应从数据库获取
    rule_types = []
    rule_levels = []
    medical_types = []
    rules = []
    
    return render_template(
        'system_rules.html',
        rule_types=rule_types,
        rule_levels=rule_levels,
        medical_types=medical_types,
        rules=rules
    )

@app.route('/add_to_rules', methods=['POST'])
def add_to_rules():
    try:
        data = request.json
        sql = data.get('sql')
        template = data.get('template')
        # 处理添加规则的逻辑
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/add_batch_to_rules', methods=['POST'])
def add_batch_to_rules():
    try:
        file = request.files['excel_file']
        template = request.form.get('template')
        filename_columns = request.form.get('filename_columns')
        # 处理批量添加规则的逻
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/excel_delete')
def excel_delete():
    return render_template('excel_delete.html')

@app.route('/process_excel_delete', methods=['POST'])
def process_excel_delete():
    try:
        # 检查是否上传了文件
        has_main_file = request.files.get('main_file') and request.files['main_file'].filename
        has_folder_files = request.files.getlist('folder_files[]') and any(f.filename for f in request.files.getlist('folder_files[]'))
        
        if not has_main_file and not has_folder_files:
            flash('请选择要处理的Excel文件')
            return redirect(url_for('excel_delete'))
        
        if 'delete_file' not in request.files or not request.files['delete_file'].filename:
            flash('请选择包含要删除内容的Excel文件')
            return redirect(url_for('excel_delete'))
            
        column_name = request.form.get('column_name')
        if not column_name:
            flash('请输入列名')
            return redirect(url_for('excel_delete'))

        # 保存删除内容文件
        delete_file = request.files['delete_file']
        delete_filename = secure_filename(delete_file.filename)
        delete_file_path = os.path.join(app.config['UPLOAD_FOLDER'], f'delete_{delete_filename}')
        delete_file.save(delete_file_path)

        # 处理单个文件或多个文件
        output_files = []
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(app.config['OUTPUT_FOLDER'], f'excel_delete_{timestamp}')
        os.makedirs(output_dir, exist_ok=True)

        # 处理单个文件
        if has_main_file:
            main_file = request.files['main_file']
            output_file = process_single_delete(main_file, column_name, delete_file_path, output_dir)
            if output_file:
                output_files.append(output_file)
        
        # 处理多个文件
        if has_folder_files:
            folder_files = [f for f in request.files.getlist('folder_files[]') if f.filename]
            for file in folder_files:
                output_file = process_single_delete(file, column_name, delete_file_path, output_dir)
                if output_file:
                    output_files.append(output_file)

        if not output_files:
            flash('没有处理任何文件')
            return redirect(url_for('excel_delete'))

        # 创建ZIP文件
        memory_file = io.BytesIO()
        with zipfile.ZipFile(memory_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            for file_path, file_name in output_files:
                # 使用原始文件名（包含中文）作为zip内的文件名
                zf.write(file_path, file_name)
        memory_file.seek(0)

        # 理临时文件
        os.remove(delete_file_path)
        for file_path, _ in output_files:
            try:
                os.remove(file_path)
            except:
                pass

        flash(f'成功处理 {len(output_files)} 个文件')
        
        return send_file(
            memory_file,
            mimetype='application/zip',
            as_attachment=True,
            download_name=f'excel_delete_results_{timestamp}.zip'
        )

    except Exception as e:
        logging.error(f"处理文件时出错: {str(e)}", exc_info=True)
        flash(f'处理文件时出错: {str(e)}')
        return redirect(url_for('excel_delete'))

def process_single_delete(file: FileStorage, column_name: str, delete_file_path: str, output_dir: str) -> Optional[Tuple[str, str]]:
    """处理单个文件的行删除操作"""
    try:
        # 获取原始文件名（保留中文）
        original_filename = file.filename
        # 使用 sanitize_filename 处理文件名
        safe_filename = sanitize_filename(original_filename)
        file_path = os.path.join(output_dir, safe_filename)
        file.save(file_path)

        # 读取Excel文件
        df = pd.read_excel(file_path)
        delete_df = pd.read_excel(delete_file_path)
        
        # 验列名存在
        if column_name not in df.columns or column_name not in delete_df.columns:
            raise ValueError(f'列名 {column_name} 在文件中不存在')

        # 执行删除操作
        values_to_delete = delete_df[column_name].tolist()
        df_filtered = df[~df[column_name].isin(values_to_delete)]
        
        # 保存结果，使用 sanitize_filename 处理文件名
        output_filename = f'processed_{original_filename}'
        safe_output_filename = sanitize_filename(output_filename)
        output_path = os.path.join(output_dir, safe_output_filename)
        df_filtered.to_excel(output_path, index=False)
        
        # 返回实际保存路径和期望在zip中显示的文件名（使用原始中文名）
        return (output_path, output_filename)
    
    except Exception as e:
        flash(f'处理文件 {original_filename} 时错: {str(e)}')
        return None


@app.route('/excel_compare', methods=['GET', 'POST'])
def excel_compare():
    if request.method == 'GET':
        return render_template('excel_compare.html')
    if request.method == 'POST':
        try:
            # 首先获取比对模
            compare_mode = request.form.get('compare_mode')            
            if 'file1' not in request.files or 'file2' not in request.files:
                return render_template('excel_compare.html', error="请选择两个文件进行比对")
            file1 = request.files['file1']
            file2 = request.files['file2']
            
            if file1.filename == '' or file2.filename == '':
                return render_template('excel_compare.html', error="请选择文件")
            
            # 生成会话ID和临时目录
            session_id = str(uuid.uuid4())
            temp_dir = os.path.join(app.config['OUTPUT_FOLDER'], f'temp_{session_id}')
            os.makedirs(temp_dir, exist_ok=True)  # Ensure the directory is created
            
            # 保存DataFrame到临时文件
            df1 = pd.read_excel(file1)
            df2 = pd.read_excel(file2)
            df1.to_pickle(os.path.join(temp_dir, 'df1.pkl'))
            df2.to_pickle(os.path.join(temp_dir, 'df2.pkl'))            
            # 根据比对模式选择比对函数
            if compare_mode == 'rows':
                comparison_result, has_differences = compare_excel_rows(df1, df2, session_id)
            else:
                comparison_result, has_differences = compare_excel_columns(df1, df2, session_id)
            
            # 存储会话信息
            session['temp_dir'] = temp_dir  # Store the current session's temp_dir
            session['session_id'] = session_id
            session['compare_mode'] = compare_mode  # 也存储比对模式
            session.modified = True
            
            return render_template('excel_compare.html',
                                comparison_result=comparison_result,
                                has_differences=has_differences,
                                session_id=session_id)
                                
        except Exception as e:
            logging.error(f"Excel comparison error: {str(e)}", exc_info=True)
            return render_template('excel_compare.html', error=str(e))

def compare_excel_columns(df1, df2, session_id):
    """比较两个DataFrame的列差异"""
    differences = []
    has_differences = False
    diff_data = []  # 存储差异数据

    # 获取共同的列
    common_columns = list(set(df1.columns).intersection(set(df2.columns)))
    # 确保使用当前会话的临时目录
    temp_dir = os.path.join(app.config['OUTPUT_FOLDER'], f'temp_{session_id}')
    
    # 获取两个DataFrame的列名
    columns1 = set(df1.columns)
    columns2 = set(df2.columns)
    
    # 检查是否有不同的列
    if columns1 != columns2:
        only_in_df1 = columns1 - columns2
        only_in_df2 = columns2 - columns1
        if only_in_df1:
            differences.append(f"仅在文件1中存在的列: {', '.join(only_in_df1)}")
        if only_in_df2:
            differences.append(f"在文件2中存在的列: {', '.join(only_in_df2)}")
        # 只比较两个文件都存在的列
    common_columns = columns1.intersection(columns2)
        # 确保两个DataFrame的行数相同再进行比较
    min_rows = min(len(df1), len(df2))
    df1_trimmed = df1.iloc[:min_rows].reset_index(drop=True)
    df2_trimmed = df2.iloc[:min_rows].reset_index(drop=True)
    # 逐列比较内容
    for column in common_columns:
        try:
            # 将列转换为字符串并重置索引
            series1 = df1_trimmed[column].astype(str).reset_index(drop=True)
            series2 = df2_trimmed[column].astype(str).reset_index(drop=True)
            
            # 比较并计算差异
            mask = series1 != series2
            if mask.any():
                has_differences = True
                diff_count = mask.sum()
                # 获取具体的差异位置和值
                diff_indices = mask[mask].index
                diff_details = []
                for idx in diff_indices[:5]:  # 只显示前5个差异
                    val1 = series1.iloc[idx]
                    val2 = series2.iloc[idx]
                    diff_details.append(f"第{idx+1}行: 文件1={val1}, 文件2={val2}")
                
                diff_summary = f"列 '{column}' 有 {diff_count} 处差异"
                if diff_details:
                    if len(diff_details) < diff_count:
                        diff_summary += f"\n前5个差异:\n" + "\n".join(diff_details) + "\n..."
                    else:
                        diff_summary += f"\n具体差异:\n" + "\n".join(diff_details)
                differences.append(diff_summary)
        except Exception as e:
            differences.append(f"比较列 '{column}' 时出错: {str(e)}")
    
    # 保存差异数据到session
    if has_differences:
        diff_df = pd.DataFrame(diff_data)
        diff_df.to_pickle(os.path.join(temp_dir, 'diff_results.pkl'))
    
    if not differences:
        return "两个文件完全相同"
        
    summary = f"总共发现 {len(differences)} 列数据不同<br><br>"
    return (summary + "<br><br>".join(differences), has_differences)

def compare_excel_rows(df1, df2, session_id):
    """比较两个Excel文件，找出任何值不同的行"""
    differences = []
    has_differences = False
    diff_rows = []  # 存储差异行数据
    
    # 获取共同的列
    common_columns = list(set(df1.columns).intersection(set(df2.columns)))
      # 确保使用当前会话的临时目录
    temp_dir = os.path.join(app.config['OUTPUT_FOLDER'], f'temp_{session_id}')
 
    # 确保两个DataFrame使用相同列
    df1 = df1[common_columns].reset_index(drop=True)
    df2 = df2[common_columns].reset_index(drop=True)
    
    # 逐行比较
    min_rows = min(len(df1), len(df2))
    for idx in range(min_rows):
        row1 = df1.iloc[idx]
        row2 = df2.iloc[idx]
        
        # 检查这一行是否有任何差异
        if not (row1.astype(str) == row2.astype(str)).all():
            has_differences = True
            
            # 保存差异行数据
            row1_dict = row1.to_dict()
            row2_dict = row2.to_dict()
            row1_dict['行号'] = idx + 1
            row1_dict['来源'] = '文件1'
            row2_dict['行号'] = idx + 1
            row2_dict['来源'] = '文件2'
            diff_rows.extend([row1_dict, row2_dict])
            
            # 生成差异信息显示
            row_info = f"第 {idx + 1} 数据不同:<br>"
            row_info += "文件1的值: "
            row_info += ", ".join([f"{col}={row1[col]}" for col in common_columns])
            row_info += "<br>文件2的值: "
            row_info += ", ".join([f"{col}={row2[col]}" for col in common_columns])
            differences.append(row_info)
    
    # 保存差异数据到session
    if has_differences:
        diff_df = pd.DataFrame(diff_rows)
        # 调整列顺序
        cols = ['行号', '来源'] + [col for col in diff_df.columns if col not in ['行号', '来源']]
        diff_df = diff_df[cols]
        diff_df.to_pickle(os.path.join(temp_dir, 'diff_results.pkl'))
    
    if not differences:
        return "两个文件完全相同，未发现差异", False
    
    summary = f"总共发现 {len(differences)} 行数据不同<br><br>"
    return (summary + "<br><br>".join(differences), has_differences)

@app.route('/export_differences', methods=['POST'])
def export_differences():
    try:
        session_id = request.form.get('session_id')
        temp_dir = session.get('temp_dir')
        
        if not all([session_id, temp_dir]):
            return "会话已过期，请重新比对文件", 400
        
        # 读取保存的差异结果
        diff_results_path = os.path.join(temp_dir, 'diff_results.pkl')
        if not os.path.exists(diff_results_path):
            return "未找到差异数据，请重新比对文件", 400
            
        diff_df = pd.read_pickle(diff_results_path)
        
        # 生成输出文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f'差异报告_{timestamp}.xlsx'
        
        # 保存到Excel
        diff_df.to_excel(output_filename, index=False)
        
        return send_file(
            output_filename,
            as_attachment=True,
            download_name=output_filename
        )
        
    except Exception as e:
        return f"导出差异报告时出错: {str(e)}", 500
 #   finally:
 #       # 清理临时文件
 #       if 'output_filename' in locals() and os.path.exists(output_filename):
 #           os.remove(output_filename)

@app.route('/data_validator', methods=['GET', 'POST'])
def data_validator():
    if request.method == 'GET':
        return render_template('data_validator.html')
    
    if request.method == 'POST':
        try:
            if 'file' not in request.files:
                return render_template('data_validator.html', error="请选择文件")
            
            file = request.files['file']
            if file.filename == '':
                return render_template('data_validator.html', error="请选择文件")
            
            # 获取验证规则选项
            check_empty = request.form.get('check_empty') == 'on'
            check_format = request.form.get('check_format') == 'on'
            check_range = request.form.get('check_range') == 'on'
            
            # 读取Excel文件
            df = pd.read_excel(file)
            
            # 执行数据验证
            validation_result = validate_data(df, check_empty, check_format, check_range)
            
            return render_template('data_validator.html', validation_result=validation_result)
            
        except Exception as e:
            return render_template('data_validator.html', error=f'处理文件时出错: {str(e)}')

def validate_data(df, check_empty, check_format, check_range):
    """执行数据验证并返回结果"""
    results = []
    
    if check_empty:
        # 检查空值
        empty_counts = df.isnull().sum()
        if empty_counts.any():
            for column, count in empty_counts[empty_counts > 0].items():
                results.append(f"列 '{column}' 有 {count} 个空值")
    
    if check_format:
        # 检查数据格式
        for column in df.columns:
            try:
                if df[column].dtype == 'object':
                    # 尝试转换为数值类型
                    pd.to_numeric(df[column], errors='raise')
                    results.append(f"列 '{column}' 包含非数值数据")
            except:
                continue
    
    if check_range:
        # 检查数值范围
        for column in df.select_dtypes(include=['int64', 'float64']).columns:
            min_val = df[column].min()
            max_val = df[column].max()
            if min_val < 0:  # 示例：检查负值
                results.append(f"列 '{column}' 包含负值，最小值为 {min_val}")
    
    if not results:
        return "数据验证通过，未发现问题"
    
    return "<br>".join(results)
    
def find_duplicate_files(directory):
    """查找指定目录中的重复文件"""
    files_hash = {}
    duplicates = {}  # 使用字典来存储所有具有相同哈希值的文件

    for dirpath, _, filenames in os.walk(directory):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            file_hash = hash_file(file_path)

            if file_hash in files_hash:
                # 如果这个哈希值已经存在，将文件路径添加到对应的列表中
                if file_hash not in duplicates:
                    duplicates[file_hash] = [files_hash[file_hash]]
                duplicates[file_hash].append(file_path)
            else:
                files_hash[file_hash] = file_path

    # 转换格式为列表每个元素包含所有重复的文件路径
    return [paths for paths in duplicates.values()]

def hash_file(file_path):
    """生成文件的哈希值"""
    hasher = hashlib.md5()
    with open(file_path, 'rb') as f:
        while chunk := f.read(8192):
            hasher.update(chunk)
    return hasher.hexdigest()

def process_data_parallel(data, num_processes):
    """
    使用进程池并行处理数据
    
    Args:
        data: 要处理的数据
        num_processes: 进程数量
    
    Returns:
        list: 处理结果列表
    """
    def process_func(item):
        try:
            return item  # 在这里添加实际的处理逻辑
        except Exception as e:
            logging.error(f"处理数据时出错: {str(e)}")
            return None

    with Pool(processes=num_processes) as pool:
        results = pool.map(process_func, data)
    return results

@app.route('/find_duplicates', methods=['GET', 'POST'])
def find_duplicates():
    if request.method == 'POST':
        directory = request.form.get('directory')
        if not directory or not os.path.exists(directory):
            flash('无效的目录')
            return render_template('find_duplicates.html', 
                                 duplicate_groups=[], 
                                 duplicate_file_path=None,
                                 message='无效的目录')

        duplicate_groups = find_duplicate_files(directory)

        if duplicate_groups:
            # 将重复文件信息保存到 Excel
            # 创建一个包含所有重复文件信息的列表
            excel_data = []
            for group_index, group in enumerate(duplicate_groups, 1):
                for file_path in group:
                    excel_data.append({
                        '组号': group_index,
                        '文件路径': file_path
                    })
            
            df = pd.DataFrame(excel_data)
            output_path = os.path.join(app.config['OUTPUT_FOLDER'], 'duplicate_files.xlsx')
            df.to_excel(output_path, index=False)

            message = f'找到 {len(duplicate_groups)} 组重复文件'
            flash(message)
            return render_template('find_duplicates.html',
                                 duplicate_groups=duplicate_groups,
                                 duplicate_file_path=output_path,
                                 message=message,
                                 total_groups=len(duplicate_groups))
        else:
            message = '未找到重复文件'
            flash(message)
            return render_template('find_duplicates.html',
                                 duplicate_groups=[],
                                 duplicate_file_path=None,
                                 message=message,
                                 total_groups=0)

    return render_template('find_duplicates.html',
                         duplicate_groups=[],
                         duplicate_file_path=None,
                         message=None,
                         total_groups=0)

@app.route('/export_duplicates', methods=['POST'])
def export_duplicates():
    try:
        output_path = os.path.join(app.config['OUTPUT_FOLDER'], 'duplicate_files.xlsx')
        
        if not os.path.exists(output_path):
            flash('没有找到重复文件数据')
            return redirect(url_for('find_duplicates'))

        # 生成时间戳
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        download_name = f'重复文件清单_{timestamp}.xlsx'

        return send_file(
            output_path,
            as_attachment=True,
            download_name=download_name
        )

    except Exception as e:
        flash(f'导出文件时出错: {str(e)}')
        return redirect(url_for('find_duplicates'))

@app.route('/data_standardization', methods=['GET', 'POST'])
def data_standardization():
    """处理数据标准化请求"""
    # 在函数开始时就初始化配置
    config = ConfigParser()
    config.read('config.ini')

    if request.method == 'POST':
        try:
            # 处理数据标准化的逻辑
            if 'file' not in request.files:
                flash('请选择要标准化的文件')
                return redirect(url_for('data_standardization'))
            
            file = request.files['file']
            if file.filename == '':
                flash('未选择文件')
                return redirect(url_for('data_standardization'))

            # TODO: 添加实际的数据标准化处理逻辑
            flash('数据标准化处理完成！')
            
        except Exception as e:
            logging.error(f"数据标准化处理出错: {str(e)}")
            flash(f'处理出错: {str(e)}')
            
        return redirect(url_for('data_standardization'))

    # GET 请求返回模板
    return render_template(
        'data_standardization.html',
        config=config,
        db_username=config.get('database', 'username', fallback=''),
        db_password=config.get('database', 'password', fallback=''),
        db_dsn=config.get('database', 'dsn', fallback='')
    )

# 加新的路由于获取数据库表和字段信息
@app.route('/get_db_tables', methods=['POST'])
def get_db_tables():
    """获取数据库中的所有表"""
    try:
        data = request.get_json()
        username = data.get('db_username')
        password = data.get('db_password')
        dsn = data.get('db_dsn')
        
        if not all([username, password, dsn]):
            return jsonify({'success': False, 'error': '请提供完整的数据库连接信息'})
        
        # 连接数据库
        with connect_to_oracle(username, password, dsn) as connection:
            with connection.cursor() as cursor:
                # 查询所有表
                cursor.execute("""
                    SELECT table_name 
                    FROM all_tables 
                    WHERE owner = :owner   
                    ORDER BY table_name
                """, owner=username.upper())
                
                tables = [row[0] for row in cursor.fetchall()]
                
                return jsonify({
                    'success': True,
                    'tables': tables
                })
                
    except Exception as e:
        logging.error(f"获取表列表时出错: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/get_table_fields', methods=['POST'])
def get_table_fields():
    """获取指定表的字段信息"""
    try:
        data = request.get_json()
        username = data.get('db_username')
        password = data.get('db_password')
        dsn = data.get('db_dsn')
        table_name = data.get('table_name')
        
        if not all([username, password, dsn, table_name]):
            return jsonify({'success': False, 'error': '请提供完整的表信息'})
        
        # 连接数据库
        with connect_to_oracle(username, password, dsn) as connection:
            with connection.cursor() as cursor:
                # 查询表字段信息
                cursor.execute("""
                    SELECT 
                        column_name,
                        data_type,
                        nullable,
                        data_length,
                        data_precision,
                        data_scale,
                        column_id,
                        comments
                    FROM all_tab_columns a
                    LEFT JOIN all_col_comments b ON 
                        a.owner = b.owner AND 
                        a.table_name = b.table_name AND 
                        a.column_name = b.column_name
                    WHERE a.owner = :owner 
                    AND a.table_name = :table_name
                    ORDER BY column_id
                """, owner=username.upper(), table_name=table_name.upper())
                
                fields = []
                for row in cursor.fetchall():
                    data_type = row[1]
                    if row[4] is not None:  # 如果有精度
                        if row[5] is not None:  # 如果有小数位
                            data_type = f"{data_type}({row[4]},{row[5]})"
                        else:
                            data_type = f"{data_type}({row[4]})"
                    elif row[3] is not None:  # 如果有长度
                        data_type = f"{data_type}({row[3]})"
                    
                    fields.append({
                        'name': row[0],
                        'type': data_type,
                        'nullable': row[2] == 'Y',
                        'comment': row[7] or ''
                    })
                
                return jsonify({
                    'success': True,
                    'fields': fields
                })
                
    except Exception as e:
        logging.error(f"获取表字段时出错: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# 添获取数据库列表的由
@app.route('/get_databases', methods=['POST'])
def get_databases():
    try:
        db_username = request.form['db_username']
        db_password = request.form['db_password']
        db_dsn = request.form['db_dsn']
        
        # 连接数据库
        connection = oracledb.connect(user=db_username, password=db_password, dsn=db_dsn)
        cursor = connection.cursor()
        
        # 查询所有可访问的数据库
        cursor.execute("""
            SELECT DISTINCT owner 
            FROM all_tables 
            WHERE owner NOT IN ('SYS','SYSTEM','OUTLN','DIP','ORACLE_OCM','DBSNMP','APPQOSSYS','WMSYS','EXFSYS',
                              'CTXSYS','XDB','ANONYMOUS','XS$NULL','ORDDATA','SI_INFORMTN_SCHEMA','ORDPLUGINS',
                              'ORDSYS','MDSYS','OLAPSYS','MDDATA','SPATIAL_WFS_ADMIN_USR','SPATIAL_CSW_ADMIN_USR',
                              'SYSMAN','MGMT_VIEW','APEX_030200','FLOWS_FILES','APEX_PUBLIC_USER','OWBSYS',
                              'OWBSYS_AUDIT','SCOTT','AUDSYS','DBSFWUSER','DVSYS','GSMADMIN_INTERNAL','HR','LBACSYS','OJVMSYS')
            ORDER BY owner
        """)
        databases = [row[0] for row in cursor.fetchall()]
        
        cursor.close()
        connection.close()
        
        return jsonify({'success': True, 'databases': databases})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# 修改获取表列表的路由以支持指定数据库

# 获取 CPU 核心数
num_cores = cpu_count()

# 使用示例
def process_data(data, num_processes=None):
    if num_processes is None:
        num_processes = cpu_count()  # 默认使用所有可用核心
    
    with Pool(processes=num_processes) as pool:
        results = pool.map(process_func, data)
    return results
def process_func(item):
    """Process a single data item"""
    try:
        # Your specific processing logic here
        # This should match what you're trying to do with the data
        return item  # Replace with actual processing
    except Exception as e:
        logging.error(f"Error processing item: {str(e)}")
        return None
def process_batch(data, num_processes=None):
    """
    Process data in parallel using a process pool
    
    Args:
        data: Data to process
        num_processes: Number of processes to use (defaults to CPU count)
    """
    if num_processes is None:
        num_processes = cpu_count()
    
    def process_func(item):
        try:
            return item  # Add your processing logic here
        except Exception as e:
            logging.error(f"Error processing item: {str(e)}")
            return None
    
    with Pool(processes=num_processes) as pool:
        results = pool.map(process_func, data)
    return results

# Redis配置
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 0
REDIS_PASSWORD = None  # 如果需要密码，请在这里设置

# 创建Redis连接
try:
    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        db=REDIS_DB,
        password=REDIS_PASSWORD,
        decode_responses=True,  # 自动解码响应
        socket_timeout=5,  # 连接超时时间
        socket_connect_timeout=5,  # 连接建立超时时间
    )
except Exception as e:
    logging.error(f"Failed to connect to Redis: {str(e)}")
    redis_client = None

class Cache:
    def __init__(self, redis_client: Optional[redis.Redis] = None, default_timeout: int = 3600):
        self.redis = redis_client
        self.default_timeout = default_timeout

    def get(self, key: str) -> Any:
        """从缓存获取值"""
        if not self.redis:
            return None
        try:
            value = self.redis.get(key)
            if value:
                return pickle.loads(value)
            return None
        except Exception as e:
            logging.error(f"Cache get error: {str(e)}")
            return None

    def set(self, key: str, value: Any, timeout: Optional[int] = None) -> bool:
        """设置缓存值"""
        if not self.redis:
            return False
        try:
            timeout = timeout or self.default_timeout
            pickled_value = pickle.dumps(value)
            return self.redis.setex(key, timeout, pickled_value)
        except Exception as e:
            logging.error(f"Cache set error: {str(e)}")
            return False

    def delete(self, key: str) -> bool:
        """删除缓存值"""
        if not self.redis:
            return False
        try:
            return bool(self.redis.delete(key))
        except Exception as e:
            logging.error(f"Cache delete error: {str(e)}")
            return False

# 创建缓存实例
cache = Cache(redis_client)

# 性能优化装饰器
def cache_result(timeout: Optional[int] = None):
    """缓存函数结果的装饰器"""
    def decorator(f):
        @wraps(f)
        def wrapped(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{f.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取结果
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = f(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            return result
        return wrapped
    return decorator

@lru_cache(maxsize=128)
def get_template_variables(template_name: str) -> List[str]:
    """获取SQL模板中的变量（使用Python内置的LRU缓存）"""
    try:
        with open(f'templates/{template_name}', 'r', encoding='utf-8') as file:
            template = file.read()
        return list(set(re.findall(r'\{(\w+)\}', template)))
    except Exception as e:
        logging.error(f"Error getting template variables: {str(e)}")
        return []

def batch_process_with_progress(items: List[Any], process_func: callable, batch_size: int = 1000) -> List[Any]:
    """批量处理数据并显示进度"""
    results = []
    total = len(items)
    
    for i in range(0, total, batch_size):
        batch = items[i:i + batch_size]
        batch_results = process_func(batch)
        results.extend(batch_results)
        
        # 计算和记录进度
        progress = min(100, (i + batch_size) * 100 // total)
        logging.info(f"Processing progress: {progress}%")
    
    return results

class QueryOptimizer:
    @staticmethod
    def optimize_query(query: str) -> str:
        """优化SQL查询"""
        # 移除多余的空格
        query = ' '.join(query.split())
        
        # 添加查询优化提示
        if query.lower().startswith('select'):
            # 对于大结果集使用NO_CACHE提示
            if 'where' not in query.lower():
                query = query.replace('SELECT', 'SELECT /*+ NO_CACHE */', 1)
            # 对于小结果集使用CACHE提示
            else:
                query = query.replace('SELECT', 'SELECT /*+ CACHE */', 1)
        
        return query

    @staticmethod
    def add_pagination(query: str, page: int, page_size: int) -> str:
        """添加分页"""
        offset = (page - 1) * page_size
        return f"{query} OFFSET {offset} ROWS FETCH NEXT {page_size} ROWS ONLY"

# 更新数据库查询函数以使用优化器
@cache_result(timeout=3600)
def execute_optimized_query(connection: oracledb.Connection, query: str, params: Optional[Dict] = None,
                          page: Optional[int] = None, page_size: Optional[int] = None) -> pd.DataFrame:
    """执行优化后的查询"""
    try:
        # 优化查询
        optimized_query = QueryOptimizer.optimize_query(query)
        
        # 添加分页（如果需要）
        if page is not None and page_size is not None:
            optimized_query = QueryOptimizer.add_pagination(optimized_query, page, page_size)
        
        # 执行查询
        with connection.cursor() as cursor:
            if params:
                cursor.execute(optimized_query, params)
            else:
                cursor.execute(optimized_query)
            
            columns = [col[0] for col in cursor.description]
            rows = cursor.fetchall()
            
            return pd.DataFrame(rows, columns=columns)
    except Exception as e:
        logging.error(f"Error executing optimized query: {str(e)}\nQuery: {query}\nParams: {params}")
        raise


def process_group(connection_params, query, group_values, group_columns=None, output_dir=None, file_prefix='', file_suffix=''):
    """处理单个数据组并生成Excel文件"""
    try:
        # 构建WHERE子句
        where_conditions = []
        params = {}
        for i, col in enumerate(group_columns):
            param_name = f"param_{i}"
            where_conditions.append(f"{col} = :{param_name}")
            params[param_name] = group_values[col]
        
        where_clause = " AND ".join(where_conditions)
        filtered_query = f"{query} WHERE {where_clause}"
        
        # 执行查询获取数据
        with connect_to_oracle(**connection_params) as connection:
            df = fetch_data(connection, filtered_query, params)
        
        if df.empty:
            return []
        
        # 生成文件名
        group_values_str = "_".join(str(v) for v in group_values.values())
        filename = f"{file_prefix}_{group_values_str}_{file_suffix}.xlsx".strip("_")
        file_path = os.path.join(output_dir, filename)
        
        # 保存到Excel
        df.to_excel(file_path, index=False)
        return [(file_path, filename)]
        
    except Exception as e:
        logging.error(f"处理数据组时出错: {str(e)}")
        return []

def process_data_in_parallel(data, connection_params, query, group_columns=None, output_dir=None, num_processes=None):
    """并行处理数据"""
    if num_processes is None:
        num_processes = cpu_count()
    
    # 创建处理函数
    process_func = functools.partial(
        process_group,
        connection_params,
        query,
        group_columns=group_columns,
        output_dir=output_dir
    )
    
    # 使用进程池并行处理
    with Pool(processes=num_processes) as pool:
        results = pool.map(process_func, data)
    return results

@app.route('/test_db_connection', methods=['POST'])
def test_db_connection():
    data = request.get_json()
    username = data.get('db_username')
    password = data.get('db_password')
    dsn = data.get('db_dsn')
 

@app.route('/excel_to_sql')
def excel_to_sql():
    """Render the Excel to SQL conversion page"""
    return render_template('excel_to_sql.html')

@app.route('/process_excel_to_sql', methods=['POST'])
def process_excel_to_sql():
    """Process the Excel file and generate SQL files"""
    try:
        if 'file' not in request.files:
            flash('请选择Excel文件', 'error')
            return redirect(url_for('excel_to_sql'))
        
        file = request.files['file']
        if file.filename == '':
            flash('未选择文件', 'error')
            return redirect(url_for('excel_to_sql'))

        # 读取Excel文件
        try:
            df = pd.read_excel(file)
        except Exception as e:
            flash(f'读取Excel文件失败: {str(e)}', 'error')
            return redirect(url_for('excel_to_sql'))

        # 验证必需的列
        required_columns = ['规则名称']
        if 'SQL' in df.columns:
            sql_column = 'SQL'
        elif 'sql' in df.columns:
            sql_column = 'sql'
        else:
            flash('Excel文件必须包含"SQL"或"sql"列', 'error')
            return redirect(url_for('excel_to_sql'))

        if '规则名称' not in df.columns:
            flash('Excel文件必须包含"规则名称"列', 'error')
            return redirect(url_for('excel_to_sql'))

        # 创建内存中的ZIP文件
        memory_file = io.BytesIO()
        with zipfile.ZipFile(memory_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            # 处理每一行数据
            for index, row in df.iterrows():
                rule_name = str(row['规则名称']).strip().replace('/', '_').replace('\\', '_')
                rule_content = str(row[sql_column]).strip()
                rule_description = str(row.get('规则内涵', '')).strip()
                policy_basis = str(row.get('政策依据', '')).strip()

                # 构造SQL内容
                sql_content = f"-- 规则名称: {rule_name}\n"
                if rule_description and request.form.get('include_description'):
                    sql_content += f"-- 规则内涵: {rule_description}\n"
                if policy_basis and request.form.get('include_policy'):
                    sql_content += f"-- 政策依据: {policy_basis}\n"
                sql_content += rule_content

                # 添加到ZIP文件
                filename = f"{rule_name}.sql"
                zf.writestr(filename, sql_content)

        memory_file.seek(0)
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        return send_file(
            memory_file,
            mimetype='application/zip',
            as_attachment=True,
            download_name=f'sql_files_{timestamp}.zip'
        )

    except Exception as e:
        app.logger.error(f"处理Excel文件时出错: {str(e)}")
        flash(f'处理文件时出错: {str(e)}', 'error')
        return redirect(url_for('excel_to_sql'))

# 添加全局错误处理
@app.errorhandler(500)
def internal_server_error(e):
    app.logger.error(f"500 error: {str(e)}")
    app.logger.error(traceback.format_exc())
    return render_template('error.html', error=str(e)), 500

# 配置文件路径
CONFIG_FILE = 'config.ini'

def load_db_config():
    """从config.ini加载数据库配置"""
    try:
        config = configparser.ConfigParser()
        if os.path.exists(CONFIG_FILE):
            config.read(CONFIG_FILE, encoding='utf-8')
            if 'DATABASE' in config:
                return {
                    'db_username': config.get('DATABASE', 'username', fallback=''),
                    'db_password': config.get('DATABASE', 'password', fallback=''),
                    'db_dsn': config.get('DATABASE', 'dsn', fallback='')
                }
    except Exception as e:
        app.logger.error(f"加载配置文件失败: {str(e)}")
    return {'db_username': '', 'db_password': '', 'db_dsn': ''}

@app.route('/save_db_config', methods=['POST'])
def save_db_config():
    """保存数据库配置到config.ini"""
    try:
        new_config = request.get_json()
        
        # 验证必需的字段
        required_fields = ['db_username', 'db_password', 'db_dsn']
        if not all(field in new_config for field in required_fields):
            return jsonify({'success': False, 'message': '缺少必需的配置项'}), 400
        
        # 读取现有配置
        config = configparser.ConfigParser()
        if os.path.exists(CONFIG_FILE):
            config.read(CONFIG_FILE, encoding='utf-8')
        
        # 确保 DATABASE 部分存在
        if 'database' not in config:
            config['database'] = {}
        
        # 更新配置
        config['database']['username'] = new_config['db_username']
        config['database']['password'] = new_config['db_password']
        config['database']['dsn'] = new_config['db_dsn']
        
        # 保存配置
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            config.write(f)
        
        return jsonify({'success': True})
    
    except Exception as e:
        app.logger.error(f"保存配置文件失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

# 飞检规则知识库路由
@app.route('/rule_knowledge_base')
def rule_knowledge_base():
    """飞检规则知识库维护页面"""
    return render_template('rule_knowledge_base.html')

def execute_rules_query(conn, query: str, params: Optional[Dict] = None) -> pd.DataFrame:
    """执行规则查询"""
    try:
        with conn.cursor() as cursor:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            columns = [col[0] for col in cursor.description]
            rows = cursor.fetchall()
            
            df = pd.DataFrame(rows, columns=columns)
            app.logger.info(f"查询返回 {len(df)} 条记录")  # 添加日志
            return df
            
    except Exception as e:
        app.logger.error(f"执行规则查询失败: {str(e)}\nQuery: {query}\nParams: {params}")
        raise

def handle_db_error(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except oracledb.DatabaseError as e:
            logging.error(f"Database error in {f.__name__}: {str(e)}")
            return jsonify({'error': '数据库连接错误，请稍后重试'}), 500
        except Exception as e:
            logging.error(f"Unexpected error in {f.__name__}: {str(e)}")
            return jsonify({'error': '系统错误，请稍后重试'}), 500
    return decorated_function

# 应用错误处理装饰器到数据库相关的路由
@app.route('/api/rules', methods=['GET'])
@handle_db_error
def get_rules():
    """获取所有规则"""
    try:
        # 修改查询，使用两步查询：先聚合城市和规则来源，再与其他字段关联
        query = """
        WITH rule_cities AS (
            SELECT 
                规则id,
               -- LISTAGG(DISTINCT 城市, ', ') WITHIN GROUP (ORDER BY 城市) AS 城市列表,
               -- LISTAGG(DISTINCT 规则来源, ', ') WITHIN GROUP (ORDER BY 规则来源) AS 规则来源列表
                to_char(wm_concat(DISTINCT 城市))  AS 城市列表,
                to_char(wm_concat(DISTINCT 规则来源)) AS 规则来源列表
            FROM 规则医保编码对照
            GROUP BY 规则id
        )
        SELECT
            a.id as "ID",
            a.序号,
            a.规则名称,
            a.适用范围,
            rc.城市列表 as 城市,
            rc.规则来源列表 as 规则来源,
            a.行为认定,
            b.规则内涵,
            a.发布时间,
            a.生效日期,
            a.失效日期,
            a.涉及科室,
            a.违规数量,
            a.违规天数,
            a.违规小时数,
            a.违规金额,
            a.年龄,
            a.性别,
            a.类型,
            a.规则类型,
            b.医保编码1,
            b.医保名称1,
            b.医保编码2,
            b.医保名称2,
            a.国家医保编码1,
            a.国家医保名称1,
            a.国家医保编码2,
            a.国家医保名称2,
            b.物价编码,
            a.适用年份,
            a.用途,
            a.备注
        FROM 飞检规则知识库 a
        LEFT JOIN rule_cities rc ON a.id = rc.规则id 

        LEFT JOIN (
            SELECT 规则id, 医保编码1, 医保名称1, 医保编码2, 医保名称2, 物价编码,规则内涵,
                   ROW_NUMBER() OVER (PARTITION BY 规则id ORDER BY 对照id) AS rn
            FROM 规则医保编码对照
        ) b ON a.id = b.规则id AND b.rn = 1
        --where a.id in ('244','294','1707','325','335','337','407','408','303','393','399','403','423','380','1011','369','252','290','353','1710','327','330','263','271','299','387','394','424','985','249','250','251','253','289','315','1698','409','262','273','395','412','419','340','371','382','390','404','425','418','417','410','1050','1006','230','240','306','314','347','1709','326','329','255','260','270','374','398','1697','370','231','239','242','248','288','318','264','1700','336','232','328','339','301','384','413','342','392','422','1007','402','233','241','292','322','323','243','1706','1708','368','333','341','261','265','254','376','383','291','1696','1701','334','405','259','304','414','415','385','401','287','420','421','955','1744','1005','234','295','324','345','361','320','274','406','272','313','302','305','373')
        ORDER BY a.id DESC
        """
        
        with get_connection(pool) as conn:
            df = execute_rules_query(conn, query)
            
        # 将 DataFrame 转换为字典列表，并确保处理 null 值
        rules = []
        for _, row in df.iterrows():
            rule = {}
            for col in df.columns:
                value = row[col]
                if pd.isna(value):
                    rule[col] = ''
                else:
                    rule[col] = str(value) if isinstance(value, (int, float)) else value
            rules.append(rule)
            
        return jsonify(rules)
    except Exception as e:
        app.logger.error(f"获取规则列表失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/rules/<int:rule_id>', methods=['GET'])
@handle_db_error
def get_rule(rule_id):
    try:
        # 获取规则基本信息的查询
        rule_query = """
        SELECT 
            b.对照id,
            id as "ID",
            序号,
            规则名称,
            适用范围,
            b.城市,
            b.规则来源,
            行为认定,
            规则内涵,
            涉及科室,
            违规数量,
            违规天数,
            违规小时数,
            违规金额,
            年龄,
            类型,
            规则类型,
            医保编码1,
            医保名称1,
            医保编码2,
            医保名称2,
            国家医保编码1,
            国家医保名称1,
            国家医保编码2,
            国家医保名称2,
            排除诊断,
            排除科室,
            包含诊断,
            包含科室,
            时间类型,
            物价编码,
            备注,
            性别,
            用途
        FROM 飞检规则知识库 a ,规则医保编码对照 b 
        where a.id=b.规则id
        and a.id = :rule_id
        """
        
        # 获取城市列表的查询
        cities_query = """
        SELECT DISTINCT 城市
        FROM 规则医保编码对照
        WHERE 规则ID = :rule_id
        ORDER BY 城市
        """
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 获取规则基本信息
                cursor.execute(rule_query, {'rule_id': rule_id})
                columns = [col[0] for col in cursor.description]
                row = cursor.fetchone()
                
                if not row:
                    return jsonify({
                        'error': '未找到指定规则'
                    }), 404
                
                # 构建结果字典
                result = dict(zip(columns, row))
                
                # 处理所有字段的 None 值
                for key in result:
                    if result[key] is None:
                        result[key] = ''
                
                # 获取城市列表
                cursor.execute(cities_query, {'rule_id': rule_id})
                cities = [row[0] for row in cursor.fetchall()]
                
                # 将城市列表添加到结果中
                result['cities'] = cities
                
                return jsonify(result)
                
    except Exception as e:
        app.logger.error(f"获取规则详情失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/rules', methods=['POST'])
@handle_db_error
def create_rule():
    try:
        data = request.json
        
        # 处理日期字段
        date_fields = ['创建时间', '更新时间']
        for field in date_fields:
            if field in data and data[field]:
                try:
                    datetime.strptime(data[field], '%Y-%m-%d')
                except ValueError:
                    return jsonify({
                        'error': f'{field}日期格式无效，请使用YYYY-MM-DD格式'
                    }), 400
        
        # 设置默认值
        data['规则类型'] = data.get('规则类型', '0')
        
        # 构建查询获取序列值
        seq_query = "SELECT 飞检规则知识库ID_SEQ.NEXTVAL FROM DUAL"
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 获取序列值
                cursor.execute(seq_query)
                next_id = cursor.fetchone()[0]
                
                # 构建插入语句
                columns = []
                values = []
                params = {'id': next_id}  # 手动设置ID
                
                # 添加其他字段
                for key, value in data.items():
                    if key != 'id':  # 跳过ID字段，使用序列生成
                        columns.append(key)
                        if key in date_fields and value:
                            values.append(f"TO_DATE(:{key}, 'YYYY-MM-DD')")
                        else:
                            values.append(f":{key}")

                        # 特殊处理性别字段
                        if key == '性别':
                            if isinstance(value, str):
                                value = value.strip()
                                # 只允许"男"、"女"或空值
                                if value not in ['男', '女', '']:
                                    value = None
                            params[key] = value if value != '' else None
                        else:
                            params[key] = value if value != '' else None
                
                query = f"""
                INSERT INTO 飞检规则知识库 (ID, {', '.join(columns)})
                VALUES (:id, {', '.join(values)})
                """
                
                # 执行插入
                cursor.execute(query, params)
            conn.commit()
        
        return jsonify({
            'message': '创建成功',
            'id': next_id,
            'success': True
        })
        
    except Exception as e:
        app.logger.error(f"创建规则失败: {str(e)}")
        error_message = str(e)
        if 'ORA-01861' in error_message:
            error_message = '日期格式错误，请确保所有日期使用YYYY-MM-DD格式'
        
        return jsonify({
            'error': error_message,
            'success': False
        }), 500

@app.route('/api/rules/<int:rule_id>', methods=['PUT'])
@handle_db_error
def update_rule(rule_id):
    try:
        data = request.json
        
        # 处理日期字段
        date_fields = ['创建时间','更新时间']
        for field in date_fields:
            if field in data and data[field]:
                try:
                    datetime.strptime(data[field], '%Y-%m-%d')
                except ValueError:
                    return jsonify({
                        'error': f'{field}日期格式无效，请使用YYYY-MM-DD格式'
                    }), 400
        
        # 构建更新查询
        update_fields = []
        params = {}
        
        for key, value in data.items():
            if key != 'id':  # 跳过ID字段
                if key in date_fields and value:
                    # 对于日期字段，使用 TO_DATE 函数
                    update_fields.append(f"{key} = TO_DATE(:{key}, 'YYYY-MM-DD')")
                else:
                    update_fields.append(f"{key} = :{key}")

                # 特殊处理性别字段
                if key == '性别':
                    if isinstance(value, str):
                        value = value.strip()
                        # 只允许"男"、"女"或空值
                        if value not in ['男', '女', '']:
                            value = None
                    params[key] = value if value != '' else None
                else:
                    params[key] = value if value != '' else None  # 空字符串转换为 None
        
        params['rule_id'] = rule_id
        # 只更新特定字段
        specific_update_fields = [
            
            "国家医保编码1 = :国家医保编码1",
            "国家医保编码2 = :国家医保编码2",
            "国家医保名称1 = :国家医保名称1",
            "国家医保名称2 = :国家医保名称2",
            "规则名称 = :规则名称",
            "行为认定 = :行为认定",
            "类型 = :类型",
            "规则类型 = :规则类型",
            "违规数量 = :违规数量",
            "违规金额 = :违规金额",
            "年龄 = :年龄",
            "性别 = :性别",
            "时间类型 = :时间类型",
            "排除科室 = :排除科室",
            "排除诊断 = :排除诊断",
            "包含科室 = :包含科室",
            "包含诊断 = :包含诊断",
            "用途 = :用途",
            "备注 = :备注"
        ]
     # 只传入 specific_update_fields 中的参数
        specific_params = {
            '国家医保编码1': params.get('国家医保编码1', ''),
            '国家医保编码2': params.get('国家医保编码2', ''),
            '国家医保名称1': params.get('国家医保名称1', ''),
            '国家医保名称2': params.get('国家医保名称2', ''),
            '规则名称': params.get('规则名称', ''),
            '行为认定': params.get('行为认定', ''),
            '类型': params.get('类型', ''),
            '规则类型': params.get('规则类型', ''),
            '违规数量': params.get('违规数量', ''),
            '违规金额': params.get('违规金额', ''),
            '年龄': params.get('年龄', ''),
            '性别': params.get('性别', ''),
            '时间类型': params.get('时间类型', ''),
            '排除科室': params.get('排除科室', ''),
            '排除诊断': params.get('排除诊断', ''),
            '包含科室': params.get('包含科室', ''),
            '包含诊断': params.get('包含诊断', ''),
            '用途': params.get('用途', ''),
            '备注': params.get('备注', ''),
            'rule_id': params['rule_id']
        }

        query = f"""
        UPDATE 飞检规则知识库
        SET {', '.join(specific_update_fields)}
        WHERE ID = :rule_id
        """
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, specific_params)
            conn.commit()
        app.logger.info(f"规则: {params['规则名称']}保存成功")
            
        return jsonify({
            'message': '规则更新成功',
            'success': True
        })
        
    except Exception as e:
        app.logger.error(f"更新规则失败: {str(e)}")
        error_message = str(e)
        if 'ORA-01861' in error_message:
            error_message = '日期格式错误，请确保所有日期使用YYYY-MM-DD格式'
        
        return jsonify({
            'error': error_message,
            'success': False
        }), 500

@app.route('/api/rules/<int:rule_id>', methods=['DELETE'])
@handle_db_error
def delete_rule(rule_id):
    try:
        # 首先删除规则医保编码对照表中的记录
        delete_compare_query = "DELETE FROM 规则医保编码对照 WHERE 规则id = :rule_id"
        
        # 然后删除飞检规则知识库中的记录
        delete_rule_query = "DELETE FROM 飞检规则知识库 WHERE id = :rule_id"
        
        # 执行删除操作
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 先删除对照表数据
                cursor.execute(delete_compare_query, {'rule_id': rule_id})
                # 再删除规则表数据
                cursor.execute(delete_rule_query, {'rule_id': rule_id})
            # 提交事务
            conn.commit()
        
        return jsonify({
            'success': True,
            'message': '删除成功'
        })
        
    except Exception as e:
        app.logger.error(f"删除规则失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 初始化数据库连接池
def init_db_pool():
    try:
        # 读取 datachange 数据库配置
        db_config = {
            'username': config.get('datachange', 'username'),
            'password': config.get('datachange', 'password'),
            'dsn': config.get('datachange', 'dsn')
        }
        
        # 初始化Oracle客户端，用来支持11g的oracle
        try:
            oracledb.init_oracle_client()  
            logging.info("Oracle客户端初始化成功")
        except Exception as e:
            # 如果已经初始化过，会抛出异常，但这不影响后续操作
            logging.warning(f"Oracle客户端初始化时出现警告（可能已初始化）: {str(e)}")

        # 创建连接池，使用更健壮的配置参数
        pool = oracledb.create_pool(
            user=db_config['username'],
            password=db_config['password'],
            dsn=db_config['dsn'],
            min=3,                           # 增加最小连接数
            max=10,                          # 增加最大连接数
            increment=1,
            getmode=oracledb.POOL_GETMODE_WAIT,
            wait_timeout=20000,              # 增加等待超时时间
            timeout=600,                     # 增加超时时间
            retry_count=5,                   # 增加重试次数
            retry_delay=2,
            max_lifetime_session=43200,      # 增加会话生命周期到12小时
            ping_interval=60                 # 添加ping间隔，保持连接活跃
        )
        
        logging.info("Oracle连接池初始化成功")
        
        # 初始化PostgreSQL连接池
        try:
            pg_conninfo = (
                f"host={config.get('postgresql', 'host')} "
                f"port={config.get('postgresql', 'port', fallback='5432')} "
                f"dbname={config.get('postgresql', 'dbname')} "
                f"user={config.get('postgresql', 'user')} "
                f"password={config.get('postgresql', 'password')} "
            )
            
            poolpg = psycopg_pool.ConnectionPool(
                conninfo=pg_conninfo,
                min_size=2,
                max_size=5,
                timeout=30
            )
            logging.info("PostgreSQL连接池初始化成功")
            return pool, poolpg
        except Exception as pg_error:
            logging.error(f"PostgreSQL连接池初始化失败: {str(pg_error)}")
            # 即使PostgreSQL连接池初始化失败，我们仍然返回Oracle连接池
            # 这样即使PostgreSQL不可用，Oracle相关功能仍然可以正常工作
            return pool, None
        
    except Exception as e:
        logging.error(f"Oracle连接池初始化失败: {str(e)}")
        raise

# 性别值清理函数
def clean_gender_value(value):
    """清理性别字段值，确保只保存有效的性别值"""
    if not value:
        return None

    if isinstance(value, str):
        value = value.strip()
        # 只允许"男"、"女"或空值
        if value in ['男', '女']:
            return value

    return None

# 创建数据库连接池
pool,poolpg = init_db_pool()

@app.route('/api/verify_gender', methods=['POST'])
@handle_db_error
def verify_gender():
    """验证性别字段保存是否正确"""
    try:
        data = request.json
        rule_id = data.get('rule_id')

        if not rule_id:
            return jsonify({'success': False, 'error': '缺少规则ID'}), 400

        query = "SELECT 性别 FROM 飞检规则知识库 WHERE ID = :rule_id"

        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, {'rule_id': rule_id})
                result = cursor.fetchone()

                if result:
                    gender = result[0]
                    return jsonify({
                        'success': True,
                        'gender': gender
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': '规则不存在'
                    }), 404

    except Exception as e:
        app.logger.error(f"验证性别字段失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/rules/next-id', methods=['GET'])
def get_next_rule_id():
    try:
        # 通过查询序列的下一个值来获取ID
        query = "SELECT 飞检规则知识库ID_SEQ.NEXTVAL as next_id FROM DUAL"
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query)
                result = cursor.fetchone()
                next_id = result[0] if result else 1
                return jsonify({'next_id': next_id})
    except Exception as e:
        app.logger.error(f"获取下一个规则ID失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

# 全局错误处理
@app.errorhandler(404)
def not_found_error(error):
    return jsonify({'error': '请求的资源不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500

@app.errorhandler(Exception)
def handle_exception(e):
    logging.error(f"Unhandled exception: {str(e)}")
    return jsonify({'error': '系统错误，请稍后重试'}), 500

# 初始化数据库连接池
try:
    # 统一使用init_db_pool函数初始化连接池
    pool, poolpg = init_db_pool()
    logging.info("数据库连接池初始化成功")
except Exception as e:
    logging.error(f"应用程序初始化失败: {str(e)}")
    raise

@app.route('/api/medical-insurance', methods=['GET'])
@handle_db_error
def get_medical_insurance():
    try:
        # 获取查询参数
        city = request.args.get('city', '').strip()
        code = request.args.get('code', '').strip()
        name = request.args.get('name', '').strip()
        country_code = request.args.get('country_code', '').strip()
        price_code = request.args.get('price_code', '').strip()
       
        # 首先检查城市是否存在
        city_check_query = """
        SELECT DISTINCT 城市
        FROM 医保三目表
        WHERE 城市 = :city
        """
        
        message = None
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(city_check_query, {'city': city})
                city_exists = cursor.fetchone()
                
                # 如果城市不存在，使用南京
                if not city_exists:
                    #city = '南京'
                    message = f"当前城市未找到对应的医保目录"
        
        # 构建基础查询
        query = """
        SELECT DISTINCT 
            城市 as city,
            医保项目编码 as code,
            医保项目名称 as name,
            国家编码 as country_code,
            物价码 as price_code,
            费用类别 as fee_type,
            单位 as unit,
            单价 as unit_price
        FROM 医保三目表
        WHERE 1=1
        """
        params = {}
        
        # 处理所有搜索字段，支持多个关键词
        def add_search_conditions(field, value, param_prefix):
            if not value:
                return "", {}
            conditions = []
            search_params = {}
            for i, keyword in enumerate(value.split('|')):
                if keyword.strip():
                    param_name = f'{param_prefix}_{i}'
                    conditions.append(f"{field} LIKE :{param_name}")
                    search_params[param_name] = f"%{keyword.strip()}%"
            if conditions:
                return f" AND ({' OR '.join(conditions)})", search_params
            return "", {}
            
        # 添加城市条件
        if city:
            query += " AND 城市 = :city"
            params['city'] = city
            
        # 添加各个字段的搜索条件
        if code:
            code_condition, code_params = add_search_conditions("医保项目编码", code, "code")
            query += code_condition
            params.update(code_params)
            
        if name:
            name_condition, name_params = add_search_conditions("医保项目名称", name, "name")
            query += name_condition
            params.update(name_params)
            
        if country_code:
            country_condition, country_params = add_search_conditions("国家编码", country_code, "country_code")
            query += country_condition
            params.update(country_params)
            
        if price_code:
            price_condition, price_params = add_search_conditions("物价码", price_code, "price_code")
            query += price_condition
            params.update(price_params)
            
        query += " ORDER BY 城市, 医保项目编码"

        # 执行查询
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                columns = [col[0].lower() for col in cursor.description]
                rows = cursor.fetchall()
                
                results = []
                for row in rows:
                    result = dict(zip(columns, row))
                    for key in result:
                        if result[key] is None:
                            result[key] = ''
                    results.append(result)
                
                response_data = {
                    'data': results,
                    'total': len(results),
                    'success': True,
                }
                
                # 如果有提示消息，添加到响应中
                if message:
                    response_data['message'] = message
                
                return jsonify(response_data)
                
    except Exception as e:
        app.logger.error(f"获取医保信息失败: {str(e)}")
        return jsonify({
            'error': str(e),
            'success': False
        }), 500

@app.route('/api/rules/get-training-data', methods=['GET'])
@handle_db_error
def get_training_data():
    """获取规则训练数据用于优化提示词"""
    try:
        # Input查询语句
        input_query = """
        SELECT
        a.规则名称, a.行为认定, b.城市, b.规则内涵, b.规则来源
         FROM 飞检规则知识库 a, 规则医保编码对照 b
        where a.id = b.规则id  and b.医保名称1 is not null
        """

        # Output查询语句
        output_query = """
        SELECT
        a.规则名称, a.行为认定, b.城市, b.规则内涵, b.规则来源,a.类型,b.医保名称1,b.医保名称2,a.违规数量,a.违规金额,a.年龄,a.性别,a.排除科室,a.包含科室,a.排除诊断,a.包含诊断,a.时间类型
         FROM 飞检规则知识库 a, 规则医保编码对照 b
        where a.id = b.规则id and b.医保名称1 is not null
        """

        with get_connection(pool) as conn:
            cursor = conn.cursor()

            # 执行output查询获取完整数据
            cursor.execute(output_query)
            columns = [desc[0] for desc in cursor.description]
            results = cursor.fetchall()

            training_data = []
            for row in results:
                data_dict = dict(zip(columns, row))
                training_data.append(data_dict)

            return jsonify({
                'success': True,
                'count': len(training_data),
                'data': training_data[:50]  # 返回前50条用于分析
            })

    except Exception as e:
        app.logger.error(f"获取训练数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取训练数据失败: {str(e)}'
        }), 500

@app.route('/api/rules/batch-update-ai-fields/<int:rule_id>', methods=['PUT'])
@handle_db_error
def batch_update_ai_fields(rule_id):
    """批量更新AI分析得到的字段，只更新有值的字段"""
    try:
        data = request.json

        # 构建动态更新字段和参数
        update_fields = []
        params = {'rule_id': rule_id}

        # 只添加有值的字段到更新列表
        field_mapping = {
            '类型': 'type',
            '违规数量': 'violation_count',
            '时间类型': 'time_type',
            '违规金额': 'violation_amount',
            '年龄': 'age_limit',
            '性别': 'gender_limit',
            '排除诊断': 'exclude_diagnosis',
            '排除科室': 'exclude_departments',
            '包含诊断': 'include_diagnosis',
            '包含科室': 'include_departments'
        }

        for db_field, json_field in field_mapping.items():
            if json_field in data and data[json_field] is not None and data[json_field] != '':
                update_fields.append(f"{db_field} = :{db_field}")
                params[db_field] = data[json_field]

        # 如果没有字段需要更新，直接返回成功
        if not update_fields:
            return jsonify({
                'message': '没有字段需要更新',
                'success': True
            })

        # 构建并执行更新查询
        query = f"""
        UPDATE 飞检规则知识库
        SET {', '.join(update_fields)}
        WHERE ID = :rule_id
        """

        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
            conn.commit()

        app.logger.info(f"规则 {rule_id} AI字段更新成功")

        return jsonify({
            'message': 'AI字段更新成功',
            'success': True
        })

    except Exception as e:
        app.logger.error(f"批量更新AI字段失败: {str(e)}")
        return jsonify({
            'error': str(e),
            'success': False
        }), 500

@app.route('/api/rules/intelligent-get-medical-names', methods=['POST'])
@handle_db_error
def intelligent_get_medical_names():
    """智能获取医保名称"""
    try:
        data = request.json
        rule_content = data.get('rule_content', '')
        rule_name = data.get('rule_name', '')
        behavior_type = data.get('behavior_type', '')
        city = data.get('city', '')  # 获取城市信息，不设默认值

        if not rule_content and not rule_name:
            return jsonify({
                'success': False,
                'error': '请提供规则内容或规则名称'
            }), 400

        if not city:
            return jsonify({
                'success': False,
                'error': '请提供城市信息'
            }), 400

        # 构建分析文本
        analysis_text = f"""
规则名称：{rule_name}
行为认定：{behavior_type}
规则内容：{rule_content}
        """.strip()

        # 调用Gemini API
        app.logger.info(f"准备调用Gemini API，分析文本长度: {len(analysis_text)}")
        result = call_gemini_api(analysis_text)
        app.logger.info(f"Gemini API调用完成，结果: {result}")

        if result['success']:
            # 查询医保三目表获取编码信息
            medical_codes_info = query_medical_codes(result.get('medical_name1', ''), result.get('medical_name2', ''), city)

            return jsonify({
                'success': True,
                'medical_name1': result.get('medical_name1', ''),
                'medical_name2': result.get('medical_name2', ''),
                'type': result.get('type', ''),
                'violation_count': result.get('violation_count', ''),
                'exclude_diagnosis': result.get('exclude_diagnosis', ''),
                'exclude_departments': result.get('exclude_departments', ''),
                'include_diagnosis': result.get('include_diagnosis', ''),
                'include_departments': result.get('include_departments', ''),
                'time_type': result.get('time_type', ''),
                'violation_amount': result.get('violation_amount', ''),
                'age_limit': result.get('age_limit', ''),
                'gender_limit': result.get('gender_limit', ''),
                'confidence': result.get('confidence', 0.0),
                'reasoning': result.get('reasoning', ''),
                'medical_codes': medical_codes_info  # 添加医保编码信息
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '未知错误'),
                'details': result.get('details', '')
            }), 500

    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        app.logger.error(f"智能获取医保名称失败: {str(e)}")
        app.logger.error(f"错误堆栈: {error_traceback}")
        return jsonify({
            'success': False,
            'error': f'智能获取失败: {str(e)}',
            'traceback': error_traceback
        }), 500

@app.route('/api/rules/preview', methods=['POST'])
@handle_db_error
def preview_rules():
    try:
        app.logger.info("开始处理规则预览请求")
        
        if 'file' not in request.files:
            app.logger.warning("未找到上传的文件")
            return jsonify({'error': '请选择要导入的Excel文件', 'success': False}), 400
            
        file = request.files['file']
        if file.filename == '':
            app.logger.warning("文件名为空")
            return jsonify({'error': '未选择文件', 'success': False}), 400
            
        if not file.filename.endswith(('.xlsx', '.xls')):
            app.logger.warning(f"不支持的文件类型: {file.filename}")
            return jsonify({'error': '只支持Excel文件格式(.xlsx, .xls)', 'success': False}), 400

        # 创建临时文件保存上传的Excel
        temp_dir = create_temp_dir()
        temp_file = os.path.join(temp_dir, secure_filename(file.filename))
        file.save(temp_file)
        
        app.logger.info(f"开始读取Excel文件: {file.filename}")
        
        try:
            # 读取Excel文件
            df = pd.read_excel(temp_file)
            #app.logger.info(f"成功读取Excel文件，包含 {len(df)} 行数据")
        except Exception as e:
            #app.logger.error(f"读取Excel文件失败: {str(e)}")
            return jsonify({'error': f'读取Excel文件失败: {str(e)}', 'success': False}), 400
        finally:
            # 清理临时文件
            try:
                os.remove(temp_file)
                os.rmdir(temp_dir)
            except Exception as e:
                app.logger.warning(f"清理临时文件失败: {str(e)}")
        
        # 获取数据库表的列名
        query = """
        SELECT column_name 
        FROM user_tab_columns 
        WHERE table_name IN ('飞检规则知识库','规则医保编码对照')
        """
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query)
                db_columns = [row[0] for row in cursor.fetchall()]
                #app.logger.info(f"获取到数据库列名: {db_columns}")
                
                # 过滤Excel列名中的*号并匹配数据库列名
                excel_columns = df.columns
                valid_columns = []
                for col in excel_columns:
                    # 移除列名中的*号
                    clean_col = col.replace('*', '').strip()
                    if clean_col in db_columns:
                        valid_columns.append(col)

                if not valid_columns:
                    app.logger.warning("未找到匹配的列")
                    return jsonify({'error': '未找到匹配的列，请检查Excel文件格式是否正确', 'success': False}), 400
                
                #app.logger.info(f"有效的列: {valid_columns}")
                
                # 转换数据为列表
                preview_data = []
                for _, row in df.iterrows():
                    rule_data = {}
                    for col in valid_columns:
                        # 使用清理后的列名作为键
                        clean_col = col.replace('*', '').strip()
                        value = row[col]
                        # 处理空值和特殊类型
                        if pd.isna(value):
                            rule_data[clean_col] = ''
                        elif isinstance(value, (datetime, pd.Timestamp)):
                            rule_data[clean_col] = value.strftime('%Y-%m-%d')
                        else:
                            rule_data[clean_col] = str(value) if isinstance(value, (int, float)) else value
                    preview_data.append(rule_data)
                
                return jsonify({
                    'success': True,
                    'data': preview_data
                })
                
    except Exception as e:
        app.logger.error(f"预览规则失败: {str(e)}")
        return jsonify({'error': f'预览失败: {str(e)}', 'success': False}), 500

@app.route('/api/rules/import', methods=['POST'])
@handle_db_error
def import_rules():
    try:
        data = request.get_json()
        if not data or 'rules' not in data:
            return jsonify({'error': '无效的请求数据', 'success': False}), 400
            
        rules = data['rules']
        if not rules:
            return jsonify({'error': '没有要导入的规则', 'success': False}), 400
            
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 准备统计数据
                success_count = 0
                skip_rule_count = 0
                skip_city_count = 0
                error_count = 0
                error_messages = []
                skipped_rules = []
                
                for index, rule in enumerate(rules, 1):
                    try:
                        rule_name = rule.get('规则名称', '')
                        city = rule.get('城市', '')
                        
                        # 1. 检查规则名称是否存在
                        cursor.execute("""
                            SELECT ID 
                            FROM 飞检规则知识库 
                            WHERE 规则名称 = :rule_name
                        """, {'rule_name': rule_name})
                        
                        existing_rule = cursor.fetchone()
                        rule_id = None
                        
                        if existing_rule:
                            # 规则已存在，使用现有规则ID
                            rule_id = existing_rule[0]
                            skip_rule_count += 1
                            skipped_rules.append({
                                '规则名称': rule_name,
                                '原因': '规则名称已存在'
                            })
                        else:
                            # 规则不存在，插入新规则
                            rule_columns = ['ID', '序号', '类型', '规则名称', 
                                          '行为认定', '违规数量', '违规天数', '违规小时数', 
                                          '违规金额', '年龄', '备注',   '涉及科室', '规则类型',
                                          '国家医保编码1', '国家医保名称1', '国家医保编码2', '国家医保名称2',
                                          '排除诊断', '排除科室', '包含诊断', '包含科室', '时间类型', '项目数量']
                            
                            rule_query = """
                            INSERT INTO 飞检规则知识库 (ID, 序号, 类型, 规则名称,
                                          行为认定, 违规数量, 违规天数, 违规小时数,
                                          违规金额, 年龄,性别,  备注,  涉及科室, 规则类型,
                                          国家医保编码1, 国家医保名称1, 国家医保编码2, 国家医保名称2,
                                        排除诊断, 排除科室, 包含诊断, 包含科室, 时间类型,项目数量)
                            VALUES (飞检规则知识库ID_SEQ.NEXTVAL, :serial, :type, :rule_name,
                                    :behavior, :violation_count, :violation_days, :violation_hours,
                                    :violation_amount, :age, :gender, :remarks,   :departments, :status,
                                    :national_code1, :national_name1, :national_code2, :national_name2,
                                    :exclude_diagnosis, :exclude_departments, :include_diagnosis, :include_departments, :time_type, :item_count)
                            RETURNING ID INTO :new_id

                            """
                            
                            # 准备规则表参数
                            rule_params = {
                                'serial': rule.get('序号', ''),
                                'type': rule.get('类型', ''),
                                'rule_name': rule_name,
                                'behavior': rule.get('行为认定', ''),
                                'violation_count': rule.get('违规数量', ''),
                                'violation_days': rule.get('违规天数', ''),
                                'violation_hours': rule.get('违规小时数', ''),
                                'violation_amount': rule.get('违规金额', ''),
                                'age': rule.get('年龄', ''),
                                'gender': clean_gender_value(rule.get('性别', '')),  # 清理性别值
                                'remarks': rule.get('备注', ''),
                                'departments': rule.get('涉及科室', ''),
                                'status': rule.get('规则类型', ''),
                                'national_code1': rule.get('国家医保编码1', ''),
                                'national_name1': rule.get('国家医保名称1', ''),
                                'national_code2': rule.get('国家医保编码2', ''),
                                'national_name2': rule.get('国家医保名称2', ''),
                                'exclude_diagnosis': rule.get('排除诊断', ''),
                                'exclude_departments': rule.get('排除科室', ''),
                                'include_diagnosis': rule.get('包含诊断', ''),
                                'include_departments': rule.get('包含科室', ''),
                                'time_type': rule.get('时间类型', ''),
                                'item_count': rule.get('项目数量', ''),
                                'new_id': cursor.var(int)
                            }
                            
                            # 处理日期字段
                            for date_field in ['publish_date', 'effect_date', 'invalid_date']:
                                value = rule.get(date_field.upper(), '')
                                if value:
                                    try:
                                        rule_params[date_field] = datetime.strptime(value, '%Y-%m-%d')
                                    except ValueError:
                                        app.logger.warning(f"日期格式转换失败 {date_field}: {value}")
                                        rule_params[date_field] = None
                            
                            try:
                                #app.logger.info(f"准备插入规则: {rule_name}")
                                #app.logger.info(f"SQL: {rule_query}")
                                #app.logger.info(f"参数: {rule_params}")
                                
                                cursor.execute(rule_query, rule_params)
                                rule_id = rule_params['new_id'].getvalue()[0] if isinstance(rule_params['new_id'].getvalue(), list) else rule_params['new_id'].getvalue()
                            
                                conn.commit()

                                # 验证插入结果
                                cursor.execute("""
                                    SELECT ID, 规则名称
                                    FROM 飞检规则知识库 
                                    WHERE ID = :rule_id
                                """, {'rule_id': rule_id})
                                
                                result = cursor.fetchone()
                                if result:
                                    #app.logger.info(f"成功插入规则，ID: {rule_id}, 规则名称: {result[1]}")
                                    pass
                                else:
                                    raise Exception("插入后未能找到规则记录")
                                    
                            except Exception as e:
                                conn.rollback()
                                app.logger.error(f"插入规则失败: {str(e)}")
                                app.logger.error(f"规则名称: {rule_name}")
                                app.logger.error(f"SQL: {rule_query}")
                                app.logger.error(f"参数: {rule_params}")
                                raise Exception(f"插入规则失败: {str(e)}")
                        
                        # 2. 如果有城市信息，检查规则医保编码对照是否存在
                        if city and rule_id:
                            cursor.execute("""
                                SELECT 对照ID 
                                FROM 规则医保编码对照 
                                WHERE 规则ID = :rule_id AND 城市 = :city
                            """, {'rule_id': rule_id, 'city': city})
                            
                            if cursor.fetchone():
                                skip_city_count += 1
                                skipped_rules.append({
                                    '规则名称': rule_name,
                                    '城市': city,
                                    '原因': '规则城市对照已存在'
                                })
                                continue
                            
                            # 城市对照不存在，插入新对照
                            compare_columns = ['对照ID', '规则ID', '省份', '城市', '规则内涵',
                                            '医保编码1', '医保名称1', '医保编码2', '医保名称2',
                                            '物价编码']
                            
                            # 如果规则有国家医保编码，查询对应的医保编码和名称
                            if rule_id:
                            
                                 cursor.execute("""
                                        SELECT (SELECT to_char(wm_concat(DISTINCT m1.医保项目编码))
                                                FROM 医保三目表 m1
                                                WHERE INSTR(',' || a.国家医保编码1 || ',',
                                                            ',' || m1.国家编码 || ',') > 0
                                                            AND m1.国家编码 IS NOT NULL
                                                            AND m1.城市 = :city) AS medical_code1,
                                        (SELECT to_char(wm_concat(DISTINCT m1.医保项目名称))
                                                FROM 医保三目表 m1
                                                WHERE INSTR(',' || a.国家医保编码1 || ',',
                                                            ',' || m1.国家编码 || ',') > 0
                                                            AND m1.国家编码 IS NOT NULL
                                                            AND m1.城市 = :city) AS medical_name1,
                                        (SELECT to_char(wm_concat(DISTINCT m2.医保项目编码))
                                                FROM 医保三目表 m2
                                                WHERE INSTR(',' || a.国家医保编码2 || ',',
                                                            ',' || m2.国家编码 || ',') > 0
                                                            AND m2.国家编码 IS NOT NULL
                                                            AND m2.城市 = :city) AS medical_code2,
                                        (SELECT to_char(wm_concat(DISTINCT m2.医保项目名称))
                                                FROM 医保三目表 m2
                                                WHERE INSTR(',' || a.国家医保编码2 || ',',
                                                            ',' || m2.国家编码 || ',') > 0
                                                            AND m2.国家编码 IS NOT NULL
                                                            AND m2.城市 = :city) AS medical_name2
                                        FROM 飞检规则知识库 a
                                        WHERE a.id = :rule_id
                                 """, {'city': city, 'rule_id': rule_id})
                                 medical_info = cursor.fetchone()
                            compare_params = {
                                    'rule_id': rule_id,
                                    'province': rule.get('省份', ''),
                                    'city': city,
                                    'rule_content': rule.get('规则内涵', ''),
                                    'medical_code1': medical_info[0] if medical_info and medical_info[0] else rule.get('医保编码1', ''),
                                    'medical_name1': medical_info[1] if medical_info and medical_info[1] else rule.get('医保名称1', ''),
                                    'medical_code2': medical_info[2] if medical_info and medical_info[2] else rule.get('医保编码2', ''),
                                      'medical_name2': medical_info[3] if medical_info and medical_info[3] else rule.get('医保名称2', ''),
                                    'price_code': rule.get('物价编码', ''),
                                    'new_compare_id': cursor.var(int)
                                }

                            compare_query = """
                            INSERT INTO 规则医保编码对照 (对照ID, 规则ID, 序号_S, 省份, 城市, 规则内涵, 规则来源,
                                                    医保编码1, 医保名称1, 医保编码2, 医保名称2, 物价编码,规则依据)
                            VALUES (规则医保编码对照_SEQ.NEXTVAL, :rule_id, :serial_s, :province, :city, :rule_content,
                                    :rule_source, :medical_code1, :medical_name1, :medical_code2, :medical_name2, :price_code, :rule_basis)
                            RETURNING 对照ID INTO :new_compare_id
                            """
                            compare_params = {
                                'rule_id': rule_id,
                                'serial_s': rule.get('序号', ''),
                                'province': rule.get('省份', ''),
                                'city': city,
                                'rule_content': rule.get('规则内涵', ''),
                                'rule_source': rule.get('规则来源', ''),
                                'medical_code1': medical_info[0] if medical_info and medical_info[0] else rule.get('医保编码1', ''),
                                'medical_name1': medical_info[1] if medical_info and medical_info[1] else rule.get('医保名称1', ''),
                                'medical_code2': medical_info[2] if medical_info and medical_info[2] else rule.get('医保编码2', ''),
                                'medical_name2': medical_info[3] if medical_info and medical_info[3] else rule.get('医保名称2', ''),
                                'price_code': rule.get('物价编码', ''),
                                'rule_basis': rule.get('规则依据', ''),
                                'new_compare_id': cursor.var(int)  # 用于接收返回的对照ID
                            }
                            
                            try:
                             
                                cursor.execute(compare_query, compare_params)
                                # 获取单个值而不是数组
                                new_compare_id = compare_params['new_compare_id'].getvalue()[0] if isinstance(compare_params['new_compare_id'].getvalue(), list) else compare_params['new_compare_id'].getvalue()
                                # 立即提交事务
                                conn.commit()
                                
                                #app.logger.info(f"插入医保编码对照记录成功，准备验证 - 对照ID: {new_compare_id}")
                                
                                # 验证插入结果
                                verify_query = """
                                    SELECT 对照ID, 规则ID, 城市
                                    FROM 规则医保编码对照 
                                    WHERE 对照ID = :compare_id
                                """
                                verify_params = {'compare_id': new_compare_id}  # 使用单个值

                                #app.logger.info(f"执行验证查询 - SQL: {verify_query}")
                                #app.logger.info(f"验证参数: {verify_params}")
                                
                                try:
                                    # 执行查询
                                    cursor.execute(verify_query, verify_params)
                                    #app.logger.info("SQL执行成功，准备获取结果")
                                    result = cursor.fetchone()
                                    
                                except Exception as e:
                                    # 详细记录错误信息
                                    error_info = {
                                        'error_type': type(e).__name__,
                                        'error_message': str(e),
                                        'sql': verify_query,
                                        'params': verify_params
                                    }
                                    app.logger.error(f"SQL执行错误详情: {error_info}")
                                    
                                    # 如果是Oracle错误，获取更多信息
                                    if hasattr(e, 'args'):
                                        app.logger.error(f"Oracle错误代码: {e.args[0]}")
                                    
                                    # 返回错误信息给前端
                                    return jsonify({
                                        'success': False,
                                        'error': str(e),
                                        'error_details': error_info
                                    }), 500

                                if result:
                                   #app.logger.info(f"验证成功 - 对照ID: {result[0]}, 规则ID: {result[1]}, 城市: {result[2]}")
                                   pass
                                else:
                                    raise Exception(f"验证失败 - 未找到对照ID: {new_compare_id} 的记录")
                                    
                            except Exception as e:
                                conn.rollback()
                                app.logger.error(f"医保编码对照处理失败: {str(e)}")
                                app.logger.error(f"SQL: {compare_query}")
                                app.logger.error(f"参数: {compare_params}")
                                raise Exception(f"医保编码对照处理失败: {str(e)}")
                        success_count += 1
                        
                    except Exception as e:
                        error_count += 1
                        error_messages.append(f"第 {index} 条规则导入失败: {str(e)}")
                        continue
                
                conn.commit()
                
                # 构建详细的响应消息
                message = []
                if success_count > 0:
                    message.append(f'成功导入 {success_count} 条')
                if skip_rule_count > 0:
                    message.append(f'跳过 {skip_rule_count} 条重复规则')
                if skip_city_count > 0:
                    message.append(f'跳过 {skip_city_count} 条重复城市对照')
                if error_count > 0:
                    message.append(f'失败 {error_count} 条')
                
                return jsonify({
                    'success': True,
                    'message': '，'.join(message),
                    'details': {
                        'success_count': success_count,
                        'skip_rule_count': skip_rule_count,
                        'skip_city_count': skip_city_count,
                        'error_count': error_count,
                        'skipped_rules': skipped_rules,
                        'errors': error_messages if error_messages else None
                    }
                })
                
    except Exception as e:
        app.logger.error(f"导入规则失败: {str(e)}")
        return jsonify({'error': f'导入失败: {str(e)}', 'success': False}), 500

@app.route('/api/rules/template', methods=['GET'])
def download_rule_template():
    try:
        # 创建一个新的Excel工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "规则导入模板"

        # 定义列名和必填标记
        columns = [
            {"name": "序号", "required": False},
            {"name": "适用范围", "required": False},
            {"name": "城市", "required": True},
            {"name": "规则来源", "required": True},
            {"name": "行为认定", "required": True},
            {"name": "规则名称", "required": True},
            {"name": "规则内涵", "required": True},
            {"name": "医保名称1", "required": False},
            {"name": "医保编码1", "required": False},
            {"name": "类型", "required": False},
            {"name": "违规数量", "required": False},
            {"name": "违规天数", "required": False},
            {"name": "违规小时数", "required": False},
            {"name": "违规金额", "required": False},
            {"name": "年龄", "required": False},
            {"name": "适用年份", "required": False},
            {"name": "备注", "required": False},
            {"name": "发布时间", "required": False},
            {"name": "生效日期", "required": False},
            {"name": "失效日期", "required": False},
            {"name": "涉及科室", "required": False},
            {"name": "规则类型", "required": False}
        ]

        # 写入列名和设置样式
        for col, column in enumerate(columns, 1):
            cell = ws.cell(row=1, column=col)
            column_name = column["name"]
            
            # 如果是必填项，添加红色星号
            if column["required"]:
                cell.value = f"{column_name} *"
                cell.font = openpyxl.styles.Font(bold=True, color="FF0000")  # 红色字体
            else:
                cell.value = column_name
                cell.font = openpyxl.styles.Font(bold=True)

            # 设置背景色
            cell.fill = openpyxl.styles.PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
            
            # 自动调整列宽
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = max(len(column_name) * 2, 15)


        # 创建临时文件
        temp_dir = create_temp_dir()
        temp_file = os.path.join(temp_dir, "规则导入模板_标记红色列为必填项.xlsx")
        
        # 保存文件
        wb.save(temp_file)
        
        # 发送文件
        return send_file(
            temp_file,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name="规则导入模板_标记红色列为必填项.xlsx"
        )
        
    except Exception as e:
        app.logger.error(f"生成模板文件失败: {str(e)}")
        return jsonify({'error': f'生成模板文件失败: {str(e)}', 'success': False}), 500
    finally:
        # 清理临时文件
        try:
            if os.path.exists(temp_file):
                os.remove(temp_file)
            if os.path.exists(temp_dir):
                os.rmdir(temp_dir)
        except Exception as e:
            app.logger.warning(f"清理临时文件失败: {str(e)}")

@app.route('/rule_sql_generator')
def rule_sql_generator():
    """规则SQL生成器页面"""
    return render_template('rule_sql_generator.html')

@app.route('/api/behavior_types', methods=['GET'])
@handle_db_error
def get_behavior_types():
    """获取行为认定类型列表"""
    try:
        with get_connection(pool) as conn:
            query = """
            SELECT DISTINCT 行为认定
            FROM 飞检规则知识库
            ORDER BY 行为认定
            """
            df = execute_rules_query(conn, query)
            types = df['行为认定'].tolist()
            return jsonify({'success': True, 'types': types})
    except Exception as e:
        app.logger.error(f"获取行为认定类型失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
@app.route('/api/city_types', methods=['GET'])
@handle_db_error
def get_city_types():
    """获取城市列表"""
    try:
        with get_connection(pool) as conn:
            query = """
            SELECT DISTINCT 城市
            FROM 规则医保编码对照
            ORDER BY 城市
            """
            df = execute_rules_query(conn, query)
            types = df['城市'].tolist()
            return jsonify({'success': True, 'types': types})
    except Exception as e:
        app.logger.error(f"获取城市列表失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/rule_sources', methods=['GET'])
@handle_db_error
def get_rule_sources():
    """获取规则来源列表"""
    try:
        with get_connection(pool) as conn:
            query = """
            SELECT DISTINCT 规则来源
            FROM 规则医保编码对照
            WHERE 规则来源 IS NOT NULL
            ORDER BY 规则来源
            """
            df = execute_rules_query(conn, query)
            types = df['规则来源'].tolist()
            return jsonify({'success': True, 'types': types})
    except Exception as e:
        app.logger.error(f"获取规则来源列表失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
@app.route('/api/rules/search', methods=['GET'])
@handle_db_error
def search_rules():
    """搜索规则"""
    try:
        # 获取所有搜索参数
        behavior_type = request.args.get('behavior_type', '')
        city = request.args.get('city', '')
        rule_source = request.args.get('rule_source', '')
        rule_name = request.args.get('rule_name', '')
        rule_content = request.args.get('rule_content', '')
        visit_type = request.args.get('visit_type', '')
        type = request.args.get('type', '')
        rule_type = request.args.get('rule_type', '')
        with get_connection(pool) as conn:
            query = """
            SELECT 
                b.对照id as id,
                b.序号_S as 序号,
                行为认定,
                规则内涵,
                --nvl(b.原规则名称,a.规则名称) 规则名称,
                a.规则名称,
                适用范围,
                b.城市,
                b.规则来源,
                医保编码1,
                医保名称1,
                医保编码2,
                医保名称2,
                违规数量,
                违规金额,   
                类型,
                排除诊断,
                排除科室,
                包含诊断,
                包含科室,
                时间类型,
                物价编码,
                备注,
                性别,
                用途
            FROM 飞检规则知识库 a, 规则医保编码对照 b 
            WHERE a.id = b.规则id
            and a.规则类型 is not null and (a.用途=:visit_type or a.用途 is null)
            AND NOT EXISTS (SELECT 1 FROM rule_sql_history WHERE compare_id = b.对照id and visit_type=:visit_type)
            """
            params = {'visit_type': visit_type}
            
            # 添加城市搜索条件
            if city:
                query += " AND b.城市 = :city"
                params['city'] = city
            
            # 添加其他搜索条件
            if behavior_type:
                query += " AND 行为认定 = :behavior_type"
                params['behavior_type'] = behavior_type
            
            if rule_source:
                query += " AND 规则来源 = :rule_source"
                params['rule_source'] = rule_source
            
            if rule_name:
                query += " AND 规则名称 LIKE :rule_name"
                params['rule_name'] = f"%{rule_name}%"
            
            if type:
                query += " AND 类型 = :type"
                params['type'] = type
            if rule_type:
                query += " AND 规则类型 = :rule_type"
                params['rule_type'] = rule_type

            query += " ORDER BY id"
            
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                columns = [col[0].lower() for col in cursor.description]
                rows = cursor.fetchall()
                rules = [dict(zip(columns, row)) for row in rows]
                return jsonify({'success': True, 'rules': rules})
    except Exception as e:
        app.logger.error(f"搜索规则失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/rules/<int:rule_id>', methods=['GET'])
@handle_db_error
def get_rule_details(rule_id):
    """获取规则详情（完整信息）"""
    try:
        query = """
        SELECT 
            b.对照id,
            id as "ID",
            序号,
            规则名称,
            适用范围,
            b.城市,
            b.规则来源,
            行为认定,
            规则内涵,
            涉及科室,
            违规数量,
            违规天数,
            违规小时数,
            违规金额,
            年龄,
            类型,
            医保编码1,
            医保名称1,
            医保编码2,
            医保名称2,
            国家医保编码1,
            国家医保名称1,
            国家医保编码2,
            国家医保名称2,
            排除科室,
            包含科室,
            排除诊断,
            包含诊断,
            时间类型,
            物价编码,
            适用年份,
            用途,
            备注,
            性别
        FROM 飞检规则知识库 a ,规则医保编码对照 b 
            where a.id=b.规则id
        and ID = :rule_id
        """
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, {'rule_id': rule_id})
                columns = [col[0] for col in cursor.description]
                row = cursor.fetchone()
                
                if not row:
                    return jsonify({
                        'error': '未找到指定规则'
                    }), 404
                
                # 构建结果字典，保持原始列名
                result = dict(zip(columns, row))
                
                # 处理所有字段的 None 值
                for key in result:
                    if result[key] is None:
                        result[key] = ''
                
                return jsonify(result)
                
    except Exception as e:
        app.logger.error(f"获取规则详情失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/save_sql', methods=['POST'])
@handle_db_error
def save_sql():
    """保存SQL语句"""
    try:
        data = request.json
        sql = data.get('sql')
        rule_id = data.get('rule_id')

        if not sql or not rule_id:
            return jsonify({'success': False, 'error': '参数不完整'}), 400

        with get_connection(pool) as conn:
            query = """
            INSERT INTO rule_sql_history (rule_id, sql_content, create_time)
            VALUES (?, ?, CURRENT_TIMESTAMP)
            """
            execute_rules_query(conn, query, [rule_id, sql])
            return jsonify({'success': True})
    except Exception as e:
        app.logger.error(f"保存SQL失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/database_instances', methods=['GET'])
@handle_db_error
def get_database_instances():
    """获取可用的数据库实例列表"""
    try:
        instances = {
            'oracle': [
                {
                    'id': 'default',
                    'name': 'Oracle默认实例 (127.0.0.1/orcl)',
                    'description': '本地Oracle数据库'
                }
            ],
            'postgresql': [
                {
                    'id': 'default',
                    'name': 'PostgreSQL默认实例 (*************)',
                    'description': '默认PostgreSQL数据库'
                }
            ]
        }

        # 从配置文件读取额外的实例
        if 'oracle_pools' in config.sections():
            for pool_name in config.options('oracle_pools'):
                pool_config = eval(config.get('oracle_pools', pool_name))
                instances['oracle'].append({
                    'id': pool_name,
                    'name': f'Oracle {pool_name} ({pool_config["dsn"]})',
                    'description': f'Oracle实例 - {pool_config["dsn"]}'
                })

        if 'postgresql_pools' in config.sections():
            for pool_name in config.options('postgresql_pools'):
                pool_config = eval(config.get('postgresql_pools', pool_name))
                instances['postgresql'].append({
                    'id': pool_name,
                    'name': f'PostgreSQL {pool_name} ({pool_config["host"]})',
                    'description': f'PostgreSQL实例 - {pool_config["host"]}'
                })

        return jsonify({'success': True, 'instances': instances})
    except Exception as e:
        app.logger.error(f"获取数据库实例列表失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/database_schemas', methods=['POST'])
@handle_db_error
def get_database_schemas():
    """获取指定数据库实例中的所有schema"""
    try:
        data = request.json
        database = data.get('database', 'pg')
        host = data.get('host', 'default')

        schemas = []

        if database.lower() == 'oracle':
            # 获取Oracle实例中的所有schema
            if host == 'default':
                # 使用默认Oracle连接
                with get_connection(pool) as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("""
                            SELECT username FROM all_users
                            WHERE username NOT IN ('ANONYMOUS','APPQOSSYS','AUDSYS','CTXSYS','DBSFWUSER','DBSNMP','DIP','DVF','DVSYS','ENGINE','GGSYS','GSMADMIN_INTERNAL','GSMCATUSER','GSMROOTUSER','GSMUSER','HR','LBACSYS','MDDATA','MDSYS','OJVMSYS','OLAPSYS','ORACLE_OCM','ORDDATA','ORDPLUGINS','ORDSYS','OUTLN','REMOTE_SCHEDULER_AGENT','SI_INFORMTN_SCHEMA','SYS','SYS$UMF','SYSBACKUP','SYSDG','SYSKM','SYSRAC','SYSTEM','WMSYS','XDB','XS$NULL')
                            ORDER BY username
                        """)
                        rows = cursor.fetchall()
                        schemas = [row[0] for row in rows]
            else:
                # 使用指定主机的Oracle连接
                try:
                    # 使用默认的Oracle用户名和密码，但连接到指定主机
                    dsn = f"{host}:1521/orcl"  # 默认端口和服务名
                    with oracledb.connect(
                        user="datachange",  # 默认用户名
                        password="drgs2019",  # 默认密码
                        dsn=dsn
                    ) as conn:
                        with conn.cursor() as cursor:
                            cursor.execute("""
                                SELECT username FROM all_users
                                WHERE username NOT IN ('ANONYMOUS','APPQOSSYS','AUDSYS','CTXSYS','DBSFWUSER','DBSNMP','DIP','DVF','DVSYS','ENGINE','GGSYS','GSMADMIN_INTERNAL','GSMCATUSER','GSMROOTUSER','GSMUSER','HR','LBACSYS','MDDATA','MDSYS','OJVMSYS','OLAPSYS','ORACLE_OCM','ORDDATA','ORDPLUGINS','ORDSYS','OUTLN','REMOTE_SCHEDULER_AGENT','SI_INFORMTN_SCHEMA','SYS','SYS$UMF','SYSBACKUP','SYSDG','SYSKM','SYSRAC','SYSTEM','WMSYS','XDB','XS$NULL')
                                ORDER BY username
                            """)
                            rows = cursor.fetchall()
                            schemas = [row[0] for row in rows]
                except Exception as e:
                    return jsonify({'success': False, 'error': f'连接Oracle主机 {host} 失败: {str(e)}'}), 500
        else:
            # 获取PostgreSQL实例中的所有schema
            if host == 'default':
                # 使用默认PostgreSQL连接
                with get_pg_connection(poolpg) as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'pg_temp_1', 'pg_toast_temp_1') ORDER BY schema_name")
                        rows = cursor.fetchall()
                        schemas = [row[0] for row in rows]
            else:
                # 使用指定主机的PostgreSQL连接
                try:
                    import psycopg
                    with psycopg.connect(
                        host=host,
                        port=5432,  # 默认端口
                        dbname="databasetools",  # 默认数据库名
                        user="postgres",  # 默认用户名
                        password="P@ssw0rd"  # 默认密码
                    ) as conn:
                        with conn.cursor() as cursor:
                            cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'pg_temp_1', 'pg_toast_temp_1') ORDER BY schema_name")
                            rows = cursor.fetchall()
                            schemas = [row[0] for row in rows]
                except Exception as e:
                    return jsonify({'success': False, 'error': f'连接PostgreSQL主机 {host} 失败: {str(e)}'}), 500

        return jsonify({'success': True, 'schemas': schemas})

    except Exception as e:
        app.logger.error(f"获取数据库schema列表失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/rules/execute_sql', methods=['POST'])
@handle_db_error
def execute_sql():
    """执行SQL语句"""
    try:
        data = request.json
        sql = data.get('sql')
        database = data.get('database', 'pg')  # 默认使用PostgreSQL
        host = data.get('host', 'default')  # 默认使用默认主机
        schema = data.get('schema', '')  # 可选的schema

        if not sql:
            return jsonify({'success': False, 'error': 'SQL语句不能为空'}), 400

        # 为了安全起见，禁止执行危险的SQL操作
        sql_upper = sql.strip().upper()
        dangerous_keywords = ['UPDATE', 'DELETE', 'INSERT', 'DROP', 'CREATE', 'ALTER', 'TRUNCATE', 'GRANT', 'REVOKE']

        # 检查是否包含危险关键词
        for keyword in dangerous_keywords:
            if keyword in sql_upper:
                return jsonify({'success': False, 'error': f'出于安全考虑，不允许执行包含 {keyword} 的语句'}), 400

        # 根据选择的数据库和主机执行SQL
        if database.lower() == 'oracle':
            # 使用Oracle数据库
            if host == 'default':
                # 使用默认Oracle连接池
                with get_connection(pool) as conn:
                    with conn.cursor() as cursor:
                        # 如果指定了schema，切换到该schema
                        if schema:
                            cursor.execute(f"ALTER SESSION SET CURRENT_SCHEMA = {schema}")

                        # 为了提高查询速度，自动添加行数限制
                        limited_sql = sql.strip()
                        if limited_sql.endswith(';'):
                            limited_sql = limited_sql[:-1]  # 移除末尾分号

                        # 对于Oracle，使用子查询包装的方式添加ROWNUM限制
                        if 'SELECT' in limited_sql.upper() and 'ROWNUM' not in limited_sql.upper():
                            limited_sql = f"SELECT * FROM ({limited_sql}) WHERE ROWNUM <= 10"

                        cursor.execute(limited_sql)

                        # 获取查询结果
                        columns = [col[0] for col in cursor.description] if cursor.description else []
                        rows = cursor.fetchall()

                        schema_info = f" - {schema}" if schema else ""
                        return jsonify({
                            'success': True,
                            'database': f'Oracle (默认主机){schema_info}',
                            'host': 'default',
                            'schema': schema,
                            'affected_rows': len(rows),
                            'columns': columns,
                            'data': rows[:20]  # 限制返回前20行数据
                        })
            else:
                # 使用指定主机的Oracle连接
                try:
                    dsn = f"{host}:1521/orcl"
                    with oracledb.connect(
                        user="datachange",
                        password="drgs2019",
                        dsn=dsn
                    ) as conn:
                        with conn.cursor() as cursor:
                            # 如果指定了schema，切换到该schema
                            if schema:
                                cursor.execute(f"ALTER SESSION SET CURRENT_SCHEMA = {schema}")

                            # 为了提高查询速度，自动添加行数限制
                            limited_sql = sql.strip()
                            if limited_sql.endswith(';'):
                                limited_sql = limited_sql[:-1]  # 移除末尾分号

                            # 对于Oracle，使用子查询包装的方式添加ROWNUM限制
                            if 'SELECT' in limited_sql.upper() and 'ROWNUM' not in limited_sql.upper():
                                limited_sql = f"SELECT * FROM ({limited_sql}) WHERE ROWNUM <= 10"

                            cursor.execute(limited_sql)

                            # 获取查询结果
                            columns = [col[0] for col in cursor.description] if cursor.description else []
                            rows = cursor.fetchall()

                            schema_info = f" - {schema}" if schema else ""
                            return jsonify({
                                'success': True,
                                'database': f'Oracle ({host}){schema_info}',
                                'host': host,
                                'schema': schema,
                                'affected_rows': len(rows),
                                'columns': columns,
                                'data': rows[:100]  # 限制返回前100行数据
                            })
                except Exception as e:
                    return jsonify({'success': False, 'error': f'连接Oracle主机 {host} 失败: {str(e)}'}), 500
        else:
            # 使用PostgreSQL数据库
            if host == 'default':
                # 使用默认PostgreSQL连接池
                with get_pg_connection(poolpg) as conn:
                    with conn.cursor() as cursor:
                        # 如果指定了schema，设置search_path
                        if schema:
                            cursor.execute(f"SET search_path TO \"{schema}\", public")
                            # 验证search_path是否设置成功
                            cursor.execute("SHOW search_path")
                            current_path = cursor.fetchone()[0]
                            app.logger.info(f"PostgreSQL search_path已设置为: {current_path}")

                        # 为了提高查询速度，自动添加行数限制
                        limited_sql = sql.strip()
                        if not limited_sql.upper().endswith(';'):
                            limited_sql += ';'
                        # 对于PostgreSQL，添加LIMIT限制
                        if 'SELECT' in limited_sql.upper() and 'LIMIT' not in limited_sql.upper():
                            limited_sql = limited_sql.replace(';', ' LIMIT 10;')

                        cursor.execute(limited_sql)

                        # 获取查询结果
                        columns = [col[0] for col in cursor.description] if cursor.description else []
                        rows = cursor.fetchall()

                        schema_info = f" - {schema}" if schema else ""
                        return jsonify({
                            'success': True,
                            'database': f'PostgreSQL (默认主机){schema_info}',
                            'host': 'default',
                            'schema': schema,
                            'affected_rows': len(rows),
                            'columns': columns,
                            'data': rows[:100]  # 限制返回前100行数据
                        })
            else:
                # 使用指定主机的PostgreSQL连接
                try:
                    import psycopg
                    with psycopg.connect(
                        host=host,
                        port=5432,
                        dbname="databasetools",
                        user="postgres",
                        password="P@ssw0rd"
                    ) as conn:
                        with conn.cursor() as cursor:
                            # 如果指定了schema，设置search_path
                            if schema:
                                cursor.execute(f"SET search_path TO \"{schema}\", public")
                                # 验证search_path是否设置成功
                                cursor.execute("SHOW search_path")
                                current_path = cursor.fetchone()[0]
                                app.logger.info(f"PostgreSQL search_path已设置为: {current_path}")

                            # 为了提高查询速度，自动添加行数限制
                            limited_sql = sql.strip()
                            if not limited_sql.upper().endswith(';'):
                                limited_sql += ';'
                            # 对于PostgreSQL，添加LIMIT限制
                            if 'SELECT' in limited_sql.upper() and 'LIMIT' not in limited_sql.upper():
                                limited_sql = limited_sql.replace(';', ' LIMIT 10;')

                            cursor.execute(limited_sql)

                            # 获取查询结果
                            columns = [col[0] for col in cursor.description] if cursor.description else []
                            rows = cursor.fetchall()

                            schema_info = f" - {schema}" if schema else ""
                            return jsonify({
                                'success': True,
                                'database': f'PostgreSQL ({host}){schema_info}',
                                'host': host,
                                'schema': schema,
                                'affected_rows': len(rows),
                                'columns': columns,
                                'data': rows[:100]  # 限制返回前100行数据
                            })
                except Exception as e:
                    return jsonify({'success': False, 'error': f'连接PostgreSQL主机 {host} 失败: {str(e)}'}), 500

    except Exception as e:
        app.logger.error(f"执行SQL失败: {str(e)}")
        return jsonify({'success': False, 'error': f'SQL执行失败: {str(e)}'}), 500

@app.route('/api/sql_generator/rules/<int:rule_id>', methods=['GET'])
@handle_db_error
def get_rule_for_sql_generator(rule_id):
    """获取用于SQL生成器的规则信息"""
    try:
        with get_connection(pool) as conn:
            query = """
            SELECT id, 行为认定 , 规则内涵 ,
                   医保编码1, 医保名称1, 医保编码2, 医保名称2,
                   违规天数, 违规数量, 违规小时数, 违规金额
            FROM 飞检规则知识库 a, 规则医保编码对照 b 
           where a.id=b.规则id
            and 对照id = ? 
            """
            df = execute_rules_query(conn, query, [rule_id])
            if df.empty:
                return jsonify({'success': False, 'error': '规则不存在'}), 404
            
            rule = df.iloc[0].to_dict()
            return jsonify({'success': True, 'rule': rule})
    except Exception as e:
        app.logger.error(f"获取规则详情失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/generate_rule_sql', methods=['POST'])
@handle_db_error
def generate_rule_sql_endpoint():
    """根据规则生成SQL并保存到数据库"""
    try:
        data = request.get_json()
        rule_ids = data.get('rule_ids', [])
        template_paths = data.get('template_path')
        visit_type = data.get('visit_type', '门诊')  # 获取就诊类型
        selected_rule_type = data.get('selected_rule_type', '')  # 项目名称/项目编码/国家编码
        sql_type = data.get('selected_db_type', '')      # PostgreSQL/Oracle
        if not rule_ids:
            return jsonify({'success': False, 'error': '未选择规则'}), 400
        generated_sqls = []
        with get_connection(pool) as conn:
            rules_query = """
            SELECT 
                   对照id,
                   适用范围,
                   城市,
                   规则来源,
                   b.序号_S as 序号,
                   行为认定,
                   nvl(b.原规则名称,a.规则名称) as 规则名称,
                   规则内涵,
                   发布时间,
                   生效日期,
                   失效日期,
                   涉及科室,
                   备注,
                   医保编码1,
                   医保名称1,
                   医保编码2,
                   医保名称2,
                   国家医保编码1,
                   国家医保名称1,
                   国家医保编码2,
                   国家医保名称2,
                   物价编码,
                   违规数量,
                   违规天数,
                   排除科室,
                   包含科室,
                   排除诊断,
                   包含诊断,
                   时间类型,
                   id,
                   违规小时数,
                   类型,
                   规则类型,
                   违规金额,
                   年龄,
                   适用年份,
                   用途,
                   性别
            FROM 飞检规则知识库 a ,规则医保编码对照 b 
            WHERE a.id=b.规则id
            and 对照id IN ({})
            """.format(','.join(':{}'.format(i) for i in range(len(rule_ids))))
            
            # 构建参数字典
            params = {str(i): rule_id for i, rule_id in enumerate(rule_ids)}
            with conn.cursor() as cursor:
                cursor.execute(rules_query, params)
                columns = [col[0] for col in cursor.description]
                rules = [dict(zip(columns, row)) for row in cursor.fetchall()]
                if not rules:
                    return jsonify({'success': False, 'error': '未找到选中的规则'}), 404
                for rule in rules:
                    # 确保visit_type字段传递到每个rule
                    rule['visit_type'] = visit_type
                    if not (rule.get('医保名称1') or rule.get('医保名称2')):
                        app.logger.info(f"规则: {rule.get('规则名称', '')} 因医保名称内容为空被跳过")
                        continue
                    rule_name = f"{rule['城市']}_{visit_type}_{rule['序号']}_{rule['规则名称']}"
                    rule['规则名称'] = rule_name
                    sql, template_name = generate_rule_sql(rule, template_paths)
                    if sql:
                        sql_with_comments = f"""
-- ============================================================
-- 序号: {rule.get('序号', '')}
-- 规则名称: {rule_name}  
-- 城市: {rule.get('城市', '')}
-- 规则来源: {rule.get('规则来源', '')}
-- 行为认定: {rule.get('行为认定', '')}
-- 医保名称1: {rule.get('医保名称1', '')}
-- 医保名称2(违规项): {rule.get('医保名称2', '')}
-- ============================================================
{sql}
"""
                        generated_sqls.append(sql_with_comments)
                        
                        try:
                            # 将SQL内容分块处理，避免超出Oracle的参数大小限制
                            # 每个块最大为4000字符（Oracle的VARCHAR2限制）
                            chunk_size = 4000
                            sql_chunks = [sql_with_comments[i:i+chunk_size] 
                                         for i in range(0, len(sql_with_comments), chunk_size)]
                            
                            # 首先插入一个空的CLOB
                            insert_query = """
                            INSERT INTO rule_sql_history (
                                compare_id, rule_id, sql_content, city, create_time, rule_name,
                                rule_content, rule_type, rule_source, medical_behavior, template_name,
                                visit_type, types, appl_scope, selected_rule_type, sql_type
                            ) VALUES (
                                :compare_id, :rule_id, EMPTY_CLOB(), :city, :create_time, :rule_name,
                                :rule_content, :rule_type, :rule_source, :medical_behavior, :template_name,
                                :visit_type, :types, :appl_scope, :selected_rule_type, :sql_type
                            ) RETURNING ROWID INTO :row_id
                            """
                            
                            row_id_var = cursor.var(str)
                            # 将selected_rule_type转换为中文名称
                            selected_rule_type_map = {
                                'name': '项目名称',
                                'code': '项目编码',
                                'national_code': '国家编码'
                            }
                            selected_rule_type = selected_rule_type_map.get(selected_rule_type, selected_rule_type)

                            cursor.execute(insert_query, {
                                'compare_id': rule['对照ID'],
                                'rule_id': rule['ID'],
                                'city': rule['城市'],
                                'create_time': datetime.now(),
                                'rule_name': rule['规则名称'],
                                'rule_content': rule['规则内涵'],
                                'rule_type': rule['类型'],
                                'rule_source': rule['规则来源'],
                                'medical_behavior': rule['行为认定'],
                                'template_name': template_name or '未知模板',
                                'visit_type': visit_type,
                                'types': rule['规则类型'],
                                'appl_scope': rule['适用范围'],
                                'selected_rule_type': selected_rule_type,
                                'sql_type': sql_type,
                                'row_id': row_id_var
                            })
                            
                            app.logger.info(f"规则: {rule['规则名称']}插入成功")
                            # 获取插入行的ROWID
                            row_id = row_id_var.getvalue()[0]
                            
                            # 然后使用UPDATE语句和DBMS_LOB.WRITEAPPEND更新CLOB内容
                            for i, chunk in enumerate(sql_chunks):
                                if i == 0:
                                    # 第一个块使用普通UPDATE
                                    update_query = """
                                    UPDATE rule_sql_history
                                    SET sql_content = :chunk
                                    WHERE ROWID = :row_id
                                    """
                                    cursor.execute(update_query, {'chunk': chunk, 'row_id': row_id})
                                else:
                                    # 后续块使用DBMS_LOB.WRITEAPPEND
                                    append_query = """
                                    DECLARE
                                        l_clob CLOB;
                                    BEGIN
                                        SELECT sql_content INTO l_clob
                                        FROM rule_sql_history
                                        WHERE ROWID = :row_id
                                        FOR UPDATE;
                                        
                                        DBMS_LOB.WRITEAPPEND(l_clob, :chunk_len, :chunk);
                                        
                                        COMMIT;
                                    END;
                                    """
                                    cursor.execute(append_query, {
                                        'row_id': row_id,
                                        'chunk_len': len(chunk),
                                        'chunk': chunk
                                    })
                        except Exception as e:
                            app.logger.error(f"保存SQL历史记录失败: {str(e)}")
                
                # 提交事务
                conn.commit()
                
                # 返回生成的SQL（用分号和换行符连接）
                final_sql = ';\n\n'.join(generated_sqls)
                return jsonify({
                    'success': True,
                    'sql': final_sql
                })
    except Exception as e:
        app.logger.error(f"生成SQL失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

def load_sql_templates(template_paths) -> Dict[str, str]:
    """从指定目录加载所有 SQL 模板文件"""
    templates = {}
    try:

        template_path = os.path.join('templates', template_paths)
        if not os.path.exists(template_path):
            os.makedirs(template_path)
            app.logger.warning(f"创建模板目录: {template_path}")
                
        for filename in os.listdir(template_path):
            if filename.endswith(".sql"):
                with open(os.path.join(template_path, filename), "r", encoding="utf-8") as f:
                    template_name = filename.replace(".sql", "")
                    templates[template_name] = f.read().strip()
                        
        if not templates:
            app.logger.warning(f"在指定目录中未找到SQL模板文件: {template_paths}")
        return templates
        
    except Exception as e:
        app.logger.error(f"加载SQL模板失败: {str(e)}")
        return {}

def select_template(rule: Dict[str, Any], templates: Dict[str, str]) -> Tuple[Optional[str], Optional[str]]:
    """根据规则选择对应的 SQL 模板，返回(模板内容, 模板名称)"""
    try:
        behavior_type = rule.get("行为认定", "")
        rule_type = rule.get("类型", "")
        time_type = rule.get("时间类型", "")
        
        app.logger.info(f"选择模板 - 行为认定: {behavior_type}, 规则类型: {rule_type}, 时间类型: {time_type}")
        
        # 1. 优先级最高的条件判断
        if rule_type == "全量病例":
            return templates.get("病例提取-根据项目提取"), "病例提取-根据项目提取"
            
        # 重复收费根据时间类型选择不同模板
        if rule_type == "重复收费":
            if time_type == "日":
                return templates.get("重复收费"), "重复收费"
            elif time_type == "小时":
                return templates.get("重复收费_时"), "重复收费_时"
            elif time_type == "分钟":
                return templates.get("重复收费_分"), "重复收费_分"
            elif time_type == "周":
                return templates.get("重复收费_周"), "重复收费_周"
            elif time_type == "月":
                return templates.get("重复收费_月"), "重复收费_月"
            elif time_type == "年":
                return templates.get("重复收费_年"), "重复收费_年"
            elif time_type == "住院期间":
                return templates.get("重复收费_住院期间"), "重复收费_住院期间"
            elif time_type == "审查":
                return templates.get("重复收费_审查"), "重复收费_审查"
            else:
                # 默认使用普通重复收费模板
                return templates.get("重复收费"), "重复收费"
                
        if rule_type == "住院天数":
            return templates.get("超住院天数"), "超住院天数"    
        if rule_type == "合超住院天数":
            return templates.get("合超住院天数"), "合超住院天数"          
        # 2. 需要组合条件判断的情况（行为认定+类型）
        if rule_type == "频次上限":
            return templates.get("住院期间超频次"), "住院期间超频次"
        if rule_type == "天数上限":
            return templates.get("住院期间超天数"), "住院期间超天数"
        if rule_type == "组套收费":
            return templates.get("组套收费"), "组套收费"        
        if rule_type == "小时":
            return templates.get("超每日数量"), "超每日数量"
        if rule_type == "日":
            return templates.get("超每日数量"), "超每日数量"
        if rule_type == "周":
            return templates.get("超每周数量"), "超每周数量"
        if rule_type == "两周":
            return templates.get("超两周数量"), "超两周数量"
        if rule_type == "月":
            return templates.get("超每月数量"), "超每月数量"        
        if rule_type == "年":
            return templates.get("超每年数量"), "超每年数量"
        if rule_type == "金额":
            return templates.get("超每日金额"), "超每日金额"
        if rule_type == "年龄":
            return templates.get("限年龄"), "限年龄"
        if rule_type == "性别":
            return templates.get("限性别"), "限性别"
        # 如果没有找到对应模板，使用通用模板
        template = templates.get("通用模板")
        if template:
            app.logger.info(f"使用通用模板替代: {rule_type}/{behavior_type}")
            return template, "通用模板"
        return None, None
        
    except Exception as e:
        app.logger.error(f"选择SQL模板失败: {str(e)}")
        return None, None

def generate_rule_sql(rule: Dict[str, Any], template_paths: List[str]) -> Tuple[Optional[str], Optional[str]]:
    """根据规则生成 SQL 查询语句，返回(SQL语句, 模板名称)"""
    try:
        # 加载所有模板
        templates = load_sql_templates(template_paths)
        if not templates:
            app.logger.warning(template_paths + "未找到任何SQL模板文件")
            return None, None
        template_content, template_name = select_template(rule, templates)
        if not template_content:
            app.logger.warning(template_paths + "未找到匹配的模板：规则类型=" + rule.get('类型', '') + ", "
                f"行为认定=" + rule.get('行为认定', ''))
            return None, None
        # 直接根据rule中的visit_type判断是否为门诊
        visit_type = rule.get('visit_type') or rule.get('就诊类型')
        is_outpatient = visit_type == '门诊'
        query = render_template_sql(template_content, rule, is_outpatient)
        return query, template_name
    except Exception as e:
        app.logger.error(f"生成规则SQL失败: {str(e)}")
        return None, None

def split_and_join_values(value: str) -> str:
    """将逗号分隔的字符串拆分并转换为SQL查询格式"""
    if not value or not isinstance(value, str):
        return "''"
    
    # 检查是否包含%
    if '%' in value:
        values = [val.strip() for val in value.split(',') if val.strip()]
        like_conditions = [f"B.医保项目名称 LIKE '{val}'" for val in values]
        return ' OR '.join(like_conditions)
    
    # 不包含%的情况，使用原来的逻辑
    values = [f"'{val.strip()}'" for val in value.split(',') if val.strip()]
    return ','.join(values) if values else "''"

def build_ilike_any_array(keywords: str) -> str:
    """
    将字符串转换为SQL ILIKE ANY数组格式
    例：'癌|肿瘤,卵巢，前列腺' -> "ARRAY['%癌%','%肿瘤%','%卵巢%','%前列腺%']"
    """
    if not keywords:
        return "ARRAY[]::text[]"
    # 替换所有分隔符为统一的|
    for sep in ['，', ',', '|']:
        keywords = keywords.replace(sep, '|')
    # 分割并去除空白
    items = [kw.strip() for kw in keywords.split('|') if kw.strip()]
    if not items:
        return "ARRAY[]::text[]"
    # 拼接成SQL数组
    array_str = ",".join([f"'%{kw}%'" for kw in items])
    return f"ARRAY[{array_str}]"

def render_template_sql(template: str, rule: Dict[str, Any], is_outpatient: bool = False) -> str:
    """根据规则动态替换 SQL 模板中的占位符"""
    try:
        # 处理医保项目名称
        medical_items = []
        for i in range(1, 5):  # 支持4个医保项目
            item_name = rule.get(f"医保名称{i}")
            if item_name and isinstance(item_name, str):
                medical_items.extend(item_name.split(','))
        
        medical_items = [f"'{item.strip()}'" for item in medical_items if item.strip()]
        medical_items_str = ','.join(medical_items) if medical_items else "''"
        
        # 处理排除诊断
        exclude_diag = rule.get("排除诊断", "")
        exclude_diag_array = build_ilike_any_array(exclude_diag)
        
        # 处理排除科室
        exclude_dept = rule.get("排除科室", "")
        exclude_dept_array = build_ilike_any_array(exclude_dept)
        
        # 处理包含科室
        include_dept = rule.get("包含科室", "")
        include_dept_array = build_ilike_any_array(include_dept)
        
        # 处理包含诊断
        include_diag = rule.get("包含诊断", "")
        include_diag_array = build_ilike_any_array(include_diag)

        
        # 定义替换规则
        replacements = {
            "{医保项目名称}": medical_items_str,
            "{医保名称1}": split_and_join_values(rule.get('医保名称1', '')),
            "{医保名称2}": split_and_join_values(rule.get('医保名称2', '')),
            "{医保编码1}": split_and_join_values(rule.get('医保编码1', '')),
            "{医保编码2}": split_and_join_values(rule.get('医保编码2', '')),
            "{国家编码1}": split_and_join_values(rule.get('国家医保编码1', '')),
            "{国家编码2}": split_and_join_values(rule.get('国家医保编码2', '')),
            "{国家名称1}": split_and_join_values(rule.get('国家医保名称1', '')),
            "{国家名称2}": split_and_join_values(rule.get('国家医保名称2', '')),
            "{违规数量}": str(rule.get("违规数量") if rule.get("违规数量") is not None else "0"),
            "{违规天数}": str(rule.get("违规天数", "1")),
            "{违规小时数}": str(rule.get("违规小时数", "1")),
            "{违规金额}": str(rule.get("违规金额", "0")),
            "{年龄}": str(rule.get("年龄", "0")),
            "{性别}": str(rule.get("性别", "")),
            "{时间类型}": str(rule.get("时间类型",'')) 
        }
        
        # 只有当排除诊断不为空时才替换1=1
        if exclude_diag and exclude_diag.strip():
            if is_outpatient:
                replacements["1=1"] = f"not (COALESCE(A.诊断名称,'') ILIKE ANY ({exclude_diag_array}))"
            else:
                replacements["1=1"] = f"not (COALESCE(A.出院诊断名称,'') ILIKE ANY ({exclude_diag_array}) OR COALESCE(A.入院诊断名称,'') ILIKE ANY ({exclude_diag_array}))"
        
        # 只有当排除科室不为空时才替换{排除科室}
        if exclude_dept and exclude_dept.strip():
            replacements["2=2"] = f"not COALESCE(B.开单科室名称,'') ILIKE ANY ({exclude_dept_array}) "
        # 只有当包含科室不为空时才替换{包含科室}
        if include_dept and include_dept.strip():
            replacements["3=3"] = f"COALESCE(B.开单科室名称,'') ILIKE ANY ({include_dept_array}) "
        # 只有当包含诊断不为空时才替换{包含诊断}
        if include_diag and include_diag.strip():
            if is_outpatient:
                replacements["4=4"] = f"COALESCE(A.诊断名称,'') ILIKE ANY ({include_diag_array})"
            else:
                replacements["4=4"] = f"(COALESCE(A.出院诊断名称,'') ILIKE ANY ({include_diag_array}) OR COALESCE(A.入院诊断名称,'') ILIKE ANY ({include_diag_array}))"

        # 老替换规则
        #replacements = {
        #    "{医保项目名称}": medical_items_str,
        #    "{医保名称1}": split_and_join_values(rule.get('医保名称1', '')),
        #    "{医保名称2}": split_and_join_values(rule.get('医保名称2', '')),
        #    "{医保编码1}": split_and_join_values(rule.get('医保编码1', '')),
        #    "{医保编码2}": split_and_join_values(rule.get('医保编码2', '')),
        #    "{违规数量}": str(rule.get("违规数量", "1")),
        #    "{违规天数}": str(rule.get("违规天数", "1")),
        #    "{违规小时数}": str(rule.get("违规小时数", "1")),
        #    "{违规金额}": str(rule.get("违规金额", "0")),
        #    "{年龄}": str(rule.get("年龄", "0")),
        #    "{性别}": str(rule.get("性别", "")),
        #    "{排除诊断}": str(rule.get("排除诊断")) if rule.get("排除诊断") else "占位符",
        #    "{排除科室}": str(rule.get("排除科室")) if rule.get("排除科室") else "占位符",
        #    "{包含科室}": str(rule.get("包含科室",'|')) ,
        #   "{包含诊断}": str(rule.get("包含诊断",'|')) ,
        #    "{时间类型}": str(rule.get("时间类型",'')) 
        #}  
     
        # 执行替换
        result = template
        for placeholder, value in replacements.items():
            result = result.replace(placeholder, value)
            
        return result
    except Exception as e:
        app.logger.error(f"渲染SQL模板失败: {str(e)}")
        return template

@app.route('/api/rules')
@handle_db_error
def get_sql_query_rules():
    """获取SQL查询页面的规则列表"""
    try:
        query = """
        SELECT 
            b.对照id as ID,  
            b.规则id
            序号,
            行为认定,
            适用范围,
            规则名称,
            b.城市,
            b.规则来源,
            规则内涵,
            类型,
            规则类型
        FROM 飞检规则知识库 a, 规则医保编码对照 b 
           where a.id=b.规则id
        ORDER BY    b.规则id
        """
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query)
                columns = [col[0].lower() for col in cursor.description]  # 转换为小写
                rows = cursor.fetchall()
                
                # 将查询结果转换为字典列表
                rules = []
                for row in rows:
                    rule = {}
                    for i, col in enumerate(columns):
                        value = row[i]
                        # 处理 None 值和特殊类型
                        if value is None:
                            rule[col] = ''
                        elif isinstance(value, (datetime.date, datetime.datetime)):
                            rule[col] = value.strftime('%Y-%m-%d')
                        else:
                            rule[col] = str(value)
                    rules.append(rule)
                
                app.logger.info(f"成功获取 {len(rules)} 条规则")
                return jsonify(rules)
                
    except Exception as e:
        app.logger.error(f"获取规则列表失败: {str(e)}")
        return jsonify([])

@app.route('/api/sql_history')
@handle_db_error
def get_sql_history():
    """获取SQL历史记录"""
    try:
        city = request.args.get('city', '')
        rule_source = request.args.get('rule_source', '')
        behavior_type = request.args.get('behavior_type', '')
        rule_type = request.args.get('rule_type', '')
        rule_id= request.args.get('rule_id', '')
        rule_name = request.args.get('rule_name', '')
        rule_content = request.args.get('rule_content', '')
        types = request.args.get('types', '')
        
        query = """
       
        SELECT
            compare_id,
            rule_id,
            city,
            TO_CHAR(create_time, 'YYYY-MM-DD HH24:MI:SS') as create_time,
            rule_name,
            rule_content,
            rule_type,
            rule_source,
            medical_behavior,
            template_name,
            visit_type,
            types,
            appl_scope,
            selected_rule_type,
            sql_type
        FROM rule_sql_history
        WHERE 1=1

        """
        params = {}
        
        if rule_name:
            query += " AND rule_name LIKE :rule_name"
            params['rule_name'] = f"%{rule_name}%"
        if city:
            query += " AND city = :city"
            params['city'] = city
        if rule_source:
            query += " AND rule_source = :rule_source"
            params['rule_source'] = rule_source
        if behavior_type:
            query += " AND medical_behavior = :behavior_type"
            params['behavior_type'] = behavior_type
        if rule_content:
            query += " AND rule_content = :rule_content"
            params['rule_content'] = rule_content    
        if rule_type:
            query += " AND rule_type = :rule_type"
            params['rule_type'] = rule_type        
        if types:
            query += " AND types = :types"
            params['types'] = types
        if rule_id:
            # 支持分隔符 ; | , 空格
            id_list = [i for i in re.split(r'[;|,\s]+', rule_id) if i]
            if id_list:
                placeholders = ','.join(f':ruleid_{i}' for i in range(len(id_list)))
                query += f" AND rule_id IN ({placeholders})"
                for idx, val in enumerate(id_list):
                    params[f'ruleid_{idx}'] = val
        query += " ORDER BY create_time DESC"
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                columns = [col[0].lower() for col in cursor.description]
                results = [dict(zip(columns, row)) for row in cursor.fetchall()]
                return jsonify(results)
                
    except Exception as e:
        app.logger.error(f"获取SQL历史记录失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sql_content/<int:rule_id>/<visit_type>')
@handle_db_error
def get_sql_content(rule_id, visit_type):
    """获取指定规则的SQL内容"""
    try:
        # 首先获取CLOB的总长度
        length_query = """
        SELECT DBMS_LOB.GETLENGTH(sql_content) as content_length
        FROM rule_sql_history
        WHERE compare_id = :rule_id and visit_type = :visit_type
        """
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(length_query, {'rule_id': rule_id, 'visit_type': visit_type})
                result = cursor.fetchone()
                
                if not result:
                    return jsonify({'error': 'SQL not found'}), 404
                    
                content_length = result[0]
                if not content_length:
                    return jsonify({'sql_content': ''})
                
                # 使用较小的块大小，避免缓冲区溢出
                chunk_size = 2000
                num_chunks = (content_length + chunk_size - 1) // chunk_size
                
                # 分块读取CLOB内容
                chunk_query = """
                SELECT DBMS_LOB.SUBSTR(sql_content, :chunk_size, :offset) as chunk
                FROM rule_sql_history
                WHERE compare_id = :rule_id and visit_type = :visit_type
                """
                
                sql_parts = []
                for i in range(num_chunks):
                    offset = i * chunk_size + 1
                    cursor.execute(chunk_query, {
                        'rule_id': rule_id,
                        'chunk_size': chunk_size,
                        'offset': offset,
                        'visit_type': visit_type
                    })
                    chunk = cursor.fetchone()[0]
                    if chunk:
                        sql_parts.append(chunk)
                
                sql_content = ''.join(sql_parts)
                return jsonify({'sql_content': sql_content})
                
    except Exception as e:
        app.logger.error(f"获取SQL内容失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/update_sql', methods=['POST'])
@handle_db_error
def update_sql():
    """更新SQL内容"""
    try:
        data = request.json
        app.logger.info(f"收到更新SQL请求，数据: {data}")
        rule_id = data.get('rule_id')
        visit_type = data.get('visit_type')
        sql_content = data.get('sql_content')

        if not rule_id or not sql_content:
            app.logger.error(f"参数不完整: rule_id={rule_id}, sql_content长度={len(sql_content) if sql_content else 0}")
            return jsonify({'success': False, 'error': '参数不完整'}), 400
            
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 分块处理SQL内容
                chunk_size = 4000
                sql_chunks = [sql_content[i:i+chunk_size] 
                            for i in range(0, len(sql_content), chunk_size)]
                
                # 先更新为空CLOB
                row_id_var = cursor.var(str)
                cursor.execute("""
                    UPDATE rule_sql_history 
                    SET sql_content = EMPTY_CLOB()
                    WHERE compare_id = :rule_id and visit_type=:visit_type
                    RETURNING ROWID INTO :row_id
                """, {'rule_id': rule_id, 'visit_type': visit_type, 'row_id': row_id_var})
                
                row_id = row_id_var.getvalue()[0]
                #app.logger.info(f"获取到ROWID: {row_id}")
                
                # 分块更新CLOB内容
                for i, chunk in enumerate(sql_chunks):
                    if i == 0:
                        # 第一个块使用普通UPDATE
                        cursor.execute("""
                            UPDATE rule_sql_history
                            SET sql_content = :chunk
                            WHERE ROWID = :row_id and visit_type=:visit_type and compare_id = :rule_id
                        """, {'rule_id': rule_id, 'visit_type': visit_type,'chunk': chunk, 'row_id': row_id})
                    else:
                        # 后续块使用DBMS_LOB.WRITEAPPEND
                        cursor.execute("""
                            DECLARE
                                l_clob CLOB;
                            BEGIN
                                SELECT sql_content INTO l_clob
                                FROM rule_sql_history
                                WHERE ROWID = :row_id and visit_type=:visit_type and compare_id = :rule_id
                                FOR UPDATE;
                                
                                DBMS_LOB.WRITEAPPEND(l_clob, :chunk_len, :chunk);
                            END;
                        """, {'rule_id': rule_id, 'visit_type': visit_type,
                            'row_id': row_id,
                            'chunk_len': len(chunk),
                            'chunk': chunk
                        })
                
                conn.commit()
                app.logger.info(f"SQL更新成功，rule_id={rule_id}")
                return jsonify({'success': True})
                
    except Exception as e:
        app.logger.error(f"更新SQL失败: {str(e)}")
        app.logger.error(f"错误详情: {traceback.format_exc()}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/filter_options')
@handle_db_error
def get_filter_options():
    """获取筛选选项"""
    try:
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 获取所有唯一值
                queries = {
                    'cities': "SELECT DISTINCT city FROM rule_sql_history WHERE city IS NOT NULL ORDER BY city",
                    'rule_sources': "SELECT DISTINCT rule_source FROM rule_sql_history WHERE rule_source IS NOT NULL ORDER BY rule_source",
                    'rule_types': "SELECT DISTINCT rule_type FROM rule_sql_history WHERE rule_type IS NOT NULL ORDER BY rule_type"
                }
                
                result = {}
                for key, query in queries.items():
                    cursor.execute(query)
                    result[key] = [row[0] for row in cursor.fetchall() if row[0]]
                #app.logger.info(f"获取筛选选项成功: {result}")
                return jsonify(result)
                
    except Exception as e:
        app.logger.error(f"获取筛选选项失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/rule_medical_codes/<int:rule_id>', methods=['GET'])
def get_rule_medical_codes(rule_id):
    try:
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 获取基准规则信息（国家医保编码）
                cursor.execute("""
                    SELECT 国家医保编码1, 国家医保名称1, 国家医保编码2, 国家医保名称2
                    FROM 飞检规则知识库
                    WHERE id = :rule_id
                """, {'rule_id': rule_id})
                base_codes = cursor.fetchone()

                # 获取地区对照编码
                cursor.execute("""
                    SELECT 省份,
                           城市,
                           规则内涵,
                           医保编码1,
                           医保名称1,
                           医保编码2,
                           医保名称2,
                           物价编码
                      FROM 规则医保编码对照
                     WHERE 规则ID = :rule_id
                     ORDER BY 省份, 城市
                """, {'rule_id': rule_id})
                regional_codes = cursor.fetchall()

                return jsonify({
                    'base_codes': {
                        'national_code1': base_codes[0],
                        'national_name1': base_codes[1],
                        'national_code2': base_codes[2],
                        'national_name2': base_codes[3]
                    },
                    'regional_codes': [{
                        'seq_no': row[0],
                        'province': row[1],
                        'city': row[2],
                        'rule_content': row[3],
                        'medical_code1': row[4],
                        'medical_name1': row[5],
                        'medical_code2': row[6],
                        'medical_name2': row[7],
                        'price_code': row[8],
                        'price_name': row[9]
                    } for row in regional_codes]
                })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/rule_medical_codes/<int:rule_id>', methods=['POST'])
@handle_db_error
def update_or_create_rule_medical_codes(rule_id):
    try:
        data = request.json
        city = data.get('城市')
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 首先检查是否存在对应的规则
                check_query = """
                SELECT 对照ID 
                FROM 规则医保编码对照
                WHERE 规则ID = :rule_id AND 城市 = :city
                """
                cursor.execute(check_query, {'rule_id': rule_id, 'city': city})
                existing_rule = cursor.fetchone()

                if existing_rule:
                    # 如果存在，执行更新操作
                    update_query = """
                    UPDATE 规则医保编码对照
                    SET 医保编码1 = :medical_code1,
                        医保名称1 = :medical_name1,
                        医保编码2 = :medical_code2,
                        医保名称2 = :medical_name2,                        
                        物价编码 = :price_code,
                        规则内涵 = :rule_content,
                        更新时间 = SYSDATE
                    WHERE 对照ID = :compare_id
                    """
                    cursor.execute(update_query, {
                        'compare_id': existing_rule[0],
                        'medical_code1': data.get('医保编码1'),
                        'medical_name1': data.get('医保名称1'),
                        'medical_code2': data.get('医保编码2'),
                        'medical_name2': data.get('医保名称2'),
                        'price_code': data.get('物价编码'),
                        'rule_content': data.get('规则内涵')
                    })
                else:
                    # 如果不存在，执行插入操作
                    insert_query = """
                    INSERT INTO 规则医保编码对照 (
                        对照ID, 规则ID, 省份, 城市,规则来源,
                        医保编码1, 医保名称1,
                        医保编码2, 医保名称2,
                        物价编码, 创建时间, 更新时间
                    ) VALUES (
                        规则医保编码对照_SEQ.NEXTVAL, :rule_id, :province, :city,:rule_source,
                        :medical_code1, :medical_name1,
                        :medical_code2, :medical_name2,
                        :price_code, SYSDATE, SYSDATE
                    )
                    """
                    cursor.execute(insert_query, {
                        'rule_id': rule_id,
                        'province': data.get('省份'),
                        'city': city,
                        'rule_source': data.get('规则来源'),
                        'medical_code1': data.get('医保编码1'),
                        'medical_name1': data.get('医保名称1'),
                        'medical_code2': data.get('医保编码2'),
                        'medical_name2': data.get('医保名称2'),
                        'price_code': data.get('物价编码')
                    })

                conn.commit()
                return jsonify({'success': True})

    except Exception as e:
        app.logger.error(f"更新/创建规则医保编码对照失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/rules/city/<string:city>/<int:rule_id>', methods=['GET'])
@handle_db_error
def get_rules_by_city_and_id(city, rule_id):
    try:
        query = """
            SELECT 
                id ,
                序号 ,
                类型 ,
                规则名称 ,
                适用范围 ,
                城市 ,
                b.规则来源 ,
                行为认定 ,
                规则内涵 ,
                涉及科室 ,
                违规数量 ,
                违规天数 ,
                违规小时数 ,
                违规金额 ,
                年龄 ,
                性别 ,
                适用年份 ,
                用途 ,
                医保编码1 ,
                医保名称1 ,
                医保编码2 ,
                医保名称2 ,
                国家医保编码1 ,
                国家医保名称1 ,
                国家医保编码2 ,
                国家医保名称2 ,
                排除科室 ,
                排除诊断,
                包含科室,
                包含诊断,
                时间类型,
                备注
            FROM 飞检规则知识库 a, 规则医保编码对照 b 
            WHERE a.id = b.规则id
            AND b.城市 = :city
            AND a.id = :rule_id
        """
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, {'city': city, 'rule_id': rule_id})
                row = cursor.fetchone()
                
                if not row:
                    return jsonify({'success': False, 'message': '未找到规则'}), 404
                
                # 构建单个规则对象
                rule = {
                    'id': row[0],
                    '序号': row[1] or '',
                    '类型': row[2] or '',
                    '规则名称': row[3] or '',
                    '适用范围': row[4] or '',
                    '城市': row[5] or '',
                    '规则来源': row[6] or '',
                    '行为认定': row[7] or '',
                    '规则内涵': row[8] or '',
                    '涉及科室': row[9] or '',
                    '违规数量': row[10] or '',
                    '违规天数': row[11] or '',
                    '违规小时数': row[12] or '',
                    '违规金额': row[13] or '',
                    '年龄': row[14] or '',
                    '性别': row[15] or '',
                    '适用年份': row[16] or '',
                    '用途': row[17] or '',
                    '医保编码1': row[18] or '',
                    '医保名称1': row[19] or '',
                    '医保编码2': row[20] or '',
                    '医保名称2': row[21] or '',
                    '国家医保编码1': row[22] or '',
                    '国家医保名称1': row[23] or '',
                    '国家医保编码2': row[24] or '',
                    '国家医保名称2': row[25] or '',
                    '排除科室': row[26] or '',
                    '排除诊断': row[27] or '',
                    '包含科室': row[28] or '',
                    '包含诊断': row[29] or '',
                    '时间类型': row[30] or '',
                    '备注': row[31] or ''
                }
                
                return jsonify({'success': True, 'rule': rule})
                
    except Exception as e:
        app.logger.error(f"获取规则详情失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/delete_sql/<int:rule_id>/<visit_type>', methods=['DELETE'])
@handle_db_error
def delete_sql(rule_id, visit_type):
    """删除规则对应的 SQL 记录"""
    try:
        with get_connection(pool) as conn:
            # 删除 SQL 记录
            query = """
            DELETE FROM rule_sql_history
            WHERE compare_id = :rule_id and visit_type = :visit_type
            """
            with conn.cursor() as cursor:
                cursor.execute(query, {'rule_id': rule_id, 'visit_type': visit_type})
            conn.commit()
            return jsonify({
                'success': True,
                'message': 'SQL记录已成功删除'
            })
    except Exception as e:
        app.logger.error(f"删除SQL记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/batch_delete_sql', methods=['POST'])
@handle_db_error
def batch_delete_sql():
    """批量删除SQL记录"""
    try:
        data = request.json
        rules = data.get('rules', [])

        if not rules:
            return jsonify({'success': False, 'error': '没有选择要删除的记录'}), 400

        deleted_count = 0

        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                for rule in rules:
                    compare_id = rule.get('compare_id')
                    visit_type = rule.get('visit_type')

                    if compare_id and visit_type:
                        query = """
                        DELETE FROM rule_sql_history
                        WHERE compare_id = :compare_id AND visit_type = :visit_type
                        """
                        cursor.execute(query, {
                            'compare_id': compare_id,
                            'visit_type': visit_type
                        })
                        deleted_count += cursor.rowcount

            conn.commit()

        app.logger.info(f"批量删除SQL记录成功，删除了 {deleted_count} 条记录")
        return jsonify({
            'success': True,
            'deleted_count': deleted_count,
            'message': f'成功删除 {deleted_count} 条SQL记录'
        })

    except Exception as e:
        app.logger.error(f"批量删除SQL记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
    
@app.route('/api/rules/check-existing', methods=['POST'])
def check_existing_rules():
    try:
        data = request.json
        rule_names = data.get('ruleNames', [])
        city_rules = data.get('cityRules', [])
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 1. 首先检查规则名称是否存在
                rule_names_str = "','".join(rule_names)  # 将数组转换为字符串
                existing_rules_query = f"""
                SELECT DISTINCT 规则名称, ID 
                FROM 飞检规则知识库 
                WHERE 规则名称 IN ('{rule_names_str}')
                """
                cursor.execute(existing_rules_query)
                existing_rules_data = cursor.fetchall()
                
                existing_rules = [row[0] for row in existing_rules_data]
                rule_id_map = {row[0]: row[1] for row in existing_rules_data}
                
                # 2. 只对已存在的规则检查城市
                existing_city_rules = []
                if existing_rules:
                    city_check_query = """
                    SELECT a.规则名称, b.城市
                    FROM 飞检规则知识库 a
                    JOIN 规则医保编码对照 b ON a.id = b.规则id
                    WHERE a.规则名称 = :rule_name
                    AND b.城市 = :city
                    """
                    
                    for city_rule in city_rules:
                        rule_name = city_rule['ruleName']
                        if rule_name in existing_rules:  # 只检查已存在的规则
                            cursor.execute(city_check_query, {
                                'rule_name': rule_name,
                                'city': city_rule['city']
                            })
                            result = cursor.fetchone()
                            if result:
                                existing_city_rules.append({
                                    'ruleName': result[0],
                                    'city': result[1]
                                })
                
                return jsonify({
                    'existingRules': existing_rules,
                    'existingCityRules': existing_city_rules
                })
        
    except Exception as e:
        app.logger.error(f"检查规则存在状态失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


def calculate_similarity(text1, text2):
    """计算两个中文文本的相似度"""
    # 对文本进行分词
    words1 = set(jieba.cut(str(text1)))
    words2 = set(jieba.cut(str(text2)))
    
    # 计算词集合的相似度
    intersection = words1 & words2
    union = words1 | words2
    word_similarity = len(intersection) / len(union) if union else 0
    
    # 计算原始文本的相似度
    text_similarity = SequenceMatcher(None, str(text1), str(text2)).ratio()
    
    # 综合两种相似度，给予不同权重
    final_similarity = (word_similarity * 0.6) + (text_similarity * 0.4)
    
    return final_similarity

@app.route('/api/rules/similar_bak', methods=['POST'])
def get_similar_rules():
    try:
        data = request.json
        rule_name = data.get('rule_name', '')
        
        if not rule_name:
            return jsonify({'success': False, 'error': '规则名称不能为空'})
            
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT id, 规则名称
                    FROM 飞检规则知识库
                """)
                existing_rules = cursor.fetchall()
        
        matches = []
        for existing_rule in existing_rules:
            similarity = calculate_similarity(rule_name, existing_rule[1])
            if similarity >= 0.8:
                matches.append({
                    'id': existing_rule[0],
                    'name': existing_rule[1],
                    'similarity': round(similarity * 100, 2)
                })
        
        matches.sort(key=lambda x: x['similarity'], reverse=True)
        
        return jsonify({
            'success': True,
            'matches': matches
        })
        
    except Exception as e:
        app.logger.error(f"获取相似规则失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/rules/search-import-rule', methods=['POST'])
def search_import_rules():
    """搜索飞检规则知识库中的规则"""
    try:
        data = request.json
        keyword = data.get('keyword', '')
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 构建搜索查询
                query = """
                SELECT a.id, a.规则名称,b.规则来源,b.规则内涵
                FROM 飞检规则知识库 a, 规则医保编码对照 b
                WHERE a.id = b.规则id
                AND 规则名称 LIKE :keyword 
                ORDER BY a.id DESC
                """
                
                cursor.execute(query, {'keyword': f'%{keyword}%'})
                rules = cursor.fetchall()
                
                # 格式化返回结果
                formatted_rules = [{
                    'id': rule[0],
                    '规则名称': rule[1],
                    '规则来源': rule[2],
                    '规则内涵': rule[3]
                } for rule in rules]
                
                return jsonify({
                    'success': True,
                    'rules': formatted_rules
                })
                
    except Exception as e:
        app.logger.error(f"搜索规则失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# SQL性能测试
@app.route('/sql_performance')
def sql_performance():
    """SQL性能测试页面"""
    return render_template('sql_performance.html')
# 使用示例:
@app.route('/api/sql/performance-test', methods=['POST'])
@handle_db_error
def sql_performance_test():
    try:
        db_type = request.form.get('db_type')
        sql_files = request.files.getlist('sql_files')
        results = []

        if db_type == 'postgresql':
            with get_pg_connection(poolpg) as conn:
                conn.execute("SET search_path TO 'LZS_YB_YDSJ_3TO'")

                with conn.cursor(name='performance_cursor') as cursor:  # 使用命名游标
                    
                    for sql_file in sql_files:
                        try:
                            sql_content = sql_file.read().decode('utf-8')
                            start_time = time.time()
                            
                            cursor.execute(sql_content)
                            # 尝试获取一条记录验证查询成功
                            if cursor.description:  # 如果是查询语句
                                cursor.fetchone()  # 只获取一条记录
                                
                            execution_time = time.time() - start_time
                            status = '成功'
                            conn.commit()
                            
                        except Exception as e:
                            execution_time = time.time() - start_time
                            status = f'失败: {str(e)}'
                            conn.rollback()
                            
                        results.append({
                            'filename': sql_file.filename,
                            'execution_time': execution_time,
                            'status': status,
                            'sql': sql_content
                        })


        elif db_type == 'oracle':
            with get_connection(pool) as conn:
                with conn.cursor() as cursor:
                    for sql_file in sql_files:
                        content = sql_file.read().decode('utf-8')
                        sql_statements = [stmt.strip() for stmt in content.split(';') if stmt.strip()]
                        
                        for i, sql in enumerate(sql_statements):
                            start_time = time.time()
                            try:
                                cursor.execute(sql)
                                execution_time = time.time() - start_time
                                status = '成功'
                                conn.commit()
                            except Exception as e:
                                execution_time = time.time() - start_time
                                status = f'失败: {str(e)}'
                                conn.rollback()
                            
                            results.append({
                                'filename': f"{sql_file.filename} ",
                                'execution_time': execution_time,
                                'status': status,
                                'sql': sql
                            })

        # 按执行时间排序结果
        #results.sort(key=lambda x: x['execution_time'], reverse=True)
        return jsonify(results)
        
    except Exception as e:
        app.logger.error(f"SQL性能测试失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sql_templates', methods=['GET'])
@handle_db_error
def get_sql_templates():
    """获取系统SQL模板列表"""
    try:
        # 从templates/rule目录加载所有SQL模板
        template_dir = os.path.join('templates', 'rule')
        templates = []
        
        if os.path.exists(template_dir):
            for filename in os.listdir(template_dir):
                if filename.endswith('.sql'):
                    template_id = filename.replace('.sql', '')
                    # 读取模板文件的第一行作为描述（如果有注释）
                    with open(os.path.join(template_dir, filename), 'r', encoding='utf-8') as f:
                        first_line = f.readline().strip()
                        description = first_line.replace('--', '').strip() if first_line.startswith('--') else template_id
                    
                    templates.append({
                        'id': template_id,
                        'name': description,
                        'filename': filename
                    })
            
            # 按模板名称排序
            templates.sort(key=lambda x: x['name'])
            
            return jsonify({
                'success': True,
                'templates': templates
            })
        else:
            app.logger.warning(f"模板目录不存在: {template_dir}")
            return jsonify({
                'success': True,
                'templates': []
            })
            
    except Exception as e:
        app.logger.error(f"获取SQL模板列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sql_templates/<template_id>', methods=['GET'])
@handle_db_error
def get_sql_template_content(template_id):
    """获取指定SQL模板的内容"""
    try:
        # 安全检查：确保template_id不包含目录遍历
        if '..' in template_id or '/' in template_id or '\\' in template_id:
            return jsonify({
                'success': False,
                'error': '无效的模板ID'
            }), 400
        
        template_path = os.path.join('templates', 'rule', f'{template_id}.sql')
        
        if not os.path.exists(template_path):
            return jsonify({
                'success': False,
                'error': '模板不存在'
            }), 404
            
        # 读取模板内容
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 解析模板信息（从注释中）
            lines = content.split('\n')
            metadata = {
                'description': '',
                'parameters': [],
                'example': ''
            }
            
            for line in lines:
                line = line.strip()
                if line.startswith('--'):
                    line = line[2:].strip()
                    if line.startswith('描述:'):
                        metadata['description'] = line[3:].strip()
                    elif line.startswith('参数:'):
                        metadata['parameters'].append(line[3:].strip())
                    elif line.startswith('示例:'):
                        metadata['example'] = line[3:].strip()
                elif not line.startswith('--'):
                    break
            
            return jsonify({
                'success': True,
                'id': template_id,
                'content': content,
                'metadata': metadata
            })
            
    except Exception as e:
        app.logger.error(f"获取SQL模板内容失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 添加辅助函数用于验证SQL模板格式
def validate_sql_template(content: str) -> Tuple[bool, str]:
    """验证SQL模板的格式是否正确"""
    try:
        # 检查基本语法
        if not content.strip():
            return False, "模板内容不能为空"
            
        # 检查是否包含必要的注释信息
        has_description = False
        for line in content.split('\n'):
            if line.strip().startswith('-- 描述:'):
                has_description = True
                break
                
        if not has_description:
            return False, "模板缺少描述信息"
            
        # 检查SQL语法（简单检查）
        lower_content = content.lower()
        if 'select' not in lower_content:
            return False, "模板必须包含SELECT语句"
            
        return True, ""
        
    except Exception as e:
        return False, f"验证模板失败: {str(e)}"

@app.route('/api/export_system_sql', methods=['POST'])
@handle_db_error
def export_system_sql():
    """导出选中的系统规则SQL到ZIP压缩包"""
    try:
        # 支持表单提交和JSON提交
        if request.is_json:
            data = request.json
            rule_ids = data.get('rule_ids', [])
        else:
            rule_ids_str = request.form.get('rule_ids', '[]')
            rule_ids = json.loads(rule_ids_str)
        
        if not rule_ids:
            return jsonify({'success': False, 'error': '未选择规则'}), 400
        
        # 查询选中规则的SQL内容和相关信息
        with get_connection(pool) as conn:
            # 使用 IN 子句的正确方式
            placeholders = ','.join([str(rule_id) for rule_id in rule_ids])
            
            query = f"""
            SELECT 
                h.compare_id,
                c.序号_S,
                h.sql_content,
                r.规则名称,
                c.城市,
                c.规则来源,
                h.template_name
            FROM rule_sql_history h
            LEFT JOIN 规则医保编码对照 c ON h.compare_id = c.对照id
            LEFT JOIN 飞检规则知识库 r ON c.规则id = r.id
            WHERE h.compare_id IN ({placeholders})
            ORDER BY h.compare_id
            """
            
            with conn.cursor() as cursor:
                cursor.execute(query)
                rules = cursor.fetchall()
                
                if not rules:
                    return jsonify({'success': False, 'error': '未找到选中规则的SQL内容'}), 404
                
                # 创建临时目录存放SQL文件
                temp_dir = tempfile.mkdtemp()
                zip_path = os.path.join(temp_dir, f'system_rules_sql_{len(rules)}_{datetime.now().strftime("%Y%m%d")}.zip')
                
                # 创建ZIP文件
                with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for rule in rules:
                        compare_id = rule[0] if rule[0] else 'unknown'
                        seq = rule[1] if rule[1] else 'unknown'
                        sql = rule[2] if rule[2] else ''
                        rule_name = rule[3] if rule[3] else 'unknown'
                        city = rule[4] if rule[4] else ''
                        rule_source = rule[5] if rule[5] else ''
                        template_name = rule[6] if rule[6] else ''
                        
                        # 构建文件名
                        filename = f"{seq}_{rule_name}_{rule_source}_{template_name}.sql"
                        # 使用sanitize_filename函数处理文件名
                        filename = sanitize_filename(filename)
                        
                        # 添加SQL注释头
                        sql_content = f"""{sql}"""
                        # 将SQL内容添加到ZIP文件
                        zipf.writestr(filename, sql_content)
                
                # 读取ZIP文件内容
                with open(zip_path, 'rb') as f:
                    zip_data = f.read()
                
                # 清理临时文件
                shutil.rmtree(temp_dir)
                
                # 使用ASCII文件名
                ascii_filename = f"system_rules_sql_{len(rules)}_{datetime.now().strftime('%Y%m%d')}.zip"
                
                # 返回ZIP文件
                response = make_response(zip_data)
                response.headers['Content-Type'] = 'application/zip'
                response.headers['Content-Disposition'] = f'attachment; filename="{ascii_filename}"'
                
                return response
                
    except Exception as e:
        app.logger.error(f"导出系统规则SQL失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
@app.route('/api/import_to_pg', methods=['POST'])
@handle_db_error
def import_to_pg():
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'error': '数据不能为空'})

        # 使用PostgreSQL连接池
        with get_pg_connection(poolpg) as conn:
            with conn.cursor() as cursor:
                try:
                    results = {
                        'success': [],
                        'skipped': [],
                        'failed': []
                    }
                    
                    # 处理每条规则
                    rules_data = data if isinstance(data, list) else [data]
                    
                    for rule in rules_data:
                        try:
                            # 首先检查规则名是否已存在
                            check_query = """
                            SELECT id FROM rule_requirement_input_info 
                            WHERE rule_name = %(rule_name)s
                            """
                           # app.logger.info(f"执行检查查询: {check_query}")
                            app.logger.info(f"检查参数: {rule.get('rule_name')}")
                            cursor.execute(check_query, {'rule_name': rule.get('rule_name')})
                            existing_rule = cursor.fetchone()

                            if existing_rule:
                                app.logger.info(f"规则已存在，跳过: {rule.get('rule_name')}")
                                results['skipped'].append(rule.get('rule_name'))
                                continue

                            # 如果规则不存在，执行插入
                            insert_query = """
                            INSERT INTO rule_requirement_input_info (
                                id, rule_name, rule_intension, rule_hierarchy, primary_class,
                                secondary_class, belong_domain, illegal_item, diagnosis_type,
                                policy_basis, execution_result, current_status, status,
                                province_id, province_name, city_id, city_name,
                                hospital_code, hospital_name, demand_proposer, org_code,
                                yb_or_yy, create_by, create_time
                            ) VALUES (
                                %(id)s, %(rule_name)s, %(rule_intension)s, %(rule_hierarchy)s,
                                %(primary_class)s, %(secondary_class)s, %(belong_domain)s,
                                %(illegal_item)s, %(diagnosis_type)s, %(policy_basis)s,
                                %(execution_result)s, %(current_status)s, %(status)s,
                                %(province_id)s, %(province_name)s, %(city_id)s, %(city_name)s,
                                %(hospital_code)s, %(hospital_name)s, %(demand_proposer)s,
                                %(org_code)s, %(yb_or_yy)s, %(create_by)s,
                                CURRENT_TIMESTAMP
                            )
                            """
                           # app.logger.info(f"执行插入查询: {insert_query}")
                           # app.logger.info(f"插入参数: {rule}")
                            
                            cursor.execute(insert_query, rule)
                            results['success'].append(rule.get('rule_name'))
                            app.logger.info(f"成功插入规则: {rule.get('rule_name')}")
                            
                        except Exception as e:
                            app.logger.error(f"插入规则失败: {str(e)}")
                            app.logger.error(f"规则数据: {rule}")
                            app.logger.error(f"错误详情: {traceback.format_exc()}")
                            results['failed'].append({
                                'rule_name': rule.get('rule_name'),
                                'error': str(e)
                            })
                            continue
                    
                    conn.commit()
                    app.logger.info(f"事务提交成功")

                    # 构建响应消息
                    message = []
                    if results['success']:
                        message.append(f"成功导入 {len(results['success'])} 条规则")
                    if results['skipped']:
                        message.append(f"跳过 {len(results['skipped'])} 条已存在的规则")
                    if results['failed']:
                        message.append(f"导入失败 {len(results['failed'])} 条规则")
                    
                    return jsonify({
                        'success': True,
                        'message': '，'.join(message),
                        'details': results
                    })
                    
                except Exception as e:
                    conn.rollback()
                    app.logger.error(f"执行SQL失败: {str(e)}")
                    app.logger.error(f"错误详情: {traceback.format_exc()}")
                    return jsonify({
                        'success': False,
                        'error': f'执行SQL失败: {str(e)}'
                    })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'导入失败: {str(e)}'
        })
# 添加sanitize_filename函数（如果项目中没有）
def sanitize_filename(filename):
    """处理文件名，确保其有效且保留中文字符"""
    # 替换Windows不允许的字符
    filename = re.sub(r'[\\/:*?"<>|]', '_', filename)
    # 限制文件名长度
    if len(filename) > 100:
        name, ext = os.path.splitext(filename)
        filename = name[:100-len(ext)] + ext
    return filename

# 首先尝试在规则医保编码对照表中添加原规则名称字段
# 这个修改可能需要DBA操作，而不是在应用程序中执行
# ALTER TABLE 规则医保编码对照 ADD 原规则名称 VARCHAR2(200)

@app.route('/api/rules/merge', methods=['POST'])
@handle_db_error
def merge_rules():
    """合并两条规则"""
    data = request.get_json()
    source_rule_id = data.get('source_rule_id')
    target_rule_id = data.get('target_rule_id')

    if not source_rule_id or not target_rule_id:
        return jsonify({'success': False, 'error': '未提供源规则ID或目标规则ID'})

    # 存储操作日志
    app.logger.info(f"开始合并规则: 源规则ID={source_rule_id}, 目标规则ID={target_rule_id}")
    
    # 记录无法合并的城市
    skipped_cities = []
    
    try:
        with get_connection(pool) as conn:
            # 开启事务
            conn.autocommit = False
            
            try:
                with conn.cursor() as cursor:
                    # 1. 获取源规则信息
                    cursor.execute("""
                        SELECT 
                                对照id,
                                a.id as rule_id,
                                规则名称,
                                b.城市
                            FROM 飞检规则知识库 a, 规则医保编码对照 b 
                            WHERE a.id = b.规则id
                            AND a.id = :id
                    """, {'id': source_rule_id})
                    source_rules = cursor.fetchall()
                    
                    if not source_rules:
                        return jsonify({'success': False, 'error': '源规则不存在'})
                    
                    # 2. 获取目标规则信息
                    cursor.execute("""
                        SELECT 
                                id as rule_id
                            FROM 飞检规则知识库 a 
                            WHERE a.id = :id
                    """, {'id': target_rule_id})
                    target_rule_result = cursor.fetchone()
                    
                    if not target_rule_result:
                        return jsonify({'success': False, 'error': '目标规则不存在'})
                    
                    # 正确处理目标规则ID - 从元组中提取值
                    target_rule_id = target_rule_result[0]
                    
                    # 3. 获取源规则的所有医保编码对照记录
                    cursor.execute("""
                        SELECT 对照ID, 城市, 规则ID
                        FROM 规则医保编码对照
                        WHERE 规则ID = :rule_id
                    """, {'rule_id': source_rule_id})
                    source_mappings = cursor.fetchall()
                    
                    if not source_mappings:
                        return jsonify({'success': False, 'error': '源规则没有医保编码对照记录'})
                    
                    # 4. 处理每个医保编码对照记录
                    updated_mappings = 0
                    skipped_mappings = 0
                    
                    for mapping in source_mappings:
                        compare_id, city, current_rule_id = mapping
                        
                        # 获取当前记录的规则名称（用于记录原始规则名称）
                        cursor.execute("""
                            SELECT 规则名称
                            FROM 飞检规则知识库
                            WHERE id = :rule_id
                        """, {'rule_id': current_rule_id})
                        rule_name_result = cursor.fetchone()
                        original_rule_name = rule_name_result[0] if rule_name_result else "未知规则"
                        
                        # 检查目标规则在此城市是否已有对照
                        cursor.execute("""
                            SELECT 对照ID
                            FROM 规则医保编码对照
                            WHERE 规则ID = :rule_id AND 城市 = :city
                        """, {
                            'rule_id': target_rule_id,
                            'city': city
                        })
                        target_mapping = cursor.fetchone()
                        
                        if target_mapping:
                            # 如果目标规则已有此城市的对照，记录下来，但继续处理其他城市
                            app.logger.warning(f"目标规则ID={target_rule_id}在城市'{city}'已有对照记录，跳过此城市的合并")
                            skipped_cities.append(city)
                            skipped_mappings += 1
                            continue
                        
                        # 目标规则没有此城市的对照，更新源规则对照的规则ID
                        try:
                            cursor.execute("""
                                UPDATE 规则医保编码对照
                                SET 原规则名称 = :original_name,
                                    规则ID = :target_id,
                                    状态 = 'MERGED',
                                    更新时间 = SYSDATE
                                WHERE 对照ID = :compare_id
                            """, {
                                'original_name': original_rule_name,
                                'target_id': target_rule_id,
                                'compare_id': compare_id
                            })
                            updated_mappings += 1
                        except Exception as update_error:
                            # 如果原规则名称字段不存在，尝试不更新该字段
                            if "ORA-00904" in str(update_error) and "原规则名称" in str(update_error):
                                app.logger.warning(f"原规则名称字段不存在，尝试不更新该字段。错误: {str(update_error)}")
                                cursor.execute("""
                                    UPDATE 规则医保编码对照
                                    SET 规则ID = :target_id,
                                        状态 = 'MERGED',
                                        更新时间 = SYSDATE
                                    WHERE 对照ID = :compare_id
                                """, {
                                    'target_id': target_rule_id,
                                    'compare_id': compare_id
                                })
                                updated_mappings += 1
                            else:
                                # 其他错误，重新抛出
                                raise
                    
                    # 检查是否有成功更新的记录，如果有，则删除源规则
                    if updated_mappings > 0:
                        # 删除飞检规则知识库中的源规则
                        try:
                            cursor.execute("""
                                DELETE FROM 飞检规则知识库
                                WHERE id = :rule_id
                            """, {'rule_id': source_rule_id})
                             
                            rows_deleted = cursor.rowcount
                            app.logger.info(f"从飞检规则知识库中删除了规则ID={source_rule_id}，影响行数: {rows_deleted}")
                             
                        except Exception as delete_error:
                            app.logger.error(f"删除源规则时出错: {str(delete_error)}")
                            # 不要因为删除失败而导致整个事务回滚，继续提交已经更新的映射
                            app.logger.warning("继续提交已更新的映射，但源规则删除失败")
                    
                    # 提交事务
                    conn.commit()
                    
                    # 构建响应消息
                    message = f"规则合并成功。更新了{updated_mappings}个对照记录"
                    if skipped_mappings > 0:
                        message += f"，跳过了{skipped_mappings}个城市的记录（目标规则已有对照）"
                    
                    response_data = {
                        'success': True, 
                        'message': message,
                        'updated_mappings': updated_mappings,
                        'skipped_cities': skipped_cities
                    }
                    
                    app.logger.info(f"规则合并完成: {response_data}")
                    return jsonify(response_data)
            
            except Exception as e:
                # 回滚事务
                conn.rollback()
                # 记录详细错误，以便调试
                app.logger.error(f"合并规则过程中出错: {str(e)}\n{traceback.format_exc()}")
                # 重新抛出异常，让外层try-except捕获
                raise
                
    except oracledb.DatabaseError as db_error:
        error_message = str(db_error)
        # 处理数据库连接错误
        if "not connected" in error_message.lower():
            # 数据库连接已断开
            app.logger.error(f"数据库连接已断开: {error_message}")
            return jsonify({'success': False, 'error': '数据库连接已断开，请重试'})
        else:
            # 其他数据库错误
            app.logger.error(f"数据库错误: {error_message}")
            return jsonify({'success': False, 'error': f'数据库错误: {error_message}'})
    except Exception as e:
        app.logger.error(f"合并规则失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/import_sql_content_old/<int:rule_id>')
@handle_db_error
def get_import_sql_content_old(rule_id):
    """获取用于导入的SQL内容"""
    try:
        
        # 首先获取CLOB的总长度
        length_query = """
        SELECT DBMS_LOB.GETLENGTH(sql_content) as content_length
        FROM rule_sql_history
        WHERE compare_id = :rule_id
        """
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(length_query, {'rule_id': rule_id})
                result = cursor.fetchone()
                
                if not result:
                    return jsonify({'error': 'SQL not found'}), 404
                    
                content_length = result[0]
                if not content_length:
                    return jsonify({'sql_content': ''})
                
                # 使用较小的块大小，避免缓冲区溢出
                chunk_size =2000
                num_chunks = (content_length + chunk_size - 1) // chunk_size
                
                # 分块读取CLOB内容
                chunk_query = """
                SELECT DBMS_LOB.SUBSTR(sql_content, :chunk_size, :offset) as chunk
                FROM rule_sql_history
                WHERE compare_id = :rule_id
                """
                
                sql_parts = []
                for i in range(num_chunks):
                    offset = i * chunk_size + 1
                    cursor.execute(chunk_query, {
                        'rule_id': rule_id,
                        'chunk_size': chunk_size,
                        'offset': offset
                    })
                    chunk = cursor.fetchone()[0]
                    if chunk:
                        sql_parts.append(chunk)
                
                sql_content = ''.join(sql_parts)

                visit_type = request.args.get('visit_type')
                province_id = request.args.get('province_id')
                province_name = request.args.get('province_name')
                city_id = request.args.get('city_id')
                city_name = request.args.get('city_name')
                hospital_code = request.args.get('hospital_code')  
                hospital_name = request.args.get('hospital_name')
                org_code = request.args.get('org_code')
                yb_or_yy = request.args.get('yb_or_yy')

        query = """
        SELECT 
            generate_uuid() as id,
            a.rule_name as rule_name,
            b.规则内涵 as rule_intension,
            decode(a.rule_type,'全量病例',1,2) as rule_hierarchy,
            '1'as primary_class, --一级分类
            decode (a.medical_behavior,'重复收费','1','超标准收费','2','串换项目','3','过度检查','4','过度诊疗','4','过度诊疗','4','分解收费','6','9') as secondary_class, --二级分类 
            nvl(c.涉及科室,'收费管理1') belong_domain,
            医保名称2 as illegal_item,
            decode(:visit_type,'住院','1','门诊','2') as diagnosis_type,
            b.规则依据 policy_basis,
            '0' execution_result,
            '0' current_status,
            '1' status,
            :province_id province_id,
            :province_name province_name,
            :city_id city_id,
            :city_name city_name,
            :hospital_code hospital_code,
            :hospital_name hospital_name,
            '系统管理员' demand_proposer,
            :org_code org_code,
            :yb_or_yy yb_or_yy,
            'sysadmin' create_by,
            :sql_content,
            sysdate create_time,
            b.对照id,
            a.rule_name as 规则名称
        FROM RULE_SQL_HISTORY a, 规则医保编码对照 b,飞检规则知识库 c
        WHERE a.compare_id = b.对照id and b.规则id=c.id
        AND a.compare_id = :rule_id
        """
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, {'rule_id': rule_id, 'sql_content': sql_content, 'visit_type': visit_type, 'province_id': province_id, 'province_name': province_name, 'city_id': city_id, 'city_name': city_name, 'hospital_code': hospital_code, 'hospital_name': hospital_name, 'org_code': org_code, 'yb_or_yy': yb_or_yy})
                result = cursor.fetchone()
                
                if not result:
                    return jsonify({'error': 'SQL not found'}), 404
                
                # 构建返回的数据结构
                sql_data = {
                    'id': result[0],
                    'rule_name': result[1],
                    'rule_intension': result[2],
                    'rule_hierarchy': result[3],
                    'primary_class': result[4],
                    'secondary_class': result[5],
                    'belong_domain': result[6],
                    'illegal_item': result[7],
                    'diagnosis_type': result[8],
                    'policy_basis': result[9],
                    'execution_result': result[10],
                    'current_status': result[11],
                    'status': result[12],
                    'province_id': result[13],
                    'province_name': result[14],
                    'city_id': result[15],
                    'city_name': result[16],
                    'hospital_code': result[17],
                    'hospital_name': result[18],
                    'demand_proposer': result[19],
                    'org_code': result[20],
                    'yb_or_yy': result[21],
                    'create_by': result[22],
                    'sql_content': result[23],
                    'create_time': result[24],
                    'compare_id': result[25],
                    'original_rule_name': result[26]
                }
                
                return jsonify(sql_data)
                
    except Exception as e:
        app.logger.error(f"获取导入SQL内容失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/import_sql_content/<int:rule_id>')
@handle_db_error
def get_import_sql_content(rule_id):
    """获取用于导入的需求内容"""
    try:
        visit_type = request.args.get('visit_type')
        province_id = request.args.get('province_id')
        province_name = request.args.get('province_name')
        city_id = request.args.get('city_id')
        city_name = request.args.get('city_name')
        hospital_code = request.args.get('hospital_code')  
        hospital_name = request.args.get('hospital_name')
        org_code = request.args.get('org_code')
        yb_or_yy = request.args.get('yb_or_yy')

        query = """
        SELECT 
            generate_uuid() as id,
            a.rule_name as rule_name,
            nvl(b.规则内涵,'无') as rule_intension,
            decode(c.规则类型,'定性',1,'定量',2) as rule_hierarchy,
            '1'as primary_class, --一级分类
            decode (a.medical_behavior,'重复收费','1','超标准收费','2','串换项目','3','过度检查','4','过度诊疗','4','过度诊疗','4','分解收费','6','9') as secondary_class, --二级分类 
            nvl(c.涉及科室,'收费管理1') belong_domain,
            nvl(医保名称2,医保名称1) as illegal_item,
            decode(:visit_type,'住院','2','门诊','1') as diagnosis_type,
            b.规则依据 policy_basis,
            '0' execution_result,
            '0' current_status,
            '1' status,
            :province_id province_id,
            :province_name province_name,
            :city_id city_id,
            :city_name city_name,
            :hospital_code hospital_code,
            :hospital_name hospital_name,
            '系统管理员' demand_proposer,
            :org_code org_code,
            :yb_or_yy yb_or_yy,
            'sysadmin' create_by,
            sysdate create_time,
            b.对照id,
            a.rule_name as 规则名称
        FROM RULE_SQL_HISTORY a, 规则医保编码对照 b,飞检规则知识库 c
        WHERE a.compare_id = b.对照id and b.规则id=c.id
        AND a.compare_id = :rule_id AND a.visit_type = :visit_type
        """
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, {'rule_id': rule_id,  'visit_type': visit_type, 'province_id': province_id, 'province_name': province_name, 'city_id': city_id, 'city_name': city_name, 'hospital_code': hospital_code, 'hospital_name': hospital_name, 'org_code': org_code, 'yb_or_yy': yb_or_yy})
                result = cursor.fetchone()
                
                if not result:
                    return jsonify({'error': 'SQL not found'}), 404
                
                # 构建返回的数据结构
                sql_data = {
                    'id': result[0],
                    'rule_name': result[1],
                    'rule_intension': result[2],
                    'rule_hierarchy': result[3],
                    'primary_class': result[4],
                    'secondary_class': result[5],
                    'belong_domain': result[6],
                    'illegal_item': result[7],
                    'diagnosis_type': result[8],
                    'policy_basis': result[9],
                    'execution_result': result[10],
                    'current_status': result[11],
                    'status': result[12],
                    'province_id': result[13],
                    'province_name': result[14],
                    'city_id': result[15],
                    'city_name': result[16],
                    'hospital_code': result[17],
                    'hospital_name': result[18],
                    'demand_proposer': result[19],
                    'org_code': result[20],
                    'yb_or_yy': result[21],
                    'create_by': result[22],
                    'create_time': result[23],
                    'compare_id': result[24],
                    'original_rule_name': result[25]
                }
                
                return jsonify(sql_data)
                
    except Exception as e:
        app.logger.error(f"获取导入SQL内容失败: {str(e)}")
        return jsonify({'error': str(e)}), 500
    
@app.route('/api/import_rule_to_pg', methods=['POST'])
@handle_db_error
def import_rule_to_pg():
    """将规则SQL导入到PostgreSQL数据库"""
    try:
        data = request.json
        if not data or 'rule_ids' not in data:
            return jsonify({'success': False, 'error': '参数不完整'})
            
        rule_ids = data['rule_ids']
        visit_type = data['visit_type']
        imported_rules = []
        
        # 首先从Oracle获取规则信息和SQL内容
        with get_connection(pool) as oracle_conn:
            with oracle_conn.cursor() as cursor:
                # 获取规则信息和SQL内容，包含visit_type
                query = """
                SELECT 
                    a.rule_name,
                    a.sql_content,
                    a.compare_id,
                    a.visit_type
                FROM rule_sql_history a
                WHERE a.compare_id IN ({}) and a.visit_type = :visit_type
                --AND (a.is_imported IS NULL OR a.is_imported != '1')
                """.format(','.join(':' + str(i) for i in range(len(rule_ids))))
                
                params = {str(i): rule_id for i, rule_id in enumerate(rule_ids)}
                params['visit_type'] = visit_type
                #app.logger.info(f"准备导入 {params} 条规则的SQL内容")
                cursor.execute(query, params)
                rules = cursor.fetchall()

                if not rules:
                    return jsonify({
                        'success': False,
                        'error': '所选规则已全部导入或未找到'
                    })

                # 将LOB对象转换为字符串
                processed_rules = []
                for rule in rules:
                    rule_name = rule[0]
                    sql_content = rule[1].read() if hasattr(rule[1], 'read') else str(rule[1])  # 处理LOB对象
                    compare_id = rule[2]
                    visit_type = rule[3]
                    processed_rules.append((rule_name, sql_content, compare_id, visit_type))
        # 更新PostgreSQL数据库
        with get_pg_connection(poolpg) as pg_conn:
            with pg_conn.cursor() as pg_cursor:
                for rule in processed_rules:
                    rule_name, sql_content, compare_id, visit_type = rule
                    # 更新PostgreSQL中的kj_rule表
                    update_query = """
                    UPDATE kj_rule 
                    SET sql_name = %s,
                        modify_time = CURRENT_TIMESTAMP
                    WHERE rule_name = %s
                    RETURNING id
                    """
                    pg_cursor.execute(update_query, (sql_content, rule_name))
                    result = pg_cursor.fetchone()
                    if result:
                        imported_rules.append((compare_id, visit_type))
                        app.logger.info(f"成功导入 {rule_name} 的SQL内容")
                    else:
                        app.logger.warning(f"规则：{rule_name} 未更新。kj_rule表中未找到")
                    
                pg_conn.commit()

        # 更新Oracle中的导入状态
        if imported_rules:
            with get_connection(pool) as oracle_conn:
                with oracle_conn.cursor() as cursor:
                    # 构建IN条件的参数列表
                    conditions = []
                    params = {}
                    for i, (compare_id, visit_type) in enumerate(imported_rules):
                        conditions.append(f"(compare_id = :id{i} AND visit_type = :type{i})")
                        params[f'id{i}'] = compare_id
                        params[f'type{i}'] = visit_type

                    update_query = f"""
                    UPDATE rule_sql_history 
                    SET is_imported = '1',
                        import_time = SYSDATE
                    WHERE {' OR '.join(conditions)}
                    """
                    
                    cursor.execute(update_query, params)
                    app.logger.info(f"成功导入 {len(imported_rules)} 条规则的SQL内容")
                    oracle_conn.commit()
                
        return jsonify({
            'success': True,
            'message': f'成功导入 {len(imported_rules)} 条规则的SQL内容'
        })
        
    except Exception as e:
        app.logger.error(f"导入规则SQL到PG失败: {str(e)}")
        app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'导入失败: {str(e)}'
        })

@app.route('/api/sequence/<sequence_name>', methods=['GET'])
@handle_db_error
def get_sequence_value(sequence_name):
    """获取指定序列的下一个值"""
    try:
        # 处理序列名称以防止SQL注入
        if not re.match(r'^[A-Za-z0-9_]+$', sequence_name):
            return jsonify({'error': '无效的序列名称'}), 400
        
        query = f"SELECT {sequence_name}.NEXTVAL as sequence FROM DUAL"
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query)
                result = cursor.fetchone()
                if result:
                    return jsonify({'sequence': result[0]})
                return jsonify({'error': '获取序列值失败'}), 500
    except Exception as e:
        app.logger.error(f"获取序列值失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/rule_city_mapping', methods=['POST'])
@handle_db_error
def create_rule_city_mapping():
    """创建或更新规则城市对照"""
    try:
        data = request.json
        rule_id = data.get('规则id')
        compare_id = data.get('对照id')
        city = data.get('城市')
        province = data.get('省份')
        
        if not all([rule_id, compare_id, city, province]):
            return jsonify({'error': '缺少必要参数', 'success': False}), 400
        
        # 检查规则是否存在
        check_query = "SELECT 1 FROM 飞检规则知识库 WHERE id = :rule_id"
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(check_query, {'rule_id': rule_id})
                if not cursor.fetchone():
                    return jsonify({'error': '规则不存在', 'success': False}), 404
                
                # 检查对照是否已存在
                check_compare_query = """
                SELECT 1 FROM 规则医保编码对照 
                WHERE 规则id = :rule_id AND 城市 = :city
                """
                cursor.execute(check_compare_query, {'rule_id': rule_id, 'city': city})
                exists = cursor.fetchone()
                
                if exists:
                    # 更新现有记录
                    update_query = """
                    UPDATE 规则医保编码对照
                    SET 对照id = :compare_id,
                        省份 = :province
                    WHERE 规则id = :rule_id AND 城市 = :city
                    """
                    cursor.execute(update_query, {
                        'compare_id': compare_id,
                        'province': province,
                        'rule_id': rule_id,
                        'city': city
                    })
                else:
                    # 创建新记录
                    insert_query = """
                    INSERT INTO 规则医保编码对照 (对照id, 规则id, 城市, 省份) 
                    VALUES (:compare_id, :rule_id, :city, :province)
                    """
                    cursor.execute(insert_query, {
                        'compare_id': compare_id,
                        'rule_id': rule_id,
                        'city': city,
                        'province': province
                    })
                
                conn.commit()
                
        return jsonify({
            'success': True,
            'message': '规则城市对照保存成功'
        })
        
    except Exception as e:
        app.logger.error(f"保存规则城市对照失败: {str(e)}")
        return jsonify({
            'error': str(e),
            'success': False
        }), 500

@app.route('/api/sequences', methods=['GET'])
@handle_db_error
def get_available_sequences():
    """获取数据库中可用的序列列表"""
    try:
        query = """
        SELECT SEQUENCE_NAME 
        FROM USER_SEQUENCES 
        ORDER BY SEQUENCE_NAME
        """
        
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query)
                sequences = [row[0] for row in cursor.fetchall()]
                return jsonify({'sequences': sequences})
    except Exception as e:
        app.logger.error(f"获取序列列表失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/type_types', methods=['GET'])
@handle_db_error
def get_type_types():
    """获取类型类型列表"""
    try:
        with get_connection(pool) as conn:
            query = """
            SELECT DISTINCT 类型
            FROM 飞检规则知识库
            WHERE 类型 IS NOT NULL
            ORDER BY 类型
            """
            df = execute_rules_query(conn, query)
            types = df['类型'].tolist()
            return jsonify({'success': True, 'types': types})
    except Exception as e:
        app.logger.error(f"获取类型类型失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/rule_type_types', methods=['GET'])
@handle_db_error
def get_rule_type_types():
    """获取规则类型列表"""
    try:
        with get_connection(pool) as conn:
            query = """
            SELECT DISTINCT 规则类型
            FROM 飞检规则知识库
            WHERE 规则类型 IS NOT NULL 
            ORDER BY 规则类型
            """
            df = execute_rules_query(conn, query)
            types = df['规则类型'].tolist()
            return jsonify({'success': True, 'types': types})
    except Exception as e:
        app.logger.error(f"获取规则类型失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

PG_CONFIG_FILE = 'config.ini'

@app.route('/api/pg_config', methods=['GET'])
def get_pg_config():
    """获取PG数据库配置"""
    config = configparser.ConfigParser()
    if os.path.exists(PG_CONFIG_FILE):
        config.read(PG_CONFIG_FILE, encoding='utf-8')
        if 'postgresql' in config:
            return jsonify({
                'host': config.get('postgresql', 'host', fallback=''),
                'port': config.get('postgresql', 'port', fallback='5432'),
                'dbname': config.get('postgresql', 'dbname', fallback=''),
                'user': config.get('postgresql', 'user', fallback=''),
                'password': config.get('postgresql', 'password', fallback=''),
                'schema': config.get('postgresql', 'schema', fallback='public')
            })
    return jsonify({'host': '', 'port': '5432', 'dbname': '', 'user': '', 'password': '', 'schema': 'public'})

@app.route('/api/pg_config', methods=['POST'])
def save_pg_config():
    """保存PG数据库配置"""
    data = request.get_json()
    required = ['host', 'port', 'dbname', 'user', 'password', 'schema']
    if not all(k in data for k in required):
        return jsonify({'success': False, 'message': '缺少必要参数'}), 400
    config = configparser.ConfigParser()
    if os.path.exists(PG_CONFIG_FILE):
        config.read(PG_CONFIG_FILE, encoding='utf-8')
    if 'postgresql' not in config:
        config['postgresql'] = {}
    for k in required:
        config['postgresql'][k] = data[k]
    with open(PG_CONFIG_FILE, 'w', encoding='utf-8') as f:
        config.write(f)

    # 重新初始化PG连接池
    try:
        import psycopg_pool
        pg_conninfo = (
            f"host={data['host']} "
            f"port={data['port']} "
            f"dbname={data['dbname']} "
            f"user={data['user']} "
            f"password={data['password']} "
        )
        global poolpg
        poolpg = psycopg_pool.ConnectionPool(
            conninfo=pg_conninfo,
            min_size=2,
            max_size=5,
            timeout=30
        )
    except Exception as e:
        app.logger.error(f"PG连接池重载失败: {str(e)}")
        return jsonify({'success': False, 'message': f'PG连接池重载失败: {str(e)}'}), 500

    return jsonify({'success': True})

# ==================== 医院个性化规则推荐系统 API ====================

@app.route('/hospital_rules')
def hospital_rules_page():
    """医院个性化规则推荐系统页面"""
    return render_template_string(open('page/hospital_rules.html', 'r', encoding='utf-8').read())

@app.route('/api/hospitals', methods=['GET'])
@handle_db_error
def get_hospitals():
    """获取医院列表"""
    try:
        # 首先检查表是否存在
        check_table_query = """
        SELECT COUNT(*) FROM user_tables WHERE table_name = '医院信息表'
        """

        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(check_table_query)
                table_exists = cursor.fetchone()[0] > 0

                if not table_exists:
                    # 表不存在，返回空列表和提示信息
                    return jsonify({
                        'success': True,
                        'hospitals': [],
                        'message': '数据库表尚未创建，请先执行SQL脚本创建表结构'
                    })

                query = """
                SELECT 医院ID, 医院名称, 医院编码, 所在城市, 医院等级, 创建时间
                FROM 医院信息表
                ORDER BY 创建时间 DESC
                """

                cursor.execute(query)
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()

                hospitals = []
                for row in rows:
                    hospital = dict(zip(columns, row))
                    # 处理日期格式
                    if hospital.get('创建时间'):
                        hospital['创建时间'] = hospital['创建时间'].strftime('%Y-%m-%d %H:%M:%S')
                    hospitals.append(hospital)

                return jsonify({
                    'success': True,
                    'hospitals': hospitals
                })

    except Exception as e:
        app.logger.error(f"获取医院列表失败: {str(e)}")
        # 如果是表不存在的错误，返回友好提示
        if 'ORA-00942' in str(e) or '表或视图不存在' in str(e):
            return jsonify({
                'success': True,
                'hospitals': [],
                'message': '数据库表尚未创建，请先执行SQL脚本创建表结构'
            })
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/hospitals', methods=['POST'])
@handle_db_error
def create_hospital():
    """创建医院"""
    try:
        data = request.json

        # 验证必填字段
        if not data.get('医院名称'):
            return jsonify({
                'success': False,
                'error': '医院名称不能为空'
            }), 400

        # 检查医院名称是否已存在
        check_query = "SELECT COUNT(*) FROM 医院信息表 WHERE 医院名称 = :hospital_name"

        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                cursor.execute(check_query, {'hospital_name': data['医院名称']})
                if cursor.fetchone()[0] > 0:
                    return jsonify({
                        'success': False,
                        'error': '医院名称已存在'
                    }), 400

                # 插入新医院
                insert_query = """
                INSERT INTO 医院信息表 (医院ID, 医院名称, 医院编码, 所在城市, 医院等级, 创建时间)
                VALUES (医院信息表_SEQ.NEXTVAL, :医院名称, :医院编码, :所在城市, :医院等级, SYSDATE)
                RETURNING 医院ID INTO :new_id
                """

                params = {
                    '医院名称': data['医院名称'],
                    '医院编码': data.get('医院编码', ''),
                    '所在城市': data.get('所在城市', ''),
                    '医院等级': data.get('医院等级', ''),
                    'new_id': cursor.var(int)
                }

                cursor.execute(insert_query, params)
                new_id = params['new_id'].getvalue()[0] if isinstance(params['new_id'].getvalue(), list) else params['new_id'].getvalue()

            conn.commit()

            return jsonify({
                'success': True,
                'hospital_id': new_id,
                'message': '医院创建成功'
            })

    except Exception as e:
        app.logger.error(f"创建医院失败: {str(e)}")
        # 如果是表不存在的错误，返回友好提示
        if 'ORA-00942' in str(e) or '表或视图不存在' in str(e):
            return jsonify({
                'success': False,
                'error': '数据库表尚未创建，请先执行SQL脚本创建表结构：sql/hospital_rules_tables.sql'
            }), 400
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/hospitals/<int:hospital_id>', methods=['DELETE'])
@handle_db_error
def delete_hospital(hospital_id):
    """删除医院"""
    try:
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 先删除相关的适用规则记录
                cursor.execute("DELETE FROM 医院适用规则表 WHERE 医院ID = :hospital_id", {'hospital_id': hospital_id})

                # 删除相关的收费数据记录
                cursor.execute("DELETE FROM 医院收费数据表 WHERE 医院ID = :hospital_id", {'hospital_id': hospital_id})

                # 删除医院记录
                cursor.execute("DELETE FROM 医院信息表 WHERE 医院ID = :hospital_id", {'hospital_id': hospital_id})

            conn.commit()

            return jsonify({
                'success': True,
                'message': '医院删除成功'
            })

    except Exception as e:
        app.logger.error(f"删除医院失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/hospital-data/preview', methods=['POST'])
@handle_db_error
def preview_hospital_data():
    """预览医院收费数据"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '未找到上传文件'}), 400

        file = request.files['file']
        hospital_id = request.form.get('hospital_id')

        if not hospital_id:
            return jsonify({'success': False, 'error': '缺少医院ID参数'}), 400

        # 创建临时文件
        temp_dir = create_temp_dir()
        temp_file = os.path.join(temp_dir, secure_filename(file.filename))
        file.save(temp_file)

        try:
            # 读取文件
            if file.filename.endswith('.csv'):
                df = pd.read_csv(temp_file, encoding='utf-8')
            else:
                df = pd.read_excel(temp_file)

            # 验证必需字段
            required_fields = ['医保项目编码', '医保项目名称', '收费金额', '收费次数']
            missing_fields = [field for field in required_fields if field not in df.columns]

            if missing_fields:
                return jsonify({
                    'success': False,
                    'error': f'缺少必需字段: {", ".join(missing_fields)}'
                }), 400

            # 数据清洗
            df = df.dropna(subset=['医保项目编码', '医保项目名称'])  # 删除关键字段为空的行
            df['收费金额'] = pd.to_numeric(df['收费金额'], errors='coerce').fillna(0)
            df['收费次数'] = pd.to_numeric(df['收费次数'], errors='coerce').fillna(0)

            # 处理所有NaN值，替换为合适的默认值
            df = df.fillna('')  # 将所有NaN替换为空字符串

            # 转换为字典列表，并进一步清理
            import json
            import math

            cleaned_data = []
            for _, row in df.iterrows():
                record = {}
                for col in df.columns:
                    value = row[col]
                    # 处理各种可能的NaN情况
                    if pd.isna(value) or (isinstance(value, float) and math.isnan(value)):
                        if col in ['收费金额', '收费次数']:
                            record[col] = 0
                        else:
                            record[col] = ''
                    elif isinstance(value, (int, float)):
                        # 确保数值类型正确
                        if math.isfinite(value):
                            record[col] = float(value) if col in ['收费金额'] else int(value) if col in ['收费次数'] else value
                        else:
                            record[col] = 0
                    else:
                        record[col] = str(value) if value is not None else ''

                # 验证记录是否可以序列化为JSON
                try:
                    json.dumps(record)
                    cleaned_data.append(record)
                except (TypeError, ValueError) as e:
                    app.logger.warning(f"跳过无法序列化的记录: {e}")
                    continue

            return jsonify({
                'success': True,
                'data': cleaned_data[:100],  # 只返回前100条用于预览
                'total_count': len(cleaned_data),
                'preview_count': min(100, len(cleaned_data)),
                'full_data': cleaned_data  # 保存完整数据用于后续导入
            })

        finally:
            # 清理临时文件
            try:
                os.remove(temp_file)
                os.rmdir(temp_dir)
            except Exception as e:
                app.logger.warning(f"清理临时文件失败: {str(e)}")

    except Exception as e:
        app.logger.error(f"预览医院数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/hospital-data/import', methods=['POST'])
@handle_db_error
def import_hospital_data():
    """导入医院收费数据"""
    try:
        data = request.json
        hospital_id = data.get('hospital_id')
        records = data.get('data', [])

        if not hospital_id or not records:
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400

        imported_count = 0

        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 先删除该医院的旧数据（如果需要的话，可以根据具体需求调整）
                # 这里暂时不删除旧数据，允许累积导入
                pass

                # 插入新数据
                insert_query = """
                INSERT INTO 医院收费数据表 (
                    收费ID, 医院ID, 医保项目编码, 医保项目名称, 收费金额, 收费次数,
                    数据来源, 创建时间
                ) VALUES (
                    医院收费数据表_SEQ.NEXTVAL, :医院ID, :医保项目编码, :医保项目名称,
                    :收费金额, :收费次数, :数据来源, SYSDATE
                )
                """

                for record in records:
                    try:
                        params = {
                            '医院ID': hospital_id,
                            '医保项目编码': record.get('医保项目编码', ''),
                            '医保项目名称': record.get('医保项目名称', ''),
                            '收费金额': float(record.get('收费金额', 0)),
                            '收费次数': int(record.get('收费次数', 0)),
                            '数据来源': '手动导入'
                        }

                        cursor.execute(insert_query, params)
                        imported_count += 1

                    except Exception as e:
                        app.logger.warning(f"导入单条记录失败: {str(e)}")
                        continue

            conn.commit()

            return jsonify({
                'success': True,
                'imported_count': imported_count,
                'message': f'成功导入 {imported_count} 条记录'
            })

    except Exception as e:
        app.logger.error(f"导入医院数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/hospital-rules/generate', methods=['POST'])
@handle_db_error
def generate_hospital_rules():
    """为医院生成规则推荐"""
    try:
        data = request.json
        hospital_id = data.get('hospital_id')

        if not hospital_id:
            return jsonify({'success': False, 'error': '缺少医院ID'}), 400

        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 获取医院的收费数据中的医保项目
                hospital_items_query = """
                SELECT DISTINCT h.医保项目编码, h.医保项目名称
                FROM 医院收费数据表 h
                WHERE h.医院ID = :hospital_id
                ORDER BY h.医保项目名称
                """

                cursor.execute(hospital_items_query, {'hospital_id': hospital_id})
                hospital_items = cursor.fetchall()

                if not hospital_items:
                    return jsonify({
                        'success': False,
                        'error': '该医院暂无收费数据，请先导入数据'
                    }), 400

                # 获取医院所在城市
                hospital_query = "SELECT 所在城市 FROM 医院信息表 WHERE 医院ID = :hospital_id"
                cursor.execute(hospital_query, {'hospital_id': hospital_id})
                hospital_info = cursor.fetchone()
                hospital_city = hospital_info[0] if hospital_info else ''

                # 高效的规则匹配算法
                recommendations = generate_recommendations_efficiently(cursor, hospital_id, hospital_items)

                conn.commit()

                # 合并相同规则名称的推荐
                merged_recommendations = merge_same_rules(recommendations)

                # 按匹配度排序
                merged_recommendations.sort(key=lambda x: x['匹配度'], reverse=True)

                # 获取限制参数，默认返回所有推荐
                limit = request.json.get('limit', 0)  # 0表示不限制

                if limit > 0:
                    recommendations_to_return = merged_recommendations[:limit]
                else:
                    recommendations_to_return = merged_recommendations

                return jsonify({
                    'success': True,
                    'recommendations': recommendations_to_return,
                    'total_count': len(merged_recommendations),  # 返回总数量
                    'returned_count': len(recommendations_to_return)  # 返回实际数量
                })

    except Exception as e:
        app.logger.error(f"生成规则推荐失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def calculate_match_score(item_name, item_code, med_name1, med_name2, med_code1, med_code2):
    """计算匹配度 - 基于精确匹配"""
    score = 0.0

    # 名称完全匹配（主要匹配条件）
    if item_name and (item_name == med_name1 or item_name == med_name2):
        score = 1.0  # 精确匹配给满分

    # 如果有编码匹配，可以作为额外验证
    if item_code and (item_code == med_code1 or item_code == med_code2):
        score = 1.0  # 编码精确匹配也给满分

    return score

def calculate_match_score_with_split(item_name, item_code, med_name1, med_name2, med_code1, med_code2):
    """计算匹配度 - 支持拆分匹配"""
    score = 0.0

    # 拆分并检查医保名称1
    if item_name and med_name1:
        name1_list = [name.strip() for name in med_name1.split(r'[、,，|;；]') if name.strip()]
        if item_name in name1_list:
            score = 1.0  # 精确匹配给满分

    # 拆分并检查医保名称2
    if score == 0.0 and item_name and med_name2:
        name2_list = [name.strip() for name in med_name2.split(r'[、,，|;；]') if name.strip()]
        if item_name in name2_list:
            score = 1.0  # 精确匹配给满分

    # 拆分并检查医保编码1
    if score == 0.0 and item_code and med_code1:
        code1_list = [code.strip() for code in med_code1.split(r'[、,，|;；]') if code.strip()]
        if item_code in code1_list:
            score = 1.0  # 编码精确匹配也给满分

    # 拆分并检查医保编码2
    if score == 0.0 and item_code and med_code2:
        code2_list = [code.strip() for code in med_code2.split(r'[、,，|;；]') if code.strip()]
        if item_code in code2_list:
            score = 1.0  # 编码精确匹配也给满分

    return score

def generate_recommendations_efficiently(cursor, hospital_id, hospital_items):
    """高效生成规则推荐 - 使用交集算法"""

    # 第一步：构建医院项目集合
    hospital_names = set()
    hospital_codes = set()
    hospital_item_map = {}  # 用于存储项目的详细信息

    for item in hospital_items:
        item_code, item_name = item
        if item_name:
            hospital_names.add(item_name.strip())
        if item_code:
            hospital_codes.add(item_code.strip())

        # 存储项目详细信息
        key = f"{item_name}_{item_code}" if item_code else item_name
        hospital_item_map[key] = {
            'name': item_name,
            'code': item_code
        }

    # 第二步：一次性获取所有规则对照数据
    rule_mapping_query = """
    SELECT r.ID as 规则ID, r.规则名称, r.行为认定, r.类型, r.规则类型, c.规则内涵,
           c.医保名称1, c.医保名称2, c.医保编码1, c.医保编码2,
           c.城市, c.规则来源
    FROM 飞检规则知识库 r
    JOIN 规则医保编码对照 c ON r.ID = c.规则ID
    WHERE (c.医保名称1 IS NOT NULL OR c.医保名称2 IS NOT NULL
           OR c.医保编码1 IS NOT NULL OR c.医保编码2 IS NOT NULL)
    """

    cursor.execute(rule_mapping_query)
    all_rule_mappings = cursor.fetchall()

    # 第三步：构建规则索引和快速查找
    rule_index = {}  # 规则ID -> 规则信息
    name_to_rules = {}  # 医保名称 -> 规则ID列表
    code_to_rules = {}  # 医保编码 -> 规则ID列表

    for mapping in all_rule_mappings:
        rule_id, rule_name, behavior, rule_type, rule_category, rule_content, med_name1, med_name2, med_code1, med_code2, city, rule_source = mapping

        # 存储规则基本信息
        if rule_id not in rule_index:
            rule_index[rule_id] = {
                'rule_id': rule_id,
                'rule_name': rule_name,
                'behavior': behavior,
                'rule_type': rule_type,
                'rule_category': rule_category,
                'rule_content': rule_content,
                'cities': set(),
                'sources': set(),
                'matched_items': set()
            }

        # 添加城市和来源信息
        if city:
            rule_index[rule_id]['cities'].add(city.strip())
        if rule_source:
            rule_index[rule_id]['sources'].add(rule_source.strip())

        # 拆分并索引医保名称1
        if med_name1:
            names = [name.strip() for name in med_name1.split(r'[、,，|;；]') if name.strip()]
            for name in names:
                if name not in name_to_rules:
                    name_to_rules[name] = set()
                name_to_rules[name].add(rule_id)

        # 拆分并索引医保名称2
        if med_name2:
            names = [name.strip() for name in med_name2.split(r'[、,，|;；]') if name.strip()]
            for name in names:
                if name not in name_to_rules:
                    name_to_rules[name] = set()
                name_to_rules[name].add(rule_id)

        # 拆分并索引医保编码1
        if med_code1:
            codes = [code.strip() for code in med_code1.split(r'[、,，|;；]') if code.strip()]
            for code in codes:
                if code not in code_to_rules:
                    code_to_rules[code] = set()
                code_to_rules[code].add(rule_id)

        # 拆分并索引医保编码2
        if med_code2:
            codes = [code.strip() for code in med_code2.split(r'[、,，|;；]') if code.strip()]
            for code in codes:
                if code not in code_to_rules:
                    code_to_rules[code] = set()
                code_to_rules[code].add(rule_id)

    # 第四步：通过交集快速找到匹配的规则
    matched_rules = set()

    # 通过名称匹配
    for hospital_name in hospital_names:
        if hospital_name in name_to_rules:
            matched_rules.update(name_to_rules[hospital_name])
            # 记录匹配的项目
            for rule_id in name_to_rules[hospital_name]:
                rule_index[rule_id]['matched_items'].add(hospital_name)

    # 通过编码匹配
    for hospital_code in hospital_codes:
        if hospital_code in code_to_rules:
            matched_rules.update(code_to_rules[hospital_code])
            # 记录匹配的项目
            for rule_id in code_to_rules[hospital_code]:
                rule_index[rule_id]['matched_items'].add(hospital_code)

    # 第五步：处理匹配的规则，生成推荐
    recommendations = []

    for rule_id in matched_rules:
        rule_info = rule_index[rule_id]

        # 合并城市和来源信息
        cities_str = '、'.join(sorted(rule_info['cities'])) if rule_info['cities'] else '未知'
        sources_str = '、'.join(sorted(rule_info['sources'])) if rule_info['sources'] else '未知'
        matched_items_str = '、'.join(sorted(rule_info['matched_items'])) if rule_info['matched_items'] else ''

        # 计算匹配度（精确匹配给满分）
        match_score = 1.0

        # 生成推荐原因（使用第一个匹配项目）
        first_matched_item = list(rule_info['matched_items'])[0] if rule_info['matched_items'] else ''
        item_info = None

        # 查找匹配项目的详细信息
        for key, info in hospital_item_map.items():
            if info['name'] == first_matched_item or info['code'] == first_matched_item:
                item_info = info
                break

        if item_info:
            reason = generate_recommendation_reason(
                item_info['name'], item_info['code'],
                rule_info['rule_name']
            )
        else:
            reason = f"医院收费项目与规则'{rule_info['rule_name']}'中的医保项目匹配，建议应用此规则进行监管。"

        # 检查是否已存在推荐记录
        existing_query = """
        SELECT 适用ID, 状态 FROM 医院适用规则表
        WHERE 医院ID = :hospital_id AND 规则ID = :rule_id
        """
        cursor.execute(existing_query, {'hospital_id': hospital_id, 'rule_id': rule_id})
        existing = cursor.fetchone()

        if existing:
            # 更新现有记录
            update_query = """
            UPDATE 医院适用规则表
            SET 匹配度 = :match_score, 推荐原因 = :reason, 创建时间 = SYSDATE
            WHERE 适用ID = :existing_id
            """
            cursor.execute(update_query, {
                'match_score': match_score,
                'reason': reason,
                'existing_id': existing[0]
            })

            recommendations.append({
                '适用ID': existing[0],
                '规则ID': rule_id,
                '规则名称': rule_info['rule_name'],
                '行为认定': rule_info['behavior'],
                '类型': rule_info['rule_type'],
                '规则类型': rule_info['rule_category'],
                '规则内涵': rule_info['rule_content'],
                '匹配度': match_score,
                '推荐原因': reason,
                '状态': existing[1],
                '匹配项目': matched_items_str,
                '城市': cities_str,
                '规则来源': sources_str
            })
        else:
            # 插入新记录
            insert_rec_query = """
            INSERT INTO 医院适用规则表 (
                适用ID, 医院ID, 规则ID, 匹配度, 推荐原因, 状态, 创建时间
            ) VALUES (
                医院适用规则表_SEQ.NEXTVAL, :hospital_id, :rule_id,
                :match_score, :reason, '推荐', SYSDATE
            ) RETURNING 适用ID INTO :new_id
            """

            params = {
                'hospital_id': hospital_id,
                'rule_id': rule_id,
                'match_score': match_score,
                'reason': reason,
                'new_id': cursor.var(int)
            }

            cursor.execute(insert_rec_query, params)
            new_id = params['new_id'].getvalue()[0] if isinstance(params['new_id'].getvalue(), list) else params['new_id'].getvalue()

            recommendations.append({
                '适用ID': new_id,
                '规则ID': rule_id,
                '规则名称': rule_info['rule_name'],
                '行为认定': rule_info['behavior'],
                '类型': rule_info['rule_type'],
                '规则类型': rule_info['rule_category'],
                '规则内涵': rule_info['rule_content'],
                '匹配度': match_score,
                '推荐原因': reason,
                '状态': '推荐',
                '匹配项目': matched_items_str,
                '城市': cities_str,
                '规则来源': sources_str
            })

    return recommendations

def merge_same_rules(recommendations):
    """合并相同规则ID和相同规则名称的推荐"""
    # 第一步：按规则ID去重（同一规则因多个医保项目匹配产生的重复）
    rule_id_groups = {}

    for rec in recommendations:
        rule_id = rec['规则ID']

        if rule_id not in rule_id_groups:
            rule_id_groups[rule_id] = rec  # 保留第一个遇到的记录
        else:
            # 如果遇到相同规则ID，合并匹配项目信息
            existing = rule_id_groups[rule_id]
            if rec['匹配项目'] not in existing['匹配项目']:
                existing['匹配项目'] += '、' + rec['匹配项目']

    # 第二步：按规则名称分组（处理相同规则名称但不同规则ID的情况）
    rule_name_groups = {}

    for rule_id, rec in rule_id_groups.items():
        rule_name = rec['规则名称']

        if rule_name not in rule_name_groups:
            rule_name_groups[rule_name] = []

        rule_name_groups[rule_name].append(rec)

    # 第三步：合并相同规则名称的记录
    merged_recommendations = []

    for rule_name, group_recs in rule_name_groups.items():
        if len(group_recs) == 1:
            # 只有一条记录，直接添加
            merged_recommendations.append(group_recs[0])
        else:
            # 多条记录，需要合并
            base_rec = group_recs[0]  # 使用第一条作为基础

            # 合并城市信息
            all_cities = []
            all_sources = []
            all_match_items = []
            all_rule_ids = []

            for rec in group_recs:
                # 收集城市
                if rec['城市'] and rec['城市'] != '未知':
                    cities = rec['城市'].split(r'[、,，|;；]')
                    all_cities.extend([c.strip() for c in cities if c.strip()])

                # 收集规则来源
                if rec['规则来源'] and rec['规则来源'] != '未知':
                    sources = rec['规则来源'].split(r'[、,，|;；]')
                    all_sources.extend([s.strip() for s in sources if s.strip()])

                # 收集匹配项目
                if rec['匹配项目']:
                    items = rec['匹配项目'].split(r'[、,，|;；]')
                    all_match_items.extend([i.strip() for i in items if i.strip()])

                # 收集规则ID
                all_rule_ids.append(rec['规则ID'])

            # 去重并合并
            unique_cities = list(dict.fromkeys(all_cities))  # 保持顺序的去重
            unique_sources = list(dict.fromkeys(all_sources))
            unique_match_items = list(dict.fromkeys(all_match_items))

            # 创建合并后的记录
            merged_rec = base_rec.copy()
            merged_rec['城市'] = '、'.join(unique_cities) if unique_cities else '未知'
            merged_rec['规则来源'] = '、'.join(unique_sources) if unique_sources else '未知'
            merged_rec['匹配项目'] = '、'.join(unique_match_items) if unique_match_items else ''
            merged_rec['合并规则数量'] = len(group_recs)
            merged_rec['合并规则ID列表'] = all_rule_ids

            merged_recommendations.append(merged_rec)

    # 第四步：按规则名称相似度排序
    sorted_rule_names = sort_by_similarity([rec['规则名称'] for rec in merged_recommendations])

    # 重新组织推荐列表
    name_to_rec = {rec['规则名称']: rec for rec in merged_recommendations}
    sorted_recommendations = []
    for rule_name in sorted_rule_names:
        if rule_name in name_to_rec:
            sorted_recommendations.append(name_to_rec[rule_name])

    return sorted_recommendations

def sort_by_similarity(rule_names):
    """按规则名称相似度排序"""
    if not rule_names:
        return []

    # 简单的相似度排序：按规则名称的前缀和关键词分组
    def get_similarity_key(name):
        # 提取关键词用于分组
        keywords = []

        # 常见的医疗关键词
        medical_keywords = [
            'CT', 'MRI', '超声', 'X线', '心电图', '血常规', '生化',
            '手术', '治疗', '检查', '化验', '放射', '影像', '监护',
            '麻醉', '护理', '床位', '材料', '药品', '输血', '透析'
        ]

        for keyword in medical_keywords:
            if keyword in name:
                keywords.append(keyword)

        # 如果没有找到关键词，使用前几个字符
        if not keywords:
            keywords.append(name[:3] if len(name) >= 3 else name)

        return ''.join(sorted(keywords))

    # 按相似度键分组并排序
    similarity_groups = {}
    for name in rule_names:
        key = get_similarity_key(name)
        if key not in similarity_groups:
            similarity_groups[key] = []
        similarity_groups[key].append(name)

    # 组内按名称长度和字母顺序排序
    sorted_names = []
    for key in sorted(similarity_groups.keys()):
        group = similarity_groups[key]
        group.sort(key=lambda x: (len(x), x))
        sorted_names.extend(group)

    return sorted_names

def generate_recommendation_reason(item_name, item_code, rule_name):
    """生成推荐原因"""
    if item_code:
        return f"医院收费项目'{item_name}'（编码：{item_code}）与规则'{rule_name}'中的医保项目完全匹配，建议应用此规则进行监管。"
    else:
        return f"医院收费项目'{item_name}'与规则'{rule_name}'中的医保项目名称完全匹配，建议应用此规则进行监管。"

@app.route('/api/hospital-rules/status', methods=['PUT'])
@handle_db_error
def update_rule_status():
    """更新规则状态"""
    try:
        data = request.json
        适用ID = data.get('适用ID')
        状态 = data.get('状态')

        if not 适用ID or not 状态:
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400

        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                update_query = """
                UPDATE 医院适用规则表
                SET 状态 = :状态, 创建时间 = SYSDATE
                WHERE 适用ID = :适用ID
                """

                cursor.execute(update_query, {'状态': 状态, '适用ID': 适用ID})

            conn.commit()

            return jsonify({
                'success': True,
                'message': '规则状态更新成功'
            })

    except Exception as e:
        app.logger.error(f"更新规则状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/hospital-rules/batch-status', methods=['PUT'])
@handle_db_error
def batch_update_rule_status():
    """批量更新规则状态"""
    try:
        data = request.json
        rule_ids = data.get('rule_ids', [])
        status = data.get('status')

        if not rule_ids or not status:
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400

        updated_count = 0

        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                update_query = """
                UPDATE 医院适用规则表
                SET 状态 = :状态, 创建时间 = SYSDATE
                WHERE 适用ID = :适用ID
                """

                for rule_id in rule_ids:
                    try:
                        cursor.execute(update_query, {'状态': status, '适用ID': rule_id})
                        updated_count += 1
                    except Exception as e:
                        app.logger.warning(f"更新规则 {rule_id} 状态失败: {str(e)}")
                        continue

            conn.commit()

            return jsonify({
                'success': True,
                'updated_count': updated_count,
                'message': f'成功更新 {updated_count} 条规则状态'
            })

    except Exception as e:
        app.logger.error(f"批量更新规则状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/hospital-items/<int:hospital_id>', methods=['GET'])
@handle_db_error
def get_hospital_items(hospital_id):
    """获取医院的医保项目列表（用于调试）"""
    try:
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 获取医院的所有医保项目
                items_query = """
                SELECT DISTINCT h.医保项目编码, h.医保项目名称
                FROM 医院收费数据表 h
                WHERE h.医院ID = :hospital_id
                ORDER BY h.医保项目名称
                """
                cursor.execute(items_query, {'hospital_id': hospital_id})
                items = cursor.fetchall()

                # 转换为列表格式
                items_list = []
                for item in items:
                    items_list.append({
                        '编码': item[0],
                        '名称': item[1]
                    })

                return jsonify({
                    'success': True,
                    'hospital_id': hospital_id,
                    'items_count': len(items_list),
                    'items': items_list
                })

    except Exception as e:
        app.logger.error(f"获取医院项目失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/hospital-data/<int:hospital_id>', methods=['GET'])
@handle_db_error
def get_hospital_data(hospital_id):
    """获取医院收费数据"""
    try:
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 获取医院基本信息
                hospital_query = """
                SELECT 医院名称, 医院编码, 所在城市, 医院等级
                FROM 医院信息表
                WHERE 医院ID = :hospital_id
                """
                cursor.execute(hospital_query, {'hospital_id': hospital_id})
                hospital_info = cursor.fetchone()

                if not hospital_info:
                    return jsonify({
                        'success': False,
                        'error': '医院不存在'
                    }), 404

                # 获取收费数据统计
                stats_query = """
                SELECT
                    COUNT(*) as 总记录数,
                    COUNT(DISTINCT 医保项目编码) as 项目种类数,
                    SUM(收费金额) as 总收费金额,
                    SUM(收费次数) as 总收费次数,
                    MIN(创建时间) as 最早数据时间,
                    MAX(创建时间) as 最新数据时间
                FROM 医院收费数据表
                WHERE 医院ID = :hospital_id
                """
                cursor.execute(stats_query, {'hospital_id': hospital_id})
                stats = cursor.fetchone()

                # 获取收费数据详情（分页）
                page = int(request.args.get('page', 1))
                per_page = int(request.args.get('per_page', 20))
                offset = (page - 1) * per_page

                data_query = """
                SELECT * FROM (
                    SELECT 医保项目编码, 医保项目名称, 收费金额, 收费次数,
                           科室名称, 医生姓名, 患者年龄, 创建时间,
                           ROW_NUMBER() OVER (ORDER BY 创建时间 DESC) as rn
                    FROM 医院收费数据表
                    WHERE 医院ID = :hospital_id
                ) WHERE rn BETWEEN :start_row AND :end_row
                """
                start_row = offset + 1
                end_row = offset + per_page

                cursor.execute(data_query, {
                    'hospital_id': hospital_id,
                    'start_row': start_row,
                    'end_row': end_row
                })

                columns = [col[0] for col in cursor.description]
                data_rows = cursor.fetchall()

                data_list = []
                for row in data_rows:
                    data_dict = dict(zip(columns, row))
                    # 处理日期格式
                    if data_dict.get('创建时间'):
                        data_dict['创建时间'] = data_dict['创建时间'].strftime('%Y-%m-%d %H:%M:%S')
                    data_list.append(data_dict)

                # 获取按项目汇总的数据
                summary_query = """
                SELECT * FROM (
                    SELECT 医保项目编码, 医保项目名称,
                           SUM(收费金额) as 总金额,
                           SUM(收费次数) as 总次数,
                           COUNT(*) as 记录数
                    FROM 医院收费数据表
                    WHERE 医院ID = :hospital_id
                    GROUP BY 医保项目编码, 医保项目名称
                    ORDER BY 总金额 DESC
                ) WHERE ROWNUM <= 20
                """
                cursor.execute(summary_query, {'hospital_id': hospital_id})
                summary_columns = [col[0] for col in cursor.description]
                summary_rows = cursor.fetchall()

                summary_list = []
                for row in summary_rows:
                    summary_list.append(dict(zip(summary_columns, row)))

                return jsonify({
                    'success': True,
                    'hospital_info': {
                        '医院名称': hospital_info[0],
                        '医院编码': hospital_info[1],
                        '所在城市': hospital_info[2],
                        '医院等级': hospital_info[3]
                    },
                    'statistics': {
                        '总记录数': stats[0] or 0,
                        '项目种类数': stats[1] or 0,
                        '总收费金额': float(stats[2]) if stats[2] else 0,
                        '总收费次数': stats[3] or 0,
                        '最早数据时间': stats[4].strftime('%Y-%m-%d %H:%M:%S') if stats[4] else None,
                        '最新数据时间': stats[5].strftime('%Y-%m-%d %H:%M:%S') if stats[5] else None
                    },
                    'data': data_list,
                    'summary': summary_list,
                    'pagination': {
                        'page': page,
                        'per_page': per_page,
                        'total': stats[0] or 0
                    }
                })

    except Exception as e:
        app.logger.error(f"获取医院数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/hospital-rules/adopted/<int:hospital_id>', methods=['GET'])
@handle_db_error
def get_hospital_adopted_rules(hospital_id):
    """获取医院已采用的规则"""
    try:
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                query = """
                SELECT DISTINCT h.适用ID, h.规则ID, r.规则名称, r.行为认定, r.类型, r.规则类型,
                       h.匹配度, h.推荐原因, h.创建时间,
                       c.城市, c.规则来源, c.规则内涵
                FROM 医院适用规则表 h
                JOIN 飞检规则知识库 r ON h.规则ID = r.ID
                LEFT JOIN (
                    SELECT 规则ID, 城市, 规则来源, 规则内涵,
                           ROW_NUMBER() OVER (PARTITION BY 规则ID ORDER BY 对照ID) as rn
                    FROM 规则医保编码对照
                    WHERE 城市 IS NOT NULL AND 规则来源 IS NOT NULL
                ) c ON r.ID = c.规则ID AND c.rn = 1
                WHERE h.医院ID = :hospital_id AND h.状态 = '已采用'
                ORDER BY h.创建时间 DESC
                """

                cursor.execute(query, {'hospital_id': hospital_id})
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()

                rules = []
                seen_rules = set()  # 用于去重相同规则ID的记录

                for row in rows:
                    rule = dict(zip(columns, row))

                    # 去重：如果已经有相同规则ID的记录，跳过
                    rule_id = rule.get('规则ID')
                    if rule_id in seen_rules:
                        continue
                    seen_rules.add(rule_id)

                    # 处理日期格式
                    if rule.get('创建时间'):
                        rule['创建时间'] = rule['创建时间'].strftime('%Y-%m-%d %H:%M:%S')

                    # 处理空值
                    rule['城市'] = rule.get('城市') or '未知'
                    rule['规则来源'] = rule.get('规则来源') or '未知'
                    rule['类型'] = rule.get('类型') or '未知'
                    rule['匹配度'] = rule.get('匹配度') or 1.0

                    rules.append(rule)

                return jsonify({
                    'success': True,
                    'rules': rules,
                    'count': len(rules)
                })

    except Exception as e:
        app.logger.error(f"获取医院已采用规则失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/debug-matching/<int:hospital_id>', methods=['GET'])
@handle_db_error
def debug_matching(hospital_id):
    """调试规则匹配过程"""
    try:
        with get_connection(pool) as conn:
            with conn.cursor() as cursor:
                # 获取医院的医保项目
                hospital_items_query = """
                SELECT DISTINCT h.医保项目编码, h.医保项目名称
                FROM 医院收费数据表 h
                WHERE h.医院ID = :hospital_id
                ORDER BY h.医保项目名称
                """
                cursor.execute(hospital_items_query, {'hospital_id': hospital_id})
                hospital_items = cursor.fetchall()

                # 构建医院项目集合
                hospital_names = set()
                hospital_codes = set()
                for item in hospital_items:
                    item_code, item_name = item
                    if item_name:
                        hospital_names.add(item_name.strip())
                    if item_code:
                        hospital_codes.add(item_code.strip())

                # 获取包含"全身麻醉"的规则
                anesthesia_rules_query = """
                SELECT r.ID as 规则ID, r.规则名称, c.医保名称1, c.医保名称2, c.医保编码1, c.医保编码2
                FROM 飞检规则知识库 r
                JOIN 规则医保编码对照 c ON r.ID = c.规则ID
                WHERE (c.医保名称1 LIKE '%全身麻醉%' OR c.医保名称2 LIKE '%全身麻醉%'
                       OR c.医保编码1 LIKE '%全身麻醉%' OR c.医保编码2 LIKE '%全身麻醉%')
                """
                cursor.execute(anesthesia_rules_query)
                anesthesia_rules = cursor.fetchall()

                # 检查匹配情况
                matching_info = {
                    'hospital_items_count': len(hospital_items),
                    'hospital_names': list(hospital_names),
                    'hospital_codes': list(hospital_codes),
                    'has_anesthesia': '全身麻醉' in hospital_names,
                    'anesthesia_rules_count': len(anesthesia_rules),
                    'anesthesia_rules': []
                }

                for rule in anesthesia_rules:
                    rule_info = {
                        '规则ID': rule[0],
                        '规则名称': rule[1],
                        '医保名称1': rule[2],
                        '医保名称2': rule[3],
                        '医保编码1': rule[4],
                        '医保编码2': rule[5]
                    }
                    matching_info['anesthesia_rules'].append(rule_info)

                return jsonify({
                    'success': True,
                    'matching_info': matching_info
                })

    except Exception as e:
        app.logger.error(f"调试匹配过程失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    try:
        port = int(os.environ.get('PORT', 5001))
        app.run(
            host='0.0.0.0',
            port=port,
            debug=True,
            use_reloader=False,
            reloader_type='stat'  # 使用 stat 替代默认的 watchdog
        )
    except Exception as e:
        logging.error(f"Application failed to start: {str(e)}")
        raise
