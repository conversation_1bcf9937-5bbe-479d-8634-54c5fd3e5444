#!/usr/bin/env python3
"""
Gemini API配置
"""

import os

# Gemini API配置
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', 'AIzaSyABSOhbPgGLHl78bTXb55Keyxhh1rCDCUw')  # 请替换为实际的API Key
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent"

# API调用配置
GEMINI_TIMEOUT = 30  # 超时时间（秒）
GEMINI_BATCH_SIZE = 10  # 批处理大小
GEMINI_DELAY = 1  # 请求间隔（秒）

# 提示词模板
GEMINI_PROMPT_TEMPLATE = """
你是一个医保基金违规行为识别专家。请分析以下文本内容，提取其中的医保违规规则，并按照标准格式输出。

输入格式要求（JSON格式）：
{
    "rules": [
        {
            "rule_source": "规则来源",
            "city": "城市",
            "sequence_number": 序号,
            "department": "涉及科室",
            "violation_type": "行为认定",
            "rule_name": "规则名称",
            "rule_content": "规则内涵"
        }
    ]
}


分析规则：
1. 只提取明确的违规行为规则，忽略标题、目录、说明等内容
2. 规则名称应简洁明确，描述具体的违规行为
3. 规则内涵是完整的规则描述
4. 医保名称1是主要涉及的医保项目，医保名称2是关联的医保项目
5. 违规类型包括：重复收费、超标准收费、分解收费、虚假收费、套用收费、过度医疗、挂床住院等
6. 科室根据医疗内容判断：心血管内科、神经内科、消化内科、呼吸内科、外科、妇产科、儿科、眼科、耳鼻喉科等
7. 时间类型包括：分钟、小时、天、周、月、年等
8. 置信度根据规则明确程度评估，明确的违规行为规则置信度应在0.8以上

待分析文本：
"""

# 智能获取规则信息的提示词模板
GEMINI_RULE_ANALYSIS_PROMPT = """
你是一个医保基金违规行为识别专家。请分析以下医保违规规则内容，从中提取详细的规则信息。

分析要求：
1. 仔细阅读规则内容，识别其中涉及的具体医保项目、药品、检查、治疗等
2. 分析违规行为的类型和特征
3. 识别数量、时间、金额等限制条件
4. 分析适用的科室和诊断范围
5. 提取的信息应该准确、具体，符合医保管理规范

输出格式（JSON）：
{
    "medical_name1": "主要医保项目名称",
    "medical_name2": "次要医保项目名称（可为空）",
    "type": "违规类型（如：重复收费、超标准收费、分解收费、虚假收费、套用收费、过度医疗、挂床住院、超频次、超数量、超金额、超天数、超年龄、限性别等）",
    "violation_count": "违规数量（数字，如果有的话）",
    "exclude_diagnosis": "排除诊断（应该排除的诊断名称，多个用逗号分隔）",
    "exclude_departments": "排除科室（应该排除的科室名称，多个用逗号分隔）",
    "include_diagnosis": "包含诊断（必须包含的诊断名称，多个用逗号分隔）",
    "include_departments": "包含科室（适用的科室名称，多个用逗号分隔）",
    "time_type": "时间类型（如：分钟、小时、日、周、月、年、住院期间、审查等）",
    "violation_amount": "违规金额（数字，如果有的话）",
    "age_limit": "年龄限制（数字，如果有的话）",
    "gender_limit": "性别限制（男/女，如果有的话）",
    "confidence": 置信度(0-1),
    "reasoning": "详细的分析推理过程"
}

分析指南：
- **类型判断**：根据规则描述判断违规类型
  * 如果是按小时计费但每日总时长超过24小时，类型为"日"
  * 如果是重复收费，类型为"重复收费"
  * 如果是超标准收费，类型为"超标准收费"
  * 如果是不符合适用范围，类型为"超范围"

- **数量提取**：从规则中提取具体的数量限制
  * "每日总时长大于24小时"中的违规数量是"24"
  * "超过3次"中的违规数量是"3"
  * 注意区分是超过还是等于的概念

- **时间类型判断**：
  * 如果规则涉及"每日"、"日总时长"等，时间类型为"日"
  * 如果规则涉及"每周"，时间类型为"周"
  * 如果规则涉及"每月"，时间类型为"月"
  * 如果规则涉及"住院期间"，时间类型为"住院期间"

- **"限于"条件的理解**：
  * "限于重症监护室危重病人脑功能监测"意味着只有在重症监护室的危重病人脑功能监测情况下才不违规
  * 因此，不在重症监护室的就是违规，所以排除科室应该填入"重症监护室"
  * 不是危重病人脑功能监测的就是违规，所以排除诊断应该填入相关的危重病诊断

- **科室分析**：
  * 根据医疗项目特点判断适用科室
  * 注意"限于"条件中提到的科室应该作为排除科室

- **诊断分析**：
  * 识别规则中明确提到的诊断要求或排除条件
  * 对于"危重病人脑功能监测"等描述，需要推断对应的ICD10诊断名称
  * 危重病人可能对应的诊断：重症、危重症、意识障碍、昏迷等相关诊断

**分析示例**：

示例1：
规则：呼吸机辅助呼吸、遥测心电监护、连续性血液净化等计价单位为小时，医院计费每日总时长大于24小时
分析结果：
- medical_name1: "呼吸机辅助呼吸"
- medical_name2: "遥测心电监护"
- type: "日"
- violation_count: "24"
- time_type: "日"

示例2：
规则：对不符合脑电双频指数监测适用范围（限于重症监护室危重病人脑功能监测）的患者，收取脑电双频指数监测费用
分析结果：
- medical_name1: "脑电双频指数监测"
- type: "超范围"
- exclude_departments: "重症监护室"
- exclude_diagnosis: "危重症,意识障碍,昏迷"

**常见违规类型映射**：
- 按小时计费但每日超时 → "日"
- 重复收费 → "重复收费"
- 超标准收费 → "超标准收费"
- 分解收费 → "分解收费"
- 虚假收费 → "虚假收费"
- 套用收费 → "套用收费"
- 不符合适用范围 → "超范围"
- 超频次使用 → "超频次"
- 超数量使用 → "超数量"
- 超金额收费 → "超金额"
- 超天数使用 → "超天数"
- 超年龄限制 → "超年龄"
- 性别限制违规 → "限性别"

**常见医疗场景诊断映射**：
- 危重病人 → "危重症,重症,意识障碍,昏迷,休克"
- 手术病人 → "手术,术后,麻醉"
- 急诊病人 → "急诊,急性,外伤"
- 儿童病人 → "儿童,小儿,新生儿"
- 孕产妇 → "妊娠,分娩,产后"
- 肿瘤病人 → "肿瘤,癌症,恶性"
- 心血管病人 → "心脏病,冠心病,心律失常,高血压"
- 呼吸系统病人 → "肺炎,哮喘,慢阻肺,呼吸衰竭"

**科室常见映射**：
- 重症监护 → "重症监护室,ICU,NICU,PICU"
- 手术相关 → "手术室,麻醉科"
- 急诊相关 → "急诊科,急诊室"
- 专科科室 → "心内科,呼吸科,神经科,肿瘤科"

规则内容：
"""
