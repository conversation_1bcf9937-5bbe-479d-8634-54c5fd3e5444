-- 医院个性化规则推荐系统数据库表结构

-- 1. 医院信息表
CREATE TABLE 医院信息表 (
    医院ID NUMBER PRIMARY KEY,
    医院名称 VARCHAR2(200) NOT NULL,
    医院编码 VARCHAR2(50),
    所在城市 VARCHAR2(50),
    医院等级 VARCHAR2(20),
    创建时间 DATE DEFAULT SYSDATE,
    CONSTRAINT UK_医院名称 UNIQUE (医院名称)
);

-- 创建医院信息表序列
CREATE SEQUENCE 医院信息表_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- 2. 医院收费数据表
CREATE TABLE 医院收费数据表 (
    收费ID NUMBER PRIMARY KEY,
    医院ID NUMBER NOT NULL,
    医保项目编码 VARCHAR2(50),
    医保项目名称 VARCHAR2(200),
    收费金额 NUMBER(10,2) DEFAULT 0,
    收费次数 NUMBER DEFAULT 0,
    统计月份 VARCHAR2(7), -- 格式：2024-01
    数据来源 VARCHAR2(50) DEFAULT '手动导入',
    科室名称 VARCHAR2(100),
    医生姓名 VARCHAR2(50),
    患者年龄 NUMBER,
    创建时间 DATE DEFAULT SYSDATE,
    CONSTRAINT FK_医院收费_医院ID FOREIGN KEY (医院ID) REFERENCES 医院信息表(医院ID)
);

-- 创建医院收费数据表序列
CREATE SEQUENCE 医院收费数据表_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- 3. 医院适用规则表
CREATE TABLE 医院适用规则表 (
    适用ID NUMBER PRIMARY KEY,
    医院ID NUMBER NOT NULL,
    规则ID NUMBER NOT NULL,
    匹配度 NUMBER(3,2) DEFAULT 0, -- 0.00-1.00
    推荐原因 VARCHAR2(500),
    状态 VARCHAR2(20) DEFAULT '推荐', -- 推荐/已采用/已忽略
    匹配项目 VARCHAR2(500), -- 匹配的医保项目列表
    创建时间 DATE DEFAULT SYSDATE,
    更新时间 DATE DEFAULT SYSDATE,
    CONSTRAINT FK_医院规则_医院ID FOREIGN KEY (医院ID) REFERENCES 医院信息表(医院ID),
    CONSTRAINT FK_医院规则_规则ID FOREIGN KEY (规则ID) REFERENCES 飞检规则知识库(ID),
    CONSTRAINT CK_匹配度 CHECK (匹配度 >= 0 AND 匹配度 <= 1),
    CONSTRAINT CK_状态 CHECK (状态 IN ('推荐', '已采用', '已忽略'))
);

-- 创建医院适用规则表序列
CREATE SEQUENCE 医院适用规则表_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- 创建索引以提高查询性能
CREATE INDEX IDX_医院收费_医院ID ON 医院收费数据表(医院ID);
CREATE INDEX IDX_医院收费_项目编码 ON 医院收费数据表(医保项目编码);
CREATE INDEX IDX_医院收费_项目名称 ON 医院收费数据表(医保项目名称);
CREATE INDEX IDX_医院收费_统计月份 ON 医院收费数据表(统计月份);

CREATE INDEX IDX_医院规则_医院ID ON 医院适用规则表(医院ID);
CREATE INDEX IDX_医院规则_规则ID ON 医院适用规则表(规则ID);
CREATE INDEX IDX_医院规则_状态 ON 医院适用规则表(状态);
CREATE INDEX IDX_医院规则_匹配度 ON 医院适用规则表(匹配度);

-- 插入示例数据
INSERT INTO 医院信息表 (医院ID, 医院名称, 医院编码, 所在城市, 医院等级) VALUES 
(医院信息表_SEQ.NEXTVAL, '西安市第一人民医院', 'XA001', '西安', '三甲');

INSERT INTO 医院信息表 (医院ID, 医院名称, 医院编码, 所在城市, 医院等级) VALUES 
(医院信息表_SEQ.NEXTVAL, '西安交通大学第一附属医院', 'XA002', '西安', '三甲');

INSERT INTO 医院信息表 (医院ID, 医院名称, 医院编码, 所在城市, 医院等级) VALUES 
(医院信息表_SEQ.NEXTVAL, '沈阳市中心医院', 'SY001', '沈阳', '三甲');

-- 提交事务
COMMIT;

-- 查询验证
SELECT '医院信息表' as 表名, COUNT(*) as 记录数 FROM 医院信息表
UNION ALL
SELECT '医院收费数据表' as 表名, COUNT(*) as 记录数 FROM 医院收费数据表
UNION ALL
SELECT '医院适用规则表' as 表名, COUNT(*) as 记录数 FROM 医院适用规则表;
