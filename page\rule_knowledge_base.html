<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则知识库</title>
    
    <!-- CSS 文件 -->
   <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
   <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
   <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    
    <!-- JavaScript 文件 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- 添加favicon引用 -->
   <!-- <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}"> -->
    
    <!-- 修改为本地Bootstrap样式 -->
    <!-- <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet"> -->
    <!-- <link href="{{ url_for('static', filename='css/dataTables.bootstrap5.min.css') }}" rel="stylesheet"> -->
    <!-- <link href="{{ url_for('static', filename='css/bootstrap-icons.css') }}" rel="stylesheet"> -->
    
    <!-- 修改为本地JavaScript文件 -->
    <!-- <script src="{{ url_for('static', filename='js/jquery-3.6.0.min.js') }}"></script> -->
    <!-- <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script> -->
    <!-- <script src="{{ url_for('static', filename='js/jquery.dataTables.min.js') }}"></script> -->
    <!-- <script src="{{ url_for('static', filename='js/dataTables.bootstrap5.min.js') }}"></script> -->

    <style>
        :root {
            --primary-color: #0078D4;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
        }
        
        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            padding: 20px;
        }
        
        .container-fluid {
            max-width: 2400px;
        }
        
        .card {
            background-color: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 15px 20px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 120, 212, 0.25);
        }
        
        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background-color: var(--primary-color) !important;
            color: white !important;
            border: none;
        }
        
        .modal-header {
            background-color: var(--primary-color);
            color: white;
        }
        
        .required::after {
            content: " *";
            color: red;
        }
        
        .form-control-sm {
            height: calc(1.5em + 0.5rem + 2px);
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .mb-2 {
            margin-bottom: 0.5rem !important;
        }
        
        .g-2 {
            --bs-gutter-y: 0.5rem;
            --bs-gutter-x: 0.5rem;
        }
        
        label.form-label {
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <nav>
            <a href="/">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M9.78 12.78a.75.75 0 01-1.06 0L4.47 8.53a.75.75 0 010-1.06l4.25-4.25a.75.75 0 011.06 1.06L6.06 8l3.72 3.72a.75.75 0 010 1.06z"/>
                </svg>
                返回主页
            </a>
        </nav>
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                <h3 class="mb-0">飞检规则知识库维护</h3>
                    <div class="toolbar">
                        <button onclick="addNewRule()" class="btn btn-primary">
                            <i class="bi bi-plus"></i> 新增规则
                        </button>
                        <button onclick="showImportModal()" class="btn btn-success ms-2">
                            <i class="bi bi-file-earmark-excel"></i> 导入规则
                        </button>

                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- 搜索区域 -->
                <div class="row mb-4">
                    <div class="col-md-2 mb-2">
                        <label class="form-label">城市</label>
                        <select class="form-select" id="searchCity">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <label class="form-label">规则名称</label>
                        <input type="text" class="form-control" id="searchRuleName" placeholder="规则名称">
                    </div>
                    <div class="col-md-2 mb-2">
                        <label class="form-label">类型</label>
                        <select class="form-select" id="searchType">
                            <option value="">全部</option>
                            <option value="__unset__">未设置</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <label class="form-label">规则类型</label>
                        <select class="form-select" id="searchRuleType">
                            <option value="">全部</option>
                            <option value="__unset__">未设置</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <label class="form-label">规则来源</label>
                        <select class="form-select" id="searchRuleSource">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <label class="form-label">行为认定</label>
                        <select class="form-select" id="searchBehavior">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-2">
                        <button class="btn btn-primary me-2" onclick="searchRules()">搜索</button>
                        <button class="btn btn-secondary" onclick="resetSearch()">重置</button>
                        <button id="batchMergeBtn" class="btn btn-warning ms-2" onclick="batchMergeRules()">
                            <i class="bi bi-git-merge"></i> 批量合并
                        </button>
                        <button id="batchAiBtn" class="btn btn-success ms-2" onclick="batchAiIntelligentGet()" title="批量AI智能分析选中的规则，自动填充医保名称、违规类型等信息">
                            <i class="bi bi-magic"></i> 批量AI智能获取
                        </button>
                    </div>
                </div>
                
                <!-- 数据表格 -->
                <div class="container-fluid mt-3">

                    <table id="rulesTable" class="table table-striped table-bordered">

                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" onclick="toggleSelectAll()">
                                </th>
                                <th>ID</th>
                                <th>规则来源</th>
                                <th>序号</th>
                                <th>规则名称</th>
                                <th>城市</th>
                                <th>类型</th>
                                <th>规则类型</th>
                                <th>行为认定</th>
                                <th>用途</th>
                                <th>医保编码1</th>
                                <th>医保名称1</th>
                                <th>医保编码2</th>
                                <th>医保名称2</th>
                                <th>规则内涵</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑/新增模态框 -->
    <div class="modal fade" id="ruleModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">规则信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="ruleForm">
                        <div class="row">
                            <div class="col-md-2 mb-3">
                                <label class="form-label">ID</label>
                                <input type="text" class="form-control" name="id" readonly>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label required">序号</label>
                                <input type="number" class="form-control" name="序号" readonly>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">类型</label>
                                <select class="form-control" name="类型" >
                                    <option value="">请选择</option>
                                    <option value="重复收费">重复收费</option>
                                    <option value="日">日</option>
                                    <option value="周">周</option>
                                    <option value="两周">两周</option>
                                    <option value="月">月</option>
                                    <option value="年">年</option>
                                    <option value="小时">小时</option>
                                    <option value="金额">金额</option>
                                    <option value="年龄">年龄</option>
                                    <option value="性别">性别</option>
                                    <option value="住院天数">住院天数</option>
                                    <option value="合超住院天数">合超住院天数</option>
                                    <option value="频次上限">频次上限</option>
                                    <option value="天数上限">天数上限</option>
                                    <option value="全量病例">全量病例</option>
                                    <option value="组套收费">组套收费</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">规则类型</label>
                                <select class="form-control form-control-sm" name="规则类型">
                                    <option value="">请选择</option>
                                    <option value="定量">定量</option>
                                    <option value="定性">定性</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">城市</label>
                                <div class="input-group">
                                    <select class="form-control" name="城市" id="citySelect" onchange="loadRulesByCity()">
                                    </select>
                                    <button class="btn btn-outline-primary" type="button" onclick="showProvinceModal()">
                                        <i class="bi bi-plus-circle"></i> 添加省市
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label required">规则名称</label>
                                <input type="text" class="form-control" name="规则名称" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">规则来源</label>
                                <input type="text" class="form-control" name="规则来源">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label required">行为认定</label>
                                <textarea class="form-control" name="行为认定" rows="3" required></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label required">规则内涵</label>
                                <textarea class="form-control" name="规则内涵" rows="3" required></textarea>
                            </div>
                            <div class="row g-2">
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">违规数量</label>
                                    <input type="number" class="form-control form-control-sm" name="违规数量">
                                </div>
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">时间类型</label>
                                    <select class="form-control form-control-sm" name="时间类型">
                                        <option value="">请选择</option>
                                        <option value="分钟">分钟</option>
                                        <option value="小时">小时</option>
                                        <option value="日">日</option>
                                        <option value="住院期间">住院期间</option>
                                        <option value="审查">审查</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-2">
                                    <label class="form-label">性别</label>
                                    <select class="form-control form-control-sm" name="性别">
                                        <option value="">请选择</option>
                                        <option value="男">男</option>
                                        <option value="女">女</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">违规金额</label>
                                    <input type="number" class="form-control form-control-sm" name="违规金额">
                                </div>
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">年龄</label>
                                    <input type="number" class="form-control form-control-sm" name="年龄">
                                </div>

                                <div class="col-md-4 mb-2">
                                    <label class="form-label">所属领域</label>
                                    <select class="form-control form-control-sm" name="所属领域">
                                        <option value="通用" selected>通用</option>
                                        <option value="药品">药品</option>
                                        <option value="检验">检验</option>
                                        <option value="影像">影像</option>
                                        <option value="麻醉">麻醉</option>
                                        <option value="手术">手术</option>
                                        <option value="介入">介入</option>
                                        <option value="骨科">骨科</option>
                                        <option value="心血管">心血管</option>
                                        <option value="康复">康复</option>
                                        <option value="血透">血透</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-2">
                                    <label class="form-label">用途</label>
                                    <select class="form-control form-control-sm" name="用途">
                                        <option value="">全部适用</option>
                                        <option value="门诊">门诊</option>
                                        <option value="住院">住院</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row g-2">
                                <div class="col-md-3 mb-2">
                                <label class="form-label">排除诊断</label>
                                    <input type="text" class="form-control form-control-sm" name="排除诊断">
                            </div>
                                <div class="col-md-3 mb-2">
                                <label class="form-label">排除科室</label>
                                    <input type="text" class="form-control form-control-sm" name="排除科室">
                            </div>
                                <div class="col-md-3 mb-2">
                                <label class="form-label">包含诊断</label>
                                    <input type="text" class="form-control form-control-sm" name="包含诊断">
                            </div>
                            <div class="col-md-3 mb-2">
                                <label class="form-label">包含科室</label>
                                    <input type="text" class="form-control form-control-sm" name="包含科室">
                            </div>

                            </div>

                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5>医保信息</h5>
                                    <button type="button" class="btn btn-success btn-sm" onclick="intelligentGetMedicalCodes()" title="AI智能分析规则内容，自动填充医保名称、违规类型、数量、时间类型、科室、诊断等多个字段">
                                        <i class="bi bi-magic"></i> AI智能获取
                                    </button>
                                </div>
                                <div class="row">
                                    <!-- 医保信息1 -->
                                    <div class="col-md-6 mb-3">
                                        <div class="row">
                                            <div class="col-md-6">
                                        <label class="form-label">医保编码1</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="医保编码1" id="医保编码1">
                                                    <button class="btn btn-outline-secondary" type="button" onclick="searchMedicalInsurance(1)">
                                                        <i class="bi bi-search"></i> 检索
                                                    </button>
                                    </div>
                                            </div>
                                            <div class="col-md-6">
                                        <label class="form-label">医保名称1</label>
                                                <input type="text" class="form-control" name="医保名称1" id="医保名称1" >
                                    </div>
                                        </div>
                                    </div>

                                    <!-- 医保信息2 -->
                                    <div class="col-md-6 mb-3">
                                        <div class="row">
                                            <div class="col-md-6">
                                        <label class="form-label">医保编码2</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="医保编码2" id="医保编码2">
                                                    <button class="btn btn-outline-secondary" type="button" onclick="searchMedicalInsurance(2)">
                                                        <i class="bi bi-search"></i> 检索
                                                    </button>
                                    </div>
                                            </div>
                                            <div class="col-md-6">
                                        <label class="form-label">医保名称2</label>
                                                <input type="text" class="form-control" name="医保名称2" id="医保名称2" >
                                    </div>
                                        </div>
                                    </div>

                                    <!-- 国家医保信息1 -->
                                    <div class="col-md-6 mb-3">
                                        <div class="row">
                                            <div class="col-md-6">
                                        <label class="form-label">国家医保编码1</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="国家医保编码1" id="国家医保编码1">
                                                    <button class="btn btn-outline-secondary" type="button" onclick="searchMedicalInsurance(3)">
                                                        <i class="bi bi-search"></i> 检索
                                                    </button>
                                    </div>
                                            </div>
                                            <div class="col-md-6">
                                        <label class="form-label">国家医保名称1</label>
                                                <input type="text" class="form-control" name="国家医保名称1" id="国家医保名称1" >
                                    </div>
                                        </div>
                                    </div>

                                    <!-- 国家医保信息2 -->
                                    <div class="col-md-6 mb-3">
                                        <div class="row">
                                            <div class="col-md-6">
                                        <label class="form-label">国家医保编码2</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="国家医保编码2" id="国家医保编码2">
                                                    <button class="btn btn-outline-secondary" type="button" onclick="searchMedicalInsurance(4)">
                                                        <i class="bi bi-search"></i> 检索
                                                    </button>
                                    </div>
                                            </div>
                                            <div class="col-md-6">
                                        <label class="form-label">国家医保名称2</label>
                                                <input type="text" class="form-control" name="国家医保名称2" id="国家医保名称2" >
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                                    <div class="col-6 mb-3">
                                        <label class="form-label">备注</label>
                                        <textarea class="form-control" name="备注" rows="2"></textarea>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="d-flex justify-content-begin align-items-end h-100">
                                            <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">取消</button>
                                            <button type="button" class="btn btn-primary" onclick="saveRule()">保存</button>
                                        </div>
                                    </div>
                                    </div>

                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>


    <!-- 添加医保检索模态框 -->
    <div class="modal fade" id="medicalInsuranceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">医保信息检索</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col">
                            <input type="text" class="form-control" id="searchMedicalCode" placeholder="输入编码搜索">
                        </div>
                        <div class="col">
                            <input type="text" class="form-control" id="searchMedicalName" placeholder="输入名称搜索">
                        </div>
                        <div class="col">
                            <input type="text" class="form-control" id="searchCountryCode" placeholder="输入国家编码搜索">
                        </div>
                        <div class="col">
                            <input type="text" class="form-control" id="searchPriceCode" placeholder="输入物价码搜索">
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="mb-3">
                            <h6 class="text-muted">
                                当前查询城市：<span id="currentQueryCity" class="fw-bold"></span>
                            </h6>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <h6>已选择项目：</h6>
                            <button type="button" class="btn btn-primary btn-sm" onclick="confirmMedicalSelection()">确认选择</button>
                        </div>
                        <div id="selectedMedicalItems" class="mt-2">
                            <!-- 已选择的项目会显示在这里 -->
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped" id="medicalInsuranceTable">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAllMedical" onclick="toggleAllMedicalItems()">
                                    </th>
                                    <th>医保编码</th>
                                    <th>医保名称</th>
                                    <th>国家编码</th>
                                    <th>物价码</th>
                                    <th>费用类别</th>
                                    <th>单价</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入规则模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导入规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-4">
                        <h6>第一步：下载模板</h6>
                        <a href="/api/rules/template" class="btn btn-outline-primary">
                            <i class="bi bi-download"></i> 下载Excel模板
                        </a>
                    </div>
                    <div class="mb-4">
                        <h6>第二步：选择文件</h6>
                        <div class="input-group">
                            <input type="file" class="form-control" id="importFile" accept=".xlsx,.xls">
                        </div>
                        <small class="text-muted">支持.xlsx和.xls格式的Excel文件</small>
                    </div>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> 提示：
                        <ul class="mb-0">
                            <li>请使用下载的模板填写数据</li>
                            <li>确保Excel中的列名与模板一致</li>
                            <li>日期格式请使用YYYY-MM-DD</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="handleImport()">
                        <i class="bi bi-upload"></i> 导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入规则预览模态框 -->
    <div class="modal fade" id="importPreviewModal" tabindex="-1" role="dialog" aria-labelledby="importPreviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importPreviewModalLabel">导入规则预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="mb-3 d-flex justify-content-between align-items-center">
                            <div>
                                <button class="btn btn-sm btn-primary" onclick="selectAllRules()">全选</button>
                                <button class="btn btn-sm btn-secondary" onclick="deselectAllRules()">取消全选</button>
                                <span class="ms-3">已选择 <span id="selectedCount">0</span> 条规则</span>
                            </div>
                            <div class="d-flex gap-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch" id="showUnCreatedRules" onchange="filterPreviewRules()">
                                    <label class="form-check-label" for="showUnCreatedRules">未有规则</label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch" id="showExistingRules" onchange="filterPreviewRules()">
                                    <label class="form-check-label" for="showExistingRules">已有规则</label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch" id="showExistingCityRules" onchange="filterPreviewRules()">
                                    <label class="form-check-label" for="showExistingCityRules">未有城市</label>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table id="previewTable" class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th><input type="checkbox" id="selectAll"></th>
                                        <th>序号</th>
                                        <th>规则名称</th>
                                        <th>匹配规则</th>
                                        <th>规则来源</th>
                                        <th>行为认定</th>
                                        <th>规则内涵</th>
                                        <th>适用范围</th>
                                        <th>城市</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="importSelectedRules()">导入选中规则</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 省市选择模态框 -->
    <div class="modal fade" id="provinceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">选择省市</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">选择省份</label>
                        <select class="form-control" id="provinceSelect" onchange="updateModalCities()">
                            <option value="">请选择省份</option>
                            <option value="安徽">安徽</option>
                            <option value="北京">北京</option>
                            <option value="重庆">重庆</option>
                            <option value="福建">福建</option>
                            <option value="甘肃">甘肃</option>
                            <option value="广东">广东</option>
                            <option value="广西">广西</option>
                            <option value="贵州">贵州</option>
                            <option value="海南">海南</option>
                            <option value="河北">河北</option>
                            <option value="河南">河南</option>
                            <option value="黑龙江">黑龙江</option>
                            <option value="湖北">湖北</option>
                            <option value="湖南">湖南</option>
                            <option value="吉林">吉林</option>
                            <option value="江苏">江苏</option>
                            <option value="江西">江西</option>
                            <option value="辽宁">辽宁</option>
                            <option value="内蒙古">内蒙古</option>
                            <option value="宁夏">宁夏</option>
                            <option value="青海">青海</option>
                            <option value="山东">山东</option>
                            <option value="山西">山西</option>
                            <option value="陕西">陕西</option>
                            <option value="上海">上海</option>
                            <option value="四川">四川</option>
                            <option value="天津">天津</option>
                            <option value="西藏">西藏</option>
                            <option value="新疆">新疆</option>
                            <option value="云南">云南</option>
                            <option value="浙江">浙江</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">选择城市</label>
                        <select class="form-control" id="modalCitySelect">
                            <option value="">请先选择省份</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmProvinceCity()">确定</button>
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" id="ruleIdInput" value="">

    <!-- 规则搜索模态框 -->
    <div class="modal fade" id="ruleSearchModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">选择匹配规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <input type="text" class="form-control" id="ruleSearchInput" 
                               placeholder="输入关键词搜索规则">
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover" id="ruleSearchTable">
                            <thead>
                                <tr>
                                    <th>规则名称</th>
                                    <th>规则来源</th>
                                    <th>规则内涵</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加模板选择模态框 -->
    <div class="modal fade" id="templateModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">选择SQL模板</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">模板来源</label>
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="templateSource" id="systemTemplate" value="system" checked>
                            <label class="btn btn-outline-primary" for="systemTemplate">系统模板</label>
                            
                            <input type="radio" class="btn-check" name="templateSource" id="localTemplate" value="local">
                            <label class="btn btn-outline-primary" for="localTemplate">本地模板</label>
                        </div>
                    </div>
                    
                    <!-- 系统模板列表 -->
                    <div id="systemTemplateList" class="mb-3">
                        <label class="form-label">选择系统模板</label>
                        <select class="form-select" id="systemTemplateSelect">
                            <option value="">请选择模板</option>
                        </select>
                    </div>
                    
                    <!-- 本地模板选择 -->
                    <div id="localTemplateList" class="mb-3" style="display: none;">
                        <label class="form-label">选择本地模板文件</label>
                        <input type="file" class="form-control" id="localTemplateFile" accept=".sql">
                        <small class="text-muted">支持.sql格式的模板文件</small>
                    </div>
                    
                    <!-- 模板预览 -->
                    <div class="mb-3">
                        <label class="form-label">模板预览</label>
                        <pre><code id="templatePreview" class="language-sql"></code></pre>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="generateSQL()">生成SQL</button>
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" id="sourceRuleIdsInput">

    <!-- 目标规则选择模态框 -->
    <div class="modal fade" id="targetRuleSelectionModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">选择目标规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="sourceRuleIdsInput">
                    <div class="mb-3">
                        <label for="targetRuleSelect" class="form-label">请选择要合并到的目标规则：</label>
                        <select id="targetRuleSelect" class="form-select">
                            <option value="">请选择目标规则</option>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        所有选中的规则将被合并到目标规则中，此操作不可撤销。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmBatchMerge()">确认合并</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量AI智能获取模态框 -->
    <div class="modal fade" id="batchAiModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量AI智能获取</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="fw-bold">已选择 <span id="batchSelectedCount">0</span> 条规则</span>
                                <small class="text-muted ms-2">AI将分析每条规则的内容，自动填充医保名称、违规类型等信息</small>
                            </div>
                            <div>
                                <button class="btn btn-success" onclick="startBatchAiProcess()" id="startBatchBtn">
                                    <i class="bi bi-magic"></i> 开始批量处理
                                </button>
                                <button class="btn btn-warning ms-2" onclick="manualCancelBatch()" id="cancelBatchBtn" disabled>
                                    <i class="bi bi-x-circle"></i> 取消处理
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 进度条 -->
                    <div class="mb-3" id="batchProgressContainer" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>处理进度</span>
                            <span id="batchProgressText">0/0</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar"
                                 id="batchProgressBar"
                                 style="width: 0%">
                            </div>
                        </div>
                    </div>

                    <!-- 处理结果列表 -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="batchResultTable">
                            <thead>
                                <tr>
                                    <th width="5%">序号</th>
                                    <th width="15%">规则名称</th>
                                    <th width="10%">城市</th>
                                    <th width="10%">状态</th>
                                    <th width="15%">医保名称1</th>
                                    <th width="15%">医保名称2</th>
                                    <th width="10%">类型</th>
                                    <th width="10%">违规数量</th>
                                    <th width="10%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="batchResultBody">
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="saveBatchResults()" id="saveBatchBtn" disabled>
                        <i class="bi bi-save"></i> 保存所有结果
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        // 全局变量
        let editingId = null;
        let table; // 数据表格对象
        let provinceModal;
        let currentMedicalIndex = 1;
        let medicalTable;
        let selectedMedicalItems = new Map();

        // 存储预览数据
        let previewData = [];



        // 收集医保编码对照数据
        function collectMedicalMappings() {
            const mappings = [];
            document.querySelectorAll('.regional-code-row').forEach(row => {
                mappings.push({
                    seq_no: row.querySelector('.seq-no').value,
                    province: row.querySelector('.province-select').value,
                    city: row.querySelector('.city-select').value,
                    rule_content: row.querySelector('.rule-content').value,
                    medical_code1: row.querySelector('.medical-code1').value,
                    medical_name1: row.querySelector('.medical-name1').value,
                    medical_code2: row.querySelector('.medical-code2').value,
                    medical_name2: row.querySelector('.medical-name2').value,
                    price_code: row.querySelector('.price-code').value,
                    price_name: row.querySelector('.price-name').value
                });
            });
            return mappings;
        }

        // 显示提示消息
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`;
            toast.style.zIndex = '1050';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        $(document).ready(function() {
            // 确保表格元素存在
            if ($('#rulesTable').length) {
                initializeDataTable();
            } else {
                console.error('Table element not found!');
            }

            // 下拉数据加载函数
            function loadSelectOptions(url, selectId) {
                $.get(url, function(res) {
                    if (res.success && Array.isArray(res.types)) {
                        const $select = $(selectId);
                        $select.empty();
                        $select.append('<option value="">全部</option>');
                        if (selectId === '#searchType' || selectId === '#searchRuleType') {
                            $select.append('<option value="__unset__">未设置</option>');
                        }
                        res.types.forEach(function(item) {
                            $select.append('<option value="' + item + '">' + item + '</option>');
                        });
                    }
                });
            }
            loadSelectOptions('/api/city_types', '#searchCity');
            loadSelectOptions('/api/type_types', '#searchType');
            loadSelectOptions('/api/rule_type_types', '#searchRuleType');
            loadSelectOptions('/api/rule_sources', '#searchRuleSource');
            loadSelectOptions('/api/behavior_types', '#searchBehavior');
        });

        function initializeDataTable() {
            try {
                table = $('#rulesTable').DataTable({
                    ajax: {
                        url: '/api/rules',
                        dataSrc: function(response) {
                            if (!response || response.error) {
                                console.error('数据加载错误:', response.error || '未知错误');
                                return [];
                            }
                            return response.map(item => ({
                                ID: item.ID || '',
                                规则来源: item.规则来源 || '',                                
                                序号: item.序号 || '',
                                规则名称: item.规则名称 || '',
                                城市: item.城市 || '',
                                行为认定: item.行为认定 || '',
                                医保编码1: item.医保编码1 || '',
                                医保名称1: item.医保名称1 || '',
                                医保编码2: item.医保编码2 || '',
                                医保名称2: item.医保名称2 || '',
                                规则内涵: item.规则内涵 || '',
                                类型: item.类型 || '',
                                规则类型: item.规则类型 || '',
                                用途: item.用途 || ''
                            }));
                        }
                    },
                    columns: [
                        { data: null,
                            defaultContent: '',
                            orderable: false,
                            render: function (data, type, row, meta) {
                                return `<input type="checkbox" class="rule-checkbox" value="${row.id || meta.row}" onchange="handleCheckboxChange()">`;
                            }
                        },
                        { data: 'ID', width: '4%' },
                        {   data: '规则来源',
                            width: '8%' ,
                            render: function(data) {
                                return `<div class="text-wrap" style="white-space: pre-line;">${data ? data.split(',').join('<br>') : ''}</div>`;
                            }
                        },
                        { data: '序号', width: '4%' },
                        { data: '规则名称', width: '10%' ,render: function(data) {
                            return `<div class="text-wrap" style="white-space: pre-line;">${data ? data.split(',').join('<br>') : ''}</div>`;
                        }
                        },
                        { 
                            data: '城市', 
                            width: '4%',
                            render: function(data) {
                                return `<div class="text-wrap" style="white-space: pre-line;">${data ? data.split(',').join('<br>') : ''}</div>`;
                            }
                        },
                        { 
                            data: '类型',
                            width: '5%',
                            render: function(data) {
                                return `<div class="text-wrap" style="white-space: pre-line;">${data || ''}</div>`;
                            }
                        },
                        { 
                            data: '规则类型',
                            width: '6%',
                            render: function(data) {
                                return `<div class="text-wrap" style="white-space: pre-line;">${data || ''}</div>`;
                            }
                        },
                        { 
                            data: '行为认定',
                            width: '8%',
                            render: function(data) {
                                return `<div class="text-wrap" style="white-space: pre-line;">${data || ''}</div>`;
                            }
                        },
                        { 
                            data: '用途',
                            width: '4%',
                            render: function(data) {
                                return `<div class="text-wrap" style="white-space: pre-line;">${data || ''}</div>`;
                            }
                        },
                        { 
                            data: '医保编码1',
                            width: '8%',
                            render: function(data) {
                                return `<div class="text-wrap" style="white-space: pre-line;">${data ? data.split(',').join('<br>') : ''}</div>`;
                            }
                        },
                        { 
                            data: '医保名称1',
                            width: '10%',
                            render: function(data) {
                                return `<div class="text-wrap" style="white-space: pre-line;">${data ? data.split(',').join('<br>') : ''}</div>`;
                            }
                        },
                        { 
                            data: '医保编码2',
                            width: '8%',
                            render: function(data) {
                                return `<div class="text-wrap" style="white-space: pre-line;">${data ? data.split(',').join('<br>') : ''}</div>`;
                            }
                        },
                        { 
                            data: '医保名称2',
                            width: '10%',
                            render: function(data) {
                                return `<div class="text-wrap" style="white-space: pre-line;">${data ? data.split(',').join('<br>') : ''}</div>`;
                            }
                        },
                        { 
                            data: '规则内涵',
                            width: '20%',
                            render: function(data) {
                                return `<div class="text-wrap" style="white-space: pre-line;">${data || ''}</div>`;
                            }
                        },
                        {
                            data: null,
                            width: '8%',
                            render: function(data, type, row) {
                                return `
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-primary" onclick="editRule(${row.ID})">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteRule(${row.ID})">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                `;
                            }
                        }
                    ],
                    order: [[1, 'asc']], // 改为按ID列排序，避免对复选框列排序
                    pageLength: 10,
                    scrollX: true,
                    scrollY: '600px',
                    scrollCollapse: true,
                    autoWidth: false,
                    responsive: true,
                    createdRow: function(row, data, dataIndex) {
                        $(row).addClass('align-middle');
                    },
                    columnDefs: [
                        {
                            targets: 0, // 复选框列
                            defaultContent: '',
                            orderable: false, // 禁用排序
                            className: 'select-checkbox align-middle',
                            width: '30px'
                        },
                        {
                            targets: '_all',
                            className: 'align-middle'
                        }
                    ]
                });

                // 添加表格样式
                $('<style>')
                    .prop('type', 'text/css')
                    .html(`
                        .dataTables_wrapper .table {
                            margin-top: 1rem !important;
                        }
                        .table > thead > tr > th {
                            background-color: #f8f9fa;
                            font-weight: 600;
                            padding: 12px 8px;
                            vertical-align: middle;
                        }
                        .table > tbody > tr > td {
                            padding: 8px;
                            font-size: 0.9rem;
                        }
                        .text-wrap {
                            word-break: break-word;
                            max-height: 100px;
                            overflow-y: auto;
                        }
                        .btn-group .btn {
                            padding: 0.25rem 0.5rem;
                        }
                    `)
                    .appendTo('head');

            } catch (error) {
                console.error('DataTable initialization error:', error);
            }
        }

        function addNewRule() {
            console.log('添加新规则...');
            editingId = null;
            $('#ruleForm')[0].reset();
            
            // 获取下一个可用的 ID
            fetch('/api/rules/next-id')
                .then(response => response.json())
                .then(data => {
                    console.log('获取到下一个ID:', data.next_id);
                    $('[name="id"]').val(data.next_id);
                    $('[name="序号"]').val(data.next_id);
                })
                .catch(error => {
                    console.error('获取下一个ID失败:', error);
                });
            
            try {
                // 打开模态框
                console.log('打开模态框...');
                if (window.ruleModal) {
                    window.ruleModal.show();
                    console.log('使用全局ruleModal对象打开模态框');
                } else {
                    console.log('全局ruleModal不存在，尝试创建新的模态框实例');
                    const ruleModal = new bootstrap.Modal(document.getElementById('ruleModal'));
                    ruleModal.show();
                }
                console.log('模态框已打开');
            } catch (error) {
                console.error('打开模态框失败:', error);
                alert('打开新增规则窗口失败，请刷新页面重试');
            }
        }


        function editRule(id) {
            if (!id || isNaN(id)) {
                console.error('Invalid rule ID:', id);
                showToast('无效的规则ID', 'error');
                return;
            }

            console.log('Editing rule:', id);
            
            fetch(`/api/rules/${id}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    
                    console.log('Rule data:', data);
                    
                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('ruleModal'));
                    modal.show();
                    
                    // 填充表单数据
                    const form = document.getElementById('ruleForm');
                    form.reset();
                    
                    // 设置 ID 字段
                    form.elements['id'].value = data.ID || '';
                    
                    // 填充城市下拉框
                    const citySelect = document.getElementById('citySelect');
                    citySelect.innerHTML = ''; // 清空现有选项
                    
                    // 添加城市选项
                    if (data.cities && Array.isArray(data.cities)) {
                        data.cities.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city;
                            option.textContent = city;
                            citySelect.appendChild(option);
                        });
                        
                        // 设置当前选中的城市
                        if (data.城市) {
                            citySelect.value = data.城市;
                        }
                    }
                    
                    // 首先单独处理性别字段
                    if (data.性别) {
                        const sexSelect = $('select[name="性别"]');
                        if (sexSelect.length) {
                            // 确保选项已经存在
                            sexSelect.html(`
                                <option value="">请选择</option>
                                <option value="男">男</option>
                                <option value="女">女</option>
                            `);
                            
                            // 设置选中值
                            sexSelect.val(data.性别);
                            
                            // 如果设置失败，使用selectedIndex
                            if (sexSelect.val() !== data.性别) {
                                const options = sexSelect[0].options;
                                for (let i = 0; i < options.length; i++) {
                                    if (options[i].value === data.性别) {
                                        sexSelect[0].selectedIndex = i;
                                        break;
                                    }
                                }
                            }
                            
                            // 触发change事件
                            sexSelect.trigger('change');
                        }
                    }
                    
                    // 遍历所有表单字段并填充数据
                    for (let element of form.elements) {
                        const name = element.name;
                        if (name && name in data) {
                            if (element.type === 'date' && data[name]) {
                                element.value = data[name].split('T')[0];
                            } else if (element.tagName === 'SELECT') {
                                // 对于SELECT元素特殊处理
                                const value = data[name] || '';
                                console.log(`正在设置SELECT字段 ${name} 的值:`, value);
                                
                                // 先确保选项存在
                                let optionExists = false;
                                for (let option of element.options) {
                                    if (option.value === value) {
                                        optionExists = true;
                                        break;
                                    }
                                }
                                
                                // 如果选项不存在且值不为空，添加选项
                                if (!optionExists && value) {
                                    const newOption = new Option(value, value, true, true);
                                    element.add(newOption);
                                }
                                
                                // 设置值
                                element.value = value;
                                console.log(`设置后的值:`, element.value);
                                
                                // 如果设置失败，尝试使用selectedIndex
                                if (element.value !== value) {
                                    for (let i = 0; i < element.options.length; i++) {
                                        if (element.options[i].value === value) {
                                            element.selectedIndex = i;
                                            break;
                                        }
                                    }
                                }
                                
                                // 再次检查是否设置成功
                                console.log(`最终的值:`, element.value);
                            } else {
                                element.value = data[name] || '';
                            }
                        }
                    }
                    
                    // 保存当前编辑的ID
                    editingId = id;
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('获取规则数据失败: ' + error.message, 'error');
                });
        }

        function saveRule() {
            // 收集表单数据
            const formData = {};
            $('#ruleForm').serializeArray().forEach(item => {
                // 处理日期字段
                if (['发布时间', '生效日期', '失效日期'].includes(item.name)) {
                    formData[item.name] = item.value || null;  // 如果为空字符串则设为 null
                } else if (item.name === '性别') {
                    // 清理性别字段，去除首尾空格，并确保只保存有效值
                    let genderValue = item.value ? item.value.trim() : '';
                    // 只允许"男"、"女"或空值
                    if (genderValue === '男' || genderValue === '女') {
                        formData[item.name] = genderValue;
                    } else {
                        formData[item.name] = null;
                    }
                } else {
                    // 对所有字符串字段进行trim处理
                    if (typeof item.value === 'string') {
                        formData[item.name] = item.value.trim() || null;
                    } else {
                        formData[item.name] = item.value;
                    }
                }
            });

            // 输出表单数据到控制台，帮助调试
            console.log('表单数据:', formData);
            console.log('是否是编辑模式:', editingId ? true : false);
            console.log('编辑ID:', editingId);
            console.log('表单ID:', formData.id);
            
            const ruleId = formData.id;
            const city = document.getElementById('citySelect').value;
            
            // 保存当前页码
            const currentPage = table.page();

            // 先保存规则基本信息
            const url = editingId ? `/api/rules/${editingId}` : '/api/rules';
            const method = editingId ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(async response => {
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.error || '保存失败');
                }
                
                console.log('规则保存成功，返回数据:', data);
                
                // 如果保存成功，并且选择了城市，则保存医保编码对照
                if (city && city !== '') {
                    // 构建医保编码对照数据
                    const medicalCodeData = {
                        规则ID: ruleId,
                        省份: document.getElementById('provinceSelect')?.value || '',
                        城市: city,
                        规则内涵: formData.规则内涵,
                        规则来源: formData.规则来源,
                        医保编码1: formData.医保编码1,
                        医保名称1: formData.医保名称1,
                        医保编码2: formData.医保编码2,
                        医保名称2: formData.医保名称2,
                        国家医保编码1: formData.国家医保编码1,
                        国家医保名称1: formData.国家医保名称1,
                        国家医保编码2: formData.国家医保编码2,
                        国家医保名称2: formData.国家医保名称2,
                        物价编码: formData.物价编码,
                        创建时间: formData.创建时间,
                        更新时间: formData.更新时间,
                        性别: formData.性别, // 添加性别字段
                        年龄: formData.年龄, // 添加年龄字段
                        违规数量: formData.违规数量, // 添加违规数量字段
                        违规金额: formData.违规金额, // 添加违规金额字段
                        排除诊断: formData.排除诊断, // 添加排除诊断字段
                        排除科室: formData.排除科室, // 添加排除科室字段
                        包含诊断: formData.包含诊断, // 添加包含诊断字段
                        包含科室: formData.包含科室, // 添加包含科室字段
                        所属领域: formData.所属领域, // 添加所属领域字段
                        用途: formData.用途 // 添加用途字段
                    };

                    // 城市和规则iD（如果存在）
                    fetch(`/api/rules/city/${encodeURIComponent(city)}/${ruleId}`)
                        .then(response => response.json())
                        .then(cityData => {
                            if (cityData.COMPARE_ID) {
                                // 如果存在对照ID，执行更新操作
                                return fetch(`/api/rules/${ruleId}`, {
                                    method: 'PUT',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify({
                                        ...medicalCodeData,
                                        对照ID: cityData.COMPARE_ID
                                    })
                                });
                            } else {
                                // 如果不存在对照ID，执行新增操作
                                return fetch(`/api/rule_medical_codes/${ruleId}`, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify(medicalCodeData)
                                });
                            }
                        })
                        .then(response => response.json())
                        .then(result => {
                            if (!result.success) {
                                throw new Error(result.error || '保存医保编码对照失败');
                            }
                            
                            // 全部保存成功，关闭模态框并刷新表格
                            const ruleModal = bootstrap.Modal.getInstance(document.getElementById('ruleModal'));
                            ruleModal.hide();
                            table.ajax.reload(() => {
                                if (editingId) {
                                    table.page(currentPage).draw('page');
                                } else {
                                    table.page('last').draw('page');
                                }
                            }, false);
                            
                            showToast('保存成功');
                        })
                        .catch(error => {
                            console.error('保存医保编码对照失败:', error);
                            showToast('保存医保编码对照失败: ' + error.message, 'error');
                        });
                } else {
                    // 如果没有选择城市，直接关闭模态框并刷新表格
                    const ruleModal = bootstrap.Modal.getInstance(document.getElementById('ruleModal'));
                    ruleModal.hide();
                    table.ajax.reload(() => {
                        if (editingId) {
                            table.page(currentPage).draw('page');
                        } else {
                            table.page('last').draw('page');
                        }
                    }, false);
                    
                    showToast('保存成功');
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                showToast('保存失败: ' + error.message, 'error');
            });
        }

        function deleteRule(id) {
            if (confirm('确定要删除这条规则吗？')) {
                fetch(`/api/rules/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    table.ajax.reload();
                })
                .catch(error => {
                    alert('删除失败：' + error.message);
                });
            }
        }

        function searchRules() {
            // 获取搜索条件
            const searchParams = {
                ruleName: $('#searchRuleName').val(),
                ruleSource: $('#searchRuleSource').val(),
                city: $('#searchCity').val(),
                type: $('#searchType').val(),
                ruleType: $('#searchRuleType').val(),
                behavior: $('#searchBehavior').val(),
                ruleContent: $('#searchRuleContent').val()
            };

            // 重置所有列的搜索
            table.columns().search('').draw();

            // 应用搜索条件到对应列
            if (Object.values(searchParams).some(value => value)) {
                table.columns().every(function() {
                    const column = this;
                    const columnIndex = column.index();
                    switch(columnIndex) {
                        case 2: // 规则来源
                            if (searchParams.ruleSource) {
                                const searchValue = processSearchValue(searchParams.ruleSource);
                                column.search(searchValue, true, false, true);
                            }
                            break;
                        case 4: // 规则名称
                            if (searchParams.ruleName) {
                                const searchValue = processSearchValue(searchParams.ruleName);
                                column.search(searchValue, true, false, true);
                            }
                            break;
                        case 5: // 城市
                            if (searchParams.city) {
                                const searchValue = processSearchValue(searchParams.city);
                                column.search(searchValue, true, false, true);
                            }
                            break;
                        case 8: // 行为认定
                            if (searchParams.behavior) {
                                const searchValue = processSearchValue(searchParams.behavior);
                                column.search(searchValue, true, false, true);
                            }
                            break;
                        case 6: // 类型
                            if (searchParams.type) {
                                if (searchParams.type === '__unset__') {
                                    column.search('^\s*$', true, false, true); // 匹配空字符串
                                } else {
                                    const searchValue = processSearchValue(searchParams.type);
                                    column.search(searchValue, true, false, true);
                                }
                            }
                            break;
                        case 7: // 规则类型
                            if (searchParams.ruleType) {
                                if (searchParams.ruleType === '__unset__') {
                                    column.search('^\s*$', true, false, true); // 匹配空字符串
                                } else {
                                    const searchValue = processSearchValue(searchParams.ruleType);
                                    column.search(searchValue, true, false, true);
                                }
                            }
                            break;
                        case 11: // 规则内涵
                            if (searchParams.ruleContent) {
                                const searchValue = processSearchValue(searchParams.ruleContent);
                                column.search(searchValue, true, false, true);
                            }
                            break;
                    }
                });
            }
            table.draw();
        }

        // 处理搜索值，支持%作为通配符，并转义特殊字符
        function processSearchValue(value) {
            if (!value) return '';
            
            // 转义正则表达式中的特殊字符，但保留%
            let escaped = value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            
            // 将%替换为.*（正则表达式中的任意字符匹配）
            escaped = escaped.replace(/%/g, '.*');
            
            return escaped;
        }

        function getSearchValueForColumn(columnIndex, searchParams) {
            // 列索引映射到搜索参数
            const columnMapping = {
                2: searchParams.ruleSource,  // 规则来源
                4: searchParams.ruleName,    // 规则名称
                5: searchParams.scope,       // 适用范围
                6: searchParams.city,        // 城市
                7: searchParams.behavior,    // 行为认定
                11: searchParams.ruleContent  // 规则内涵
            };
            
            return columnMapping[columnIndex] || '';
        }

        function resetSearch() {
            // 重置所有搜索输入框
            $('#searchRuleName').val('');
            $('#searchRuleSource').val('');
            $('#searchCity').val('');
            $('#searchScope').val('');
            $('#searchBehavior').val('');
            $('#searchRuleContent').val('');
            
            // 重置表格搜索状态
            table.search('').columns().search('').draw();
        }

        
        function searchMedicalInsurance(index) {
            currentMedicalIndex = index;
            
            // 获取城市值
            const cityValue = document.getElementById('citySelect')?.value || '';
            
            if (!cityValue) {
                showToast('请先选择城市！', 'warning');
                return;
            }
            // 重置搜索框
            $('#searchMedicalCode').val('');
            $('#searchMedicalName').val('');
            $('#searchCountryCode').val('');
            $('#searchPriceCode').val('');
            $('#selectAllMedical').prop('checked', false);
            $('#selectAllMedical').prop('indeterminate', false);

            selectedMedicalItems.clear();
            
            // 确定当前编辑的字段名称
            let codeFieldName, nameFieldName;
            
            // 根据索引确定字段名称
            switch(index) {
                case 1:
                    codeFieldName = '医保编码1';
                    nameFieldName = '医保名称1';
                    break;
                case 2:
                    codeFieldName = '医保编码2';
                    nameFieldName = '医保名称2';
                    break;
                case 3:
                    codeFieldName = '国家医保编码1';
                    nameFieldName = '国家医保名称1';
                    break;
                case 4:
                    codeFieldName = '国家医保编码2';
                    nameFieldName = '国家医保名称2';
                    break;
                default:
                    console.error('无效的索引:', index);
                    return;
            }

            // 从当前输入框中获取已选择的项目
            const codeElement = document.getElementById(codeFieldName);
            const nameElement = document.getElementById(nameFieldName);
            
            if (codeElement && nameElement) {
                const currentCodes = codeElement.value;
                const currentNames = nameElement.value;
                
                if (currentCodes && currentNames) {
                    const codes = currentCodes.split(',');
                    const names = currentNames.split(',');
                    
                    codes.forEach((code, idx) => {
                        if (code && idx < names.length && names[idx]) {
                            selectedMedicalItems.set(code.trim(), names[idx].trim());
                        }
                    });
                }
            }
            
            // 更新显示
            updateSelectedItemsDisplay();
            
            // 初始化表格
            initMedicalTable();
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('medicalInsuranceModal'));
            modal.show();
        }

        // 初始化医保编码检索表格

        // 修改 initMedicalTable 函数，添加日志记录
        function initMedicalTable() {
            console.log('开始初始化医保编码表格');
            
            if (medicalTable) {
                medicalTable.destroy();
            }
            
            // 获取城市值
            const cityValue = document.getElementById('citySelect')?.value || '';

            document.getElementById('currentQueryCity').textContent = cityValue;
            
            let searchTimeout;
            
            medicalTable = $('#medicalInsuranceTable').DataTable({
                ajax: {
                    url: '/api/medical-insurance',
                    type: 'GET',
                    data: function(d) {
                        // 处理搜索关键词，支持多个分隔符
                        const searchName = $('#searchMedicalName').val();
                        const searchCode = $('#searchMedicalCode').val();
                        const searchCountryCode = $('#searchCountryCode').val();
                        const searchPriceCode = $('#searchPriceCode').val();
                        
                        // 处理所有搜索字段，支持多个分隔符
                        const processKeywords = (input) => {
                            if (!input) return '';
                            const keywords = input.split(/[,，、""'']/); // 支持逗号、顿号、引号等分隔符
                            return keywords.map(k => k.trim()).filter(k => k).join('|'); // 用|连接多个关键词
                        };
                        
                        const params = {
                            city: cityValue,
                            code: processKeywords(searchCode),
                            name: processKeywords(searchName),
                            country_code: processKeywords(searchCountryCode),
                            price_code: processKeywords(searchPriceCode)
                        };
                        
                        console.log('发送查询参数:', params);
                        return params;
                    },
                    dataSrc: function(json) {
                        console.log('接收到数据:', json);
                        if (!json.data || !Array.isArray(json.data)) {
                            console.error('接收到的数据格式不正确:', json);
                            return [];
                        }
                        return json.data;
                    },
                    error: function(xhr, error, thrown) {
                        console.error('AJAX请求失败:', error, thrown);
                        console.log('响应内容:', xhr.responseText);
                    }
                },
                columns: [
                    {
                        data: null,
                        render: function(data, type, row) {
                            try {
                                // 添加安全处理
                                const code = row.code || '';
                                const name = row.name || '';
                                const isChecked = selectedMedicalItems.has(code) ? 'checked' : '';
                                
                                // 添加data-code和data-name属性以便更容易选择元素
                                return `<input type="checkbox" class="medical-select" ${isChecked} 
                                       data-code="${code}" data-name="${name}"
                                       onclick="toggleMedicalSelection('${encodeURIComponent(code)}', '${encodeURIComponent(name)}')">`;
                            } catch (e) {
                                console.error('渲染复选框时出错:', e, '行数据:', row);
                                return '<input type="checkbox" class="medical-select">';
                            }
                        }
                    },
                    { data: 'code', title: '医保编码' },
                    { data: 'name', title: '医保名称' },
                    { data: 'country_code', title: '国家编码' },
                    { data: 'price_code', title: '物价码' },
                    { data: 'unit', title: '单位', defaultContent: '' },
                    { 
                        data: 'unit_price', 
                        title: '单价',
                        render: function(data, type, row) {
                            return data ? `¥${parseFloat(data).toFixed(2)}` : '';
                        }
                    },
                    { 
                        data: null,
                        render: function(data, type, row) {
                            try {
                                return `<button class="btn btn-sm btn-primary" onclick="selectSingleItem('${row.code}', '${row.name}')">选择</button>`;
                            } catch (e) {
                                console.error('渲染选择按钮时出错:', e, '行数据:', row);
                                return '<button class="btn btn-sm btn-primary">选择</button>';
                            }
                        }
                    }
                ],
                language: {
                    "sProcessing": "处理中...",
                    "sLengthMenu": "显示 _MENU_ 项结果",
                    "sZeroRecords": "没有匹配结果",
                    "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                    "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
                    "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
                    "sInfoPostFix": "",
                    "sSearch": "搜索:",
                    "sUrl": "",
                    "sEmptyTable": "表中数据为空",
                    "sLoadingRecords": "载入中...",
                    "sInfoThousands": ",",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "上页",
                        "sNext": "下页",
                        "sLast": "末页"
                    }
                },
                pageLength: 10,
                dom: '<"top"f>rt<"bottom"lip><"clear">',
                searching: false,  // 禁用DataTables默认的搜索框
                initComplete: function() {
                    console.log('表格初始化完成');
                }
            });

            // 添加表格事件监听
            medicalTable.on('xhr', function() {
                console.log('AJAX请求完成');
            });
            
            medicalTable.on('error', function(e, settings, techNote, message) {
                console.error('表格错误:', message);
            });
            
            // Update search event listeners
            $('#searchMedicalCode, #searchMedicalName, #searchCountryCode, #searchPriceCode').on('input', function() {
                console.log('搜索输入变化:', this.id, '=', this.value);
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    console.log('执行搜索刷新');
                    medicalTable.ajax.reload();
                }, 500);
            });
        }

        // 修改 toggleMedicalSelection 函数，更新全选框状态
        function toggleMedicalSelection(code, name) {
            // 对特殊字符进行正确的转义处理
            const safeCode = decodeURIComponent(code).replace(/['"\\]/g, '');
            const safeName = decodeURIComponent(name).replace(/['"\\]/g, '');
            
            console.log('Toggle medical selection:', safeCode, safeName);
            
            // 更新选中状态
            if (selectedMedicalItems.has(safeCode)) {
                selectedMedicalItems.delete(safeCode);
                // 找到对应的复选框并取消选中
                const checkbox = document.querySelector(`.medical-select[data-code="${safeCode}"]`);
                if (checkbox) checkbox.checked = false;
            } else {
                selectedMedicalItems.set(safeCode, safeName);
                // 找到对应的复选框并选中
                const checkbox = document.querySelector(`.medical-select[data-code="${safeCode}"]`);
                if (checkbox) checkbox.checked = true;
            }
            
            // 更新全选框状态
            const selectAllCheckbox = document.getElementById('selectAllMedical');
            const checkboxes = document.querySelectorAll('#medicalInsuranceTable .medical-select');
            const checkedBoxes = document.querySelectorAll('#medicalInsuranceTable .medical-select:checked');
            
            if (checkboxes.length > 0) {
                selectAllCheckbox.checked = checkboxes.length === checkedBoxes.length;
                selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
            }
            
            updateSelectedItemsDisplay();
        }        


        // 修复已选择项目显示问题
        function updateSelectedItemsDisplay() {
            const container = document.getElementById('selectedMedicalItems');
            if (!container) {
                console.error('未找到已选择项目容器元素');
                return;
            }
            
            container.innerHTML = '';
            
            // 从 Map 中获取所有选中项
            const items = Array.from(selectedMedicalItems.entries());
            console.log('更新显示，当前选中项目数量:', items.length, items);
            
            if (items.length > 0) {
                const itemsHtml = items.map(([code, name]) => {
                    return `
                        <div class="badge bg-primary me-2 mb-2">
                            ${code} - ${name}
                            <button type="button" class="btn-close btn-close-white ms-2" 
                                    onclick="toggleMedicalSelection('${encodeURIComponent(code)}', '${encodeURIComponent(name)}')"
                                    style="font-size: 0.5em;"></button>
                        </div>`;
                }).join('');
                container.innerHTML = itemsHtml;
            } else {
                container.innerHTML = '<div class="text-muted">暂无选择项目</div>';
            }
        }


        function confirmMedicalSelection() {
            try {
                const items = Array.from(selectedMedicalItems.entries());
                if (items.length === 0) {
                    alert('请至少选择一个医保项目');
                    return;
                }

                // 分别收集编码和名称
                const selectedCodes = [];
                const selectedNames = [];
                
                items.forEach(([code, name]) => {
                    if (code && name) {  // 确保编码和名称都存在
                        selectedCodes.push(code);
                        selectedNames.push(name);
                    }
                });

                // 确定当前编辑的字段名称
                let codeFieldName, nameFieldName;
                
                // 根据索引确定字段名称
                switch(currentMedicalIndex) {
                    case 1:
                        codeFieldName = '医保编码1';
                        nameFieldName = '医保名称1';
                        break;
                    case 2:
                        codeFieldName = '医保编码2';
                        nameFieldName = '医保名称2';
                        break;
                    case 3:
                        codeFieldName = '国家医保编码1';
                        nameFieldName = '国家医保名称1';
                        break;
                    case 4:
                        codeFieldName = '国家医保编码2';
                        nameFieldName = '国家医保名称2';
                        break;
                    default:
                        console.error('无效的索引:', currentMedicalIndex);
                        return;
                }

                // 将选中的项目填入对应的输入框
                const uniqueCodes = [...new Set(selectedCodes)]; // 去除重复的编码
                const uniqueNames = [...new Set(selectedNames)]; // 去除重复的名称

                const codeElement = document.getElementById(codeFieldName);
                const nameElement = document.getElementById(nameFieldName);
                
                if (codeElement && nameElement) {
                    codeElement.value = uniqueCodes.join(',');
                    nameElement.value = uniqueNames.join(',');
                    
                    console.log('已确认选择:', codeFieldName, uniqueCodes.join(','), nameFieldName, uniqueNames.join(','));
                } else {
                    console.error('未找到对应的医保编码或名称输入元素');
                }
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('medicalInsuranceModal'));
                if (modal) {
                    modal.hide();
                }

                // 清空选择
                selectedMedicalItems.clear();
                updateSelectedItemsDisplay();
                
                showToast('医保项目选择已确认', 'success');
            } catch (error) {
                console.error('确认医保信息选择时出错:', error);
                showToast('确认选择失败: ' + error.message, 'error');
            }
        }

        function selectSingleItem(code, name) {
            // 防止 XSS 和特殊字符导致的问题
            const safeCode = code.replace(/['"]/g, '');
            const safeName = name.replace(/['"]/g, '');
            
            // 确定当前编辑的字段名称
            let codeFieldName, nameFieldName;
            
            // 根据索引确定字段名称
            switch(currentMedicalIndex) {
                case 1:
                    codeFieldName = '医保编码1';
                    nameFieldName = '医保名称1';
                    break;
                case 2:
                    codeFieldName = '医保编码2';
                    nameFieldName = '医保名称2';
                    break;
                case 3:
                    codeFieldName = '国家医保编码1';
                    nameFieldName = '国家医保名称1';
                    break;
                case 4:
                    codeFieldName = '国家医保编码2';
                    nameFieldName = '国家医保名称2';
                    break;
                default:
                    console.error('无效的索引:', currentMedicalIndex);
                    return;
            }
            
            document.getElementById(codeFieldName).value = safeCode;
            document.getElementById(nameFieldName).value = safeName;
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('medicalInsuranceModal'));
            if (modal) {
                modal.hide();
            }
            
            // 清空选择状态
            selectedMedicalItems.clear();
            updateSelectedItemsDisplay();
        }

        // 智能获取医保名称功能
        function intelligentGetMedicalCodes() {
            // 获取规则信息
            const ruleContent = document.querySelector('textarea[name="规则内涵"]')?.value?.trim() || '';
            const ruleName = document.querySelector('input[name="规则名称"]')?.value?.trim() || '';
            const behaviorType = document.querySelector('textarea[name="行为认定"]')?.value?.trim() || '';

            if (!ruleContent && !ruleName) {
                showToast('请先填写规则名称或规则内涵！', 'warning');
                return;
            }

            // 显示加载提示
            showLoading('正在智能分析规则内容，获取医保名称...');

            // 调用Gemini API
            fetch('/api/rules/intelligent-get-medical-names', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    rule_content: ruleContent,
                    rule_name: ruleName,
                    behavior_type: behaviorType
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success) {
                    // 成功获取规则信息，开始填充各个字段
                    let updateCount = 0;
                    let updateDetails = [];

                    // 填充医保名称
                    const medicalName1Element = document.getElementById('医保名称1');
                    const medicalName2Element = document.getElementById('医保名称2');

                    if (data.medical_name1 && medicalName1Element) {
                        medicalName1Element.value = data.medical_name1;
                        updateCount++;
                        updateDetails.push('医保名称1');
                    }

                    if (data.medical_name2 && medicalName2Element) {
                        medicalName2Element.value = data.medical_name2;
                        updateCount++;
                        updateDetails.push('医保名称2');
                    }

                    // 填充类型
                    const typeElement = document.querySelector('select[name="类型"]');
                    if (data.type && typeElement) {
                        // 检查选项是否存在，如果不存在则添加
                        let optionExists = false;
                        for (let option of typeElement.options) {
                            if (option.value === data.type) {
                                optionExists = true;
                                break;
                            }
                        }
                        if (!optionExists) {
                            const newOption = new Option(data.type, data.type, true, true);
                            typeElement.add(newOption);
                        }
                        typeElement.value = data.type;
                        updateCount++;
                        updateDetails.push('类型');
                    }

                    // 填充违规数量
                    const violationCountElement = document.querySelector('input[name="违规数量"]');
                    if (data.violation_count && violationCountElement) {
                        violationCountElement.value = data.violation_count;
                        updateCount++;
                        updateDetails.push('违规数量');
                    }

                    // 填充时间类型
                    const timeTypeElement = document.querySelector('select[name="时间类型"]');
                    if (data.time_type && timeTypeElement) {
                        // 检查选项是否存在，如果不存在则添加
                        let optionExists = false;
                        for (let option of timeTypeElement.options) {
                            if (option.value === data.time_type) {
                                optionExists = true;
                                break;
                            }
                        }
                        if (!optionExists) {
                            const newOption = new Option(data.time_type, data.time_type, true, true);
                            timeTypeElement.add(newOption);
                        }
                        timeTypeElement.value = data.time_type;
                        updateCount++;
                        updateDetails.push('时间类型');
                    }

                    // 填充违规金额
                    const violationAmountElement = document.querySelector('input[name="违规金额"]');
                    if (data.violation_amount && violationAmountElement) {
                        violationAmountElement.value = data.violation_amount;
                        updateCount++;
                        updateDetails.push('违规金额');
                    }

                    // 填充年龄
                    const ageElement = document.querySelector('input[name="年龄"]');
                    if (data.age_limit && ageElement) {
                        ageElement.value = data.age_limit;
                        updateCount++;
                        updateDetails.push('年龄');
                    }

                    // 填充性别
                    const genderElement = document.querySelector('select[name="性别"]');
                    if (data.gender_limit && genderElement) {
                        genderElement.value = data.gender_limit;
                        updateCount++;
                        updateDetails.push('性别');
                    }

                    // 填充排除诊断
                    const excludeDiagnosisElement = document.querySelector('input[name="排除诊断"]');
                    if (data.exclude_diagnosis && excludeDiagnosisElement) {
                        excludeDiagnosisElement.value = data.exclude_diagnosis;
                        updateCount++;
                        updateDetails.push('排除诊断');
                    }

                    // 填充排除科室
                    const excludeDepartmentsElement = document.querySelector('input[name="排除科室"]');
                    if (data.exclude_departments && excludeDepartmentsElement) {
                        excludeDepartmentsElement.value = data.exclude_departments;
                        updateCount++;
                        updateDetails.push('排除科室');
                    }

                    // 填充包含诊断
                    const includeDiagnosisElement = document.querySelector('input[name="包含诊断"]');
                    if (data.include_diagnosis && includeDiagnosisElement) {
                        includeDiagnosisElement.value = data.include_diagnosis;
                        updateCount++;
                        updateDetails.push('包含诊断');
                    }

                    // 填充包含科室
                    const includeDepartmentsElement = document.querySelector('input[name="包含科室"]');
                    if (data.include_departments && includeDepartmentsElement) {
                        includeDepartmentsElement.value = data.include_departments;
                        updateCount++;
                        updateDetails.push('包含科室');
                    }

                    if (updateCount > 0) {
                        let message = `智能分析成功！自动填充了 ${updateCount} 个字段：${updateDetails.join('、')}`;
                        if (data.confidence) {
                            message += `（置信度：${(data.confidence * 100).toFixed(1)}%）`;
                        }
                        showToast(message, 'success');

                        // 如果有推理过程，显示详细信息
                        if (data.reasoning) {
                            console.log('AI分析推理过程:', data.reasoning);
                        }
                    } else {
                        showToast('AI分析完成，但未能提取到有效的规则信息', 'warning');
                    }
                } else {
                    showToast(`智能分析失败：${data.error}`, 'error');
                    console.error('智能获取医保名称失败:', data);
                }
            })
            .catch(error => {
                hideLoading();
                console.error('智能获取医保名称请求失败:', error);
                showToast('智能获取请求失败: ' + error.message, 'error');
            });
        }


        function showImportModal() {
            // 清空文件选择
            document.getElementById('importFile').value = '';
            // 显示导入模态框
            const modal = new bootstrap.Modal(document.getElementById('importModal'));
            modal.show();
        }

        function handleImport() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];
            if (!file) {
                showToast('请选择要导入的Excel文件', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            // 显示加载提示
            showLoading('正在解析Excel文件...');

            // 发送预览请求
            fetch('/api/rules/preview', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(async data => {
                hideLoading();
                if (data.success) {
                    previewData = data.data;
                    await showPreviewModal(data.data);
                } else {
                    showToast(data.error || '预览失败', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                showToast('预览失败: ' + error.message, 'error');
                console.error('预览失败:', error);
            })
            .finally(() => {
                // 清空文件输入框，允许重复选择同一个文件
                fileInput.value = '';
            });
        }

        // 在发送预览请求前，先检查规则是否存在
        async function showPreviewModal(data) {
            const tbody = document.querySelector('#previewTable tbody');
            tbody.innerHTML = '';
            
            // 获取已存在的规则检查结果
            const checkResults = await checkExistingRules(data);
            
            data.forEach((rule, index) => {
                const tr = document.createElement('tr');
                const isRuleExists = checkResults.existingRules.includes(rule.规则名称);
                const isCityRuleExists = checkResults.existingCityRules.some(cr => 
                    cr.ruleName === rule.规则名称 && cr.city === rule.城市
                );
                
                // 根据存在状态添加样式类
                const rowClass = isRuleExists ? 'table-success' : '';
                const cityClass = isCityRuleExists ? 'table-success' : '';
                
                tr.innerHTML = `
                    <td><input type="checkbox" class="rule-checkbox" data-index="${index}"></td>
                    <td>${index + 1}</td>
                    <td class="${rowClass}">${rule.规则名称 || ''}</td>
                    <td>
                        <div class="d-flex align-items-center">
                            <span class="selected-rule-name me-2"></span>
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="showRuleSearch('${encodeURIComponent(rule.规则名称)}', ${index})"
                                    title="检索匹配规则">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                        <input type="hidden" class="selected-rule-id" value="">
                    </td>
                    <td>${rule.规则来源 || ''}</td>
                    <td>${rule.行为认定 || ''}</td>
                    <td>${rule.规则内涵 || ''}</td>
                    <td>${rule.适用范围 || ''}</td>
                    <td class="${cityClass}">${rule.城市 || ''}</td>
                    <td>
                        ${isRuleExists ? '<span class="badge bg-success">规则已存在</span>' : ''}
                        ${isCityRuleExists ? '<span class="badge bg-success">城市规则已存在</span>' : ''}
                    </td>
                    <td>
                        <button class="btn btn-sm btn-success" 
                                onclick="importSingleRule(${index})"
                                title="导入此规则">
                            <i class="bi bi-upload"></i> 导入
                        </button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
            
            // 显示预览模态框
            const previewModal = new bootstrap.Modal(document.getElementById('importPreviewModal'));
            previewModal.show();
        }

        // 检查规则是否已存在
        async function checkExistingRules(rules) {
            try {
                const response = await fetch('/api/rules/check-existing', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ruleNames: rules.map(r => r.规则名称),
                        cityRules: rules.map(r => ({
                            ruleName: r.规则名称,
                            city: r.城市
                        }))
                    })
                });
                
                if (!response.ok) {
                    throw new Error('检查规则存在状态失败');
                }
                
                return await response.json();
            } catch (error) {
                console.error('检查规则存在状态失败:', error);
                showToast('检查规则存在状态失败', 'error');
                return {
                    existingRules: [],
                    existingCityRules: []
                };
            }
        }

        // 全选/取消全选
        function selectAllRules() {
            // 只选择可见行的复选框
            const visibleCheckboxes = document.querySelectorAll('#previewTable tbody tr:not([style*="display: none"]) .rule-checkbox');
            visibleCheckboxes.forEach(checkbox => checkbox.checked = true);
            updateSelectedCount();
        }

        function deselectAllRules() {
            // 只取消选择可见行的复选框
            const visibleCheckboxes = document.querySelectorAll('#previewTable tbody tr:not([style*="display: none"]) .rule-checkbox');
            visibleCheckboxes.forEach(checkbox => checkbox.checked = false);
            updateSelectedCount();
        }

        // 更新选中计数
        function updateSelectedCount() {
            const selectedCount = document.getElementById('selectedCount');
            if (selectedCount) {
                // 只计算可见行中被选中的复选框
                const visibleCheckedBoxes = document.querySelectorAll('#previewTable tbody tr:not([style*="display: none"]) .rule-checkbox:checked').length;
                selectedCount.textContent = visibleCheckedBoxes;
            }
        }

        // 导入选中的规则
        function importSelectedRules() {
            const selectedIndexes = Array.from(document.querySelectorAll('.rule-checkbox:checked'))
                .map(checkbox => parseInt(checkbox.dataset.index));
            
            if (selectedIndexes.length === 0) {
                showToast('请至少选择一条规则', 'warning');
                return;
            }

            const selectedRules = selectedIndexes.map(index => previewData[index]);
            
            // 显示加载提示
            showLoading('正在导入规则...');

            // 发送导入请求
            fetch('/api/rules/import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ rules: selectedRules })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    // 显示成功消息
                    showToast(data.message, 'success');
                    
                    // 如果有跳过的规则，显示详细信息
                    if (data.details && data.details.skip_count > 0) {
                        const skippedRules = data.details.skipped_rules;
                        let skippedMessage = '<strong>以下规则已存在，已跳过导入：</strong><br>';
                        skippedRules.forEach(rule => {
                            skippedMessage += `<div class="mt-2">
                                <div>规则名称：${rule.规则名称}</div>
                                <div>规则内涵：${rule.规则内涵}</div>
                            </div>`;
                        });
                        
                        // 创建一个模态框显示跳过的规则
                        const skipModal = document.createElement('div');
                        skipModal.className = 'modal fade';
                        skipModal.innerHTML = `
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">导入结果详情</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        ${skippedMessage}
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
                                    </div>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(skipModal);
                        
                        // 显示跳过详情模态框
                        const bsSkipModal = new bootstrap.Modal(skipModal);
                        bsSkipModal.show();
                        
                        // 模态框关闭后删除元素
                        skipModal.addEventListener('hidden.bs.modal', function () {
                            document.body.removeChild(skipModal);
                        });
                    }
                    
                    // 刷新规则列表
                    table.ajax.reload();
                    
                    // 更新选中计数
                    updateSelectedCount();
                    
                    // 重新过滤规则状态
                    //filterPreviewRules();
                    
                } else {
                    showToast(data.error || '导入失败', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                showToast('导入失败: ' + error, 'error');
                console.error('导入失败:', error);
            });
        }

        // 监听复选框变化
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('rule-checkbox')) {
                updateSelectedCount();
            }
        });

        // 监听全选复选框
        document.getElementById('selectAll').addEventListener('change', function(e) {
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = e.target.checked);
            updateSelectedCount();
        });

        // 添加上传按钮旋转动画样式
        const style = document.createElement('style');
        style.textContent = `
            .spin {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        // 添加加载和提示函数
        function showLoading(message) {
            // 如果还没有loading元素，创建一个
            if (!document.getElementById('loadingIndicator')) {
                const loadingDiv = document.createElement('div');
                loadingDiv.id = 'loadingIndicator';
                loadingDiv.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(255, 255, 255, 0.9);
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    z-index: 9999;
                    text-align: center;
                `;
                document.body.appendChild(loadingDiv);
            }
            
            const loadingIndicator = document.getElementById('loadingIndicator');
            loadingIndicator.innerHTML = `
                <div class="spinner-border text-primary" role="status"></div>
                <div class="mt-2">${message}</div>
            `;
            loadingIndicator.style.display = 'block';
        }

        function hideLoading() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        }

        function showToast(message, type = 'info') {
            // 如果还没有toast容器，创建一个
            if (!document.getElementById('toastContainer')) {
                const container = document.createElement('div');
                container.id = 'toastContainer';
                container.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                `;
                document.body.appendChild(container);
            }
            
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show`;
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.getElementById('toastContainer').appendChild(toast);
            
            // 3秒后自动消失
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // 添加全选/取消全选功能
        function toggleAllMedicalItems() {
            const selectAllCheckbox = document.getElementById('selectAllMedical');
            const checkboxes = document.querySelectorAll('#medicalInsuranceTable .medical-select');
            
            checkboxes.forEach(checkbox => {
                const code = checkbox.getAttribute('data-code');
                const name = checkbox.getAttribute('data-name');
                
                if (code && name) {
                    if (selectAllCheckbox.checked) {
                        checkbox.checked = true;
                        selectedMedicalItems.set(code, name);
                    } else {
                        checkbox.checked = false;
                        selectedMedicalItems.delete(code);
                    }
                }
            });
            
            updateSelectedItemsDisplay();
        }


        // 添加地区对照行
        function addRegionalCodeRow(data = null) {
            const tbody = document.getElementById('regionalCodesBody');
            const row = document.createElement('tr');
            const rowNum = tbody.children.length + 1;
            
            row.innerHTML = `
                <td>
                    <input type="number" class="form-control form-control-sm" value="${data?.seq_no || rowNum}" />
                </td>
                <td>
                    <select class="form-control form-control-sm province-select" onchange="updateCities(this)">
                        <option value="">选择省份</option>
                        ${provinces.map(p => `
                            <option value="${p}" ${data?.province === p ? 'selected' : ''}>${p}</option>
                        `).join('')}
                    </select>
                </td>
                <td>
                    <select class="form-control form-control-sm city-select">
                        <option value="">选择城市</option>
                    </select>
                </td>
                <td>
                    <textarea class="form-control form-control-sm" rows="2">${data?.rule_content || ''}</textarea>
                </td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="text" class="form-control" value="${data?.medical_code1 || ''}" />
                        <button class="btn btn-outline-secondary" type="button" onclick="searchMedicalCode(this, 1)">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </td>
                <td><input type="text" class="form-control form-control-sm" value="${data?.medical_name1 || ''}" /></td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="text" class="form-control" value="${data?.medical_code2 || ''}" />
                        <button class="btn btn-outline-secondary" type="button" onclick="searchMedicalCode(this, 2)">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </td>
                <td><input type="text" class="form-control form-control-sm" value="${data?.medical_name2 || ''}" /></td>
                <td><input type="text" class="form-control form-control-sm" value="${data?.price_code || ''}" /></td>
                <td><input type="text" class="form-control form-control-sm" value="${data?.price_name || ''}" /></td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeRegionalCodeRow(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
            if (data?.province) {
                updateCities(row.querySelector('.province-select'), data.city);
            }
        }

        // 删除地区对照行
        function removeRegionalCodeRow(button) {
            button.closest('tr').remove();
        }

        // 保存地区对照
        async function saveRegionalCodes() {
            const ruleId = document.querySelector('input[name="id"]').value;
            const rows = Array.from(document.getElementById('regionalCodesBody').children);
            
            const mappings = rows.map(row => ({
                序号: row.querySelector('td:nth-child(1) input').value,
                省份: row.querySelector('.province-select').value,
                城市: row.querySelector('.city-select').value,
                规则内涵: row.querySelector('td:nth-child(4) textarea').value,
                医保编码1: row.querySelector('td:nth-child(5) input').value,
                医保名称1: row.querySelector('td:nth-child(6) input').value,
                医保编码2: row.querySelector('td:nth-child(7) input').value,
                医保名称2: row.querySelector('td:nth-child(8) input').value,
                物价编码: row.querySelector('td:nth-child(9) input').value,
                物价名称: row.querySelector('td:nth-child(10) input').value
            }));

            try {
                const response = await fetch(`/api/rule_medical_codes/${ruleId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ mappings })
                });

                if (!response.ok) throw new Error('保存失败');
                
                showToast('保存成功');
                bootstrap.Modal.getInstance(document.getElementById('regionalCodesModal')).hide();
            } catch (error) {
                showToast('保存失败: ' + error.message, 'error');
            }
        }

        // 显示省市对照模态框
        function showCityMappingModal() {
            // 获取当前规则表单数据
            const ruleForm = document.getElementById('ruleForm');
            const formData = new FormData(ruleForm);
            
            // 自动填充序号
            document.getElementById('mappingSeqNo').value = formData.get('序号') || '';
            
            // 清空其他字段
            document.getElementById('mappingRuleContent').value = formData.get('规则内涵') || '';
            document.getElementById('mappingMedicalCode1').value = '';
            document.getElementById('mappingMedicalName1').value = '';
            document.getElementById('mappingMedicalCode2').value = '';
            document.getElementById('mappingMedicalName2').value = '';
            document.getElementById('mappingPriceCode').value = '';
            document.getElementById('mappingPriceName').value = '';
            
            const modal = new bootstrap.Modal(document.getElementById('cityMappingModal'));
            modal.show();
        }

        // 更新城市选择框
        function updateCitySelect() {
            const provinceSelect = document.getElementById('provinceSelect');
            const citySelect = document.getElementById('citySelect');
            const province = provinceSelect.value;
            
            // 更新显示的省份
            document.getElementById('mappingProvince').textContent = province;
            
            // 更新城市选择框
            const cities = citiesByProvince[province] || [];
            citySelect.innerHTML = `
                <option value="">请选择城市</option>
                ${cities.map(city => `<option value="${city}">${city}</option>`).join('')}
            `;
        }

        // 当选择城市时更新显示
        document.getElementById('citySelect').addEventListener('change', function() {
            document.getElementById('mappingCity').textContent = this.value;
        });

        // 保存省市对照
        async function saveCityMapping() {
            const ruleId = document.querySelector('input[name="id"]').value;
            if (!ruleId) {
                showToast('请先保存规则基本信息', 'warning');
                return;
            }
            
            const mapping = {
                规则ID: ruleId,
                序号: document.getElementById('mappingSeqNo').value,
                省份: document.getElementById('provinceSelect').value,
                城市: document.getElementById('citySelect').value,
                规则内涵: document.getElementById('mappingRuleContent').value,
                医保编码1: document.getElementById('mappingMedicalCode1').value,
                医保名称1: document.getElementById('mappingMedicalName1').value,
                医保编码2: document.getElementById('mappingMedicalCode2').value,
                医保名称2: document.getElementById('mappingMedicalName2').value,
                物价编码: document.getElementById('mappingPriceCode').value,
                物价名称: document.getElementById('mappingPriceName').value
            };

            try {
                const response = await fetch('/api/rule_city_mapping', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(mapping)
                });

                if (!response.ok) throw new Error('保存失败');

                // 更新主表城市字段
                document.querySelector('input[name="城市"]').value = mapping.城市;
                
                showToast('保存成功');
                bootstrap.Modal.getInstance(document.getElementById('cityMappingModal')).hide();
            } catch (error) {
                showToast('保存失败: ' + error.message, 'error');
            }
        }

        // 定义省份和城市数据
        const provinces = [
            '北京', '天津', '河北', '山西', '内蒙古',
            '辽宁', '吉林', '黑龙江', '上海', '江苏',
            '浙江', '安徽', '福建', '江西', '山东',
            '河南', '湖北', '湖南', '广东', '广西',
            '海南', '重庆', '四川', '贵州', '云南',
            '西藏', '陕西', '甘肃', '青海', '宁夏',
            '新疆'
        ];

        const citiesByProvince = {
            '北京': ['北京'],
            '天津': ['天津'],
            '河北': ['石家庄', '唐山', '秦皇岛', '邯郸', '邢台', '保定', '张家口', '承德', '沧州', '廊坊', '衡水'],
            '山西': ['太原', '大同', '阳泉', '长治', '晋城', '朔州', '晋中', '运城', '忻州', '临汾', '吕梁'],
            '内蒙古': ['呼和浩特', '包头', '乌海', '赤峰', '通辽', '鄂尔多斯', '呼伦贝尔', '巴彦淖尔', '乌兰察布', '兴安盟', '锡林郭勒盟', '阿拉善盟'],
            '辽宁': ['沈阳', '大连', '鞍山', '抚顺', '本溪', '丹东', '锦州', '营口', '阜新', '辽阳', '盘锦', '铁岭', '朝阳', '葫芦岛'],
            '吉林': ['长春', '吉林', '四平', '辽源', '通化', '白山', '松原', '白城', '延边'],
            '黑龙江': ['哈尔滨', '齐齐哈尔', '鸡西', '鹤岗', '双鸭山', '大庆', '伊春', '佳木斯', '七台河', '牡丹江', '黑河', '绥化', '大兴安岭'],
            '上海': ['上海'],
            '江苏': ['南京', '无锡', '徐州', '常州', '苏州', '南通', '连云港', '淮安', '盐城', '扬州', '镇江', '泰州', '宿迁'],
            '浙江': ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
            '安徽': ['合肥', '芜湖', '蚌埠', '淮南', '马鞍山', '淮北', '铜陵', '安庆', '黄山', '滁州', '阜阳', '宿州', '六安', '亳州', '池州', '宣城'],
            '福建': ['福州', '厦门', '莆田', '三明', '泉州', '漳州', '南平', '龙岩', '宁德'],
            '江西': ['南昌', '景德镇', '萍乡', '九江', '新余', '鹰潭', '赣州', '吉安', '宜春', '抚州', '上饶'],
            '山东': ['济南', '青岛', '淄博', '枣庄', '东营', '烟台', '潍坊', '济宁', '泰安', '威海', '日照', '临沂', '德州', '聊城', '滨州', '菏泽'],
            '河南': ['郑州', '开封', '洛阳', '平顶山', '安阳', '鹤壁', '新乡', '焦作', '濮阳', '许昌', '漯河', '三门峡', '南阳', '商丘', '信阳', '周口', '驻马店', '济源'],
            '湖北': ['武汉', '黄石', '十堰', '宜昌', '襄阳', '鄂州', '荆门', '孝感', '荆州', '黄冈', '咸宁', '随州', '恩施'],
            '湖南': ['长沙', '株洲', '湘潭', '衡阳', '邵阳', '岳阳', '常德', '张家界', '益阳', '郴州', '永州', '怀化', '娄底', '湘西'],
            '广东': ['广州', '深圳', '珠海', '汕头', '佛山', '韶关', '湛江', '茂名', '肇庆', '江门', '河源', '阳江', '清远', '东莞', '中山', '潮州', '揭阳', '云浮'],
            '广西': ['南宁', '柳州', '桂林', '梧州', '北海', '防城港', '钦州', '贵港', '玉林', '百色', '贺州', '河池', '来宾', '崇左'],
            '海南': ['海口', '三亚', '三沙', '儋州', '文昌', '琼海', '万宁', '东方', '定安', '屯昌', '澄迈', '临高', '白沙', '昌江', '乐东', '陵水', '保亭', '琼中'],
            '重庆': ['重庆'],
            '四川': ['成都', '自贡', '攀枝花', '泸州', '德阳', '绵阳', '广元', '遂宁', '内江', '乐山', '南充', '眉山', '宜宾', '广安', '达州', '雅安', '巴中', '资阳', '阿坝', '甘孜', '凉山'],
            '贵州': ['贵阳', '六盘水', '遵义', '安顺', '毕节', '铜仁', '黔西南', '黔东南', '黔南'],
            '云南': ['昆明', '曲靖', '玉溪', '保山', '昭通', '丽江', '普洱', '临沧', '楚雄', '红河', '文山', '西双版纳', '大理', '德宏', '怒江', '迪庆'],
            '西藏': ['拉萨', '日喀则', '昌都', '林芝', '山南', '那曲', '阿里'],
            '陕西': ['西安', '宝鸡', '咸阳', '铜川', '渭南', '延安', '汉中', '榆林', '安康', '商洛'],
            '甘肃': ['兰州', '嘉峪关', '金昌', '白银', '天水', '武威', '张掖', '平凉', '酒泉', '庆阳', '定西', '陇南', '临夏', '甘南'],
            '青海': ['西宁', '海东', '海北', '黄南', '海南', '果洛', '玉树', '海西'],
            '宁夏': ['银川', '石嘴山', '吴忠', '固原', '中卫'],
            '新疆': ['乌鲁木齐', '克拉玛依', '吐鲁番', '哈密', '昌吉', '博尔塔拉', '巴音郭楞', '阿克苏', '克孜勒苏', '喀什', '和田', '伊犁', '塔城', '阿勒泰']
    };


        // 显示省市选择模态框
        function showProvinceModal() {
            // 初始化省份下拉框
            const provinceSelect = document.getElementById('provinceSelect');
            provinceSelect.innerHTML = '<option value="">请选择省份</option>';
            provinces.forEach(province => {
                provinceSelect.innerHTML += `<option value="${province}">${province}</option>`;
            });
            
            // 重置城市下拉框
            const modalCitySelect = document.getElementById('modalCitySelect');
            modalCitySelect.innerHTML = '<option value="">请先选择省份</option>';
            
            // 添加搜索框
            const modalBody = document.querySelector('#provinceModal .modal-body');
            if (!document.getElementById('citySearch')) {
                const searchDiv = document.createElement('div');
                searchDiv.className = 'mb-3';
                searchDiv.innerHTML = `
                    <label class="form-label">搜索城市</label>
                    <input type="text" class="form-control" id="citySearch" placeholder="输入城市名称搜索...">
                    <div id="searchResults" class="list-group mt-2" style="max-height: 200px; overflow-y: auto; display: none;"></div>
                `;
                modalBody.insertBefore(searchDiv, modalBody.firstChild);
            }
            
            // 添加搜索功能
            const searchInput = document.getElementById('citySearch');
            const searchResults = document.getElementById('searchResults');
            
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.trim().toLowerCase();
                if (searchTerm.length < 1) {
                    searchResults.style.display = 'none';
                    return;
                }
                
                // 搜索所有城市
                const matchedCities = [];
                for (const [province, cities] of Object.entries(citiesByProvince)) {
                    cities.forEach(city => {
                        if (city.toLowerCase().includes(searchTerm)) {
                            matchedCities.push({ province, city });
                        }
                    });
                }
                
                // 显示搜索结果
                if (matchedCities.length > 0) {
                    searchResults.innerHTML = matchedCities.map(match => `
                        <a href="#" class="list-group-item list-group-item-action" 
                           data-province="${match.province}" 
                           data-city="${match.city}">
                            ${match.city} (${match.province})
                        </a>
                    `).join('');
                    searchResults.style.display = 'block';
                    
                    // 添加点击事件
                    searchResults.querySelectorAll('a').forEach(item => {
                        item.addEventListener('click', function(e) {
                            e.preventDefault();
                            const selectedProvince = this.dataset.province;
                            const selectedCity = this.dataset.city;
                            
                            // 设置省份
                            provinceSelect.value = selectedProvince;
                            updateModalCities();
                            
                            // 设置城市
                            modalCitySelect.value = selectedCity;
                            
                            // 清空并隐藏搜索结果
                            searchInput.value = '';
                            searchResults.style.display = 'none';
                        });
                    });
                } else {
                    searchResults.innerHTML = '<div class="list-group-item">未找到匹配的城市</div>';
                    searchResults.style.display = 'block';
                }
            });
            
            // 点击外部时隐藏搜索结果
            document.addEventListener('click', function(e) {
                if (!searchResults.contains(e.target) && e.target !== searchInput) {
                    searchResults.style.display = 'none';
                }
            });
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('provinceModal'));
            modal.show();
        }

        // 更新模态框中的城市选择框
        function updateModalCities() {
            const provinceSelect = document.getElementById('provinceSelect');
            const modalCitySelect = document.getElementById('modalCitySelect');
            const selectedProvince = provinceSelect.value;
            
            modalCitySelect.innerHTML = '<option value="">请选择城市</option>';
            
            if (selectedProvince && citiesByProvince[selectedProvince]) {
                citiesByProvince[selectedProvince].forEach(city => {
                    modalCitySelect.innerHTML += `<option value="${city}">${city}</option>`;
                });
            }
        }

        // 获取新的序列号
        async function getNextSequence() {
            try {
                // 使用规则城市对照表的序列名
                const response = await fetch('/api/sequence/规则医保编码对照_SEQ');
                const data = await response.json();
                console.log('序列响应数据:', data);
                if (data.sequence) {
                    return data.sequence;
                }
                throw new Error('获取序列号失败');
            } catch (error) {
                console.error('获取序列号失败:', error);
                showToast('获取序列号失败: ' + error.message, 'error');
                return null;
            }
        }

        // 修改 createCityRule 函数，在创建规则前获取序列号
        async function createCityRule() {
            try {
                const sequence = await getNextSequence();
                if (!sequence) {
                    console.error('获取序列号失败');
                    return;
                }
                
                const citySelect = document.getElementById('citySelect');
                const city = citySelect.value;
                const ruleId = document.querySelector('input[name="id"]').value;
                const province = document.getElementById('provinceSelect').value;
                
                console.log('序列号:', sequence);
                console.log('城市:', city);
                console.log('省份:', province);
                console.log('规则ID:', ruleId);
                
                if (!city || !ruleId || !province) {
                    console.warn('缺少必要参数');
                    showToast('缺少必要参数', 'warning');
                    return;
                }
                
                // 更新规则数据
                const formData = {
                    规则id: ruleId,
                    对照id: sequence,
                    城市: city,
                    省份: province
                };
                
                // 保存数据到规则城市对照表
                fetch(`/api/rule_city_mapping`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('添加城市成功', 'success');
                        // 重新加载规则信息
                        loadRulesByCity();
                    } else {
                        showToast(data.error || '添加城市失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('添加城市失败:', error);
                    showToast('添加城市失败: ' + error.message, 'error');
                });
                
            } catch (error) {
                console.error('添加城市规则失败:', error);
                showToast('添加失败: ' + error.message, 'error');
            }
        }

        // 修改 confirmProvinceCity 函数，在添加城市后调用新方法
        function confirmProvinceCity() {
            const province = document.getElementById('provinceSelect').value;
            const city = document.getElementById('modalCitySelect').value;
            const ruleId = document.querySelector('input[name="id"]').value; // 获取规则ID
            
            if (!province || !city) {
                showToast('请选择省份和城市', 'warning');
                return;
            }
            
            try {
                // 更新主表单中的城市选择
                const citySelect = document.getElementById('citySelect');
                const option = document.createElement('option');
                option.value = city;
                option.textContent = city;
                
                // 检查是否已存在该选项
                const existingOption = Array.from(citySelect.options).find(opt => opt.value === city);
                if (!existingOption) {
                    citySelect.appendChild(option);
                }
                citySelect.value = city;
                
                // 设置规则ID到隐藏输入
                document.getElementById('ruleIdInput').value = ruleId;
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('provinceModal'));
                modal.hide();
                
                // 替换 loadRulesByCity() 为新方法
                createCityRule();
                clearFormFields();
                showToast('已添加新城市', 'success');
            } catch (error) {
                console.error('添加城市失败:', error);
                showToast('添加城市失败: ' + error.message, 'error');
            }
        }

        // 清空表单相关字段
        function clearFormFields() {
            // 清空医保相关字段
            document.querySelector('input[name="医保编码1"]').value = '';
            document.querySelector('input[name="医保名称1"]').value = '';
            document.querySelector('input[name="医保编码2"]').value = '';
            document.querySelector('input[name="医保名称2"]').value = '';
            document.querySelector('input[name="规则内涵"]').value = '';
        }

        // 更新城市下拉框选项
        async function updateCityDropdown() {
            try {
                const response = await fetch(`/api/cities`);
                const cities = await response.json();
                
                const citySelect = document.getElementById('citySelect');
                citySelect.innerHTML = `
                    ${cities.map(city => `<option value="${city}">${city}</option>`).join('')}
                `;
            } catch (error) {
                console.error('加载城市列表失败:', error);
                showToast('加载城市列表失败', 'error');
            }
                }

        // 加载已有规则
        async function loadExistingRule() {
            const city = document.getElementById('citySelect').value;
            if (!city) return;
            
            try {
                const response = await fetch(`/api/rule_by_city/${encodeURIComponent(city)}`);
                if (!response.ok) throw new Error('加载规则失败');
                
                const rule = await response.json();
                if (rule) {
                    // 填充表单字段
                    const form = document.getElementById('ruleForm');
                    Object.keys(rule).forEach(key => {
                        const input = form.querySelector(`[name="${key}"]`);
                        if (input) {
                            input.value = rule[key];
                        }
                    });
                }
            } catch (error) {
                console.error('加载规则失败:', error);
                showToast('加载规则失败', 'error');
            }
        }


        // 搜索医保编码
        function searchMedicalCodes() {
            const searchParams = {
                code: $('#searchMedicalCode').val(),
                name: $('#searchMedicalName').val(),
                city: $('#citySelect').val(),
                rule_id: editingId
            };

            console.log('Sending request with params:', searchParams);

            fetch('/api/medical-insurance?' + new URLSearchParams(searchParams))
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(response => {
                    console.log('Response data:', response);
                    
                    // 获取表格体
                    const tbody = document.querySelector('#medicalInsuranceTable tbody');
                    if (!tbody) {
                        console.error('Table body not found');
                        return;
                    }
                    
                    // 清空现有内容
                    tbody.innerHTML = '';
                    
                    // 检查数据
                    if (!response.data || response.data.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="8" class="text-center">未找到匹配的记录</td></tr>';
                        return;
                    }
                    
                    // 构建所有行的HTML
                    const rowsHtml = response.data.map(item => `
                        <tr>
                            <td>
                                <input type="checkbox" class="medical-item-checkbox" 
                                       ${selectedMedicalItems.has(item.code) ? 'checked' : ''}
                                       data-code="${item.code || ''}"
                                       data-name="${item.name || ''}"
                                       onchange="toggleMedicalItem(this)">
                            </td>
                            <td>${item.code || ''}</td>
                            <td>${item.name || ''}</td>
                            <td>${item.country_code || ''}</td>
                            <td>${item.price_code || ''}</td>
                            <td>${item.fee_type || ''}</td>
                            <td>${item.unit_price || ''}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="selectSingleItem('${item.code || ''}', '${item.name || ''}')">
                                    选择
                                </button>
                            </td>
                        </tr>
                    `).join('');
                    
                    // 一次性更新DOM
                    tbody.innerHTML = rowsHtml;

                    // 如果有提示消息，显示它
                    if (response.message) {
                        showToast(response.message, 'info');
                    }
                })
                .catch(error => {
                    console.error('搜索失败:', error);
                    showToast('搜索失败: ' + error.message, 'error');
                });
        }

        // 在搜索框输入时自动搜索
        $('#searchMedicalCode, #searchMedicalName').on('input', function() {
            if (this.value.length >= 2) {  // 当输入至少2个字符时触发搜索
                searchMedicalCodes();
            }
        });

        // 添加回车键搜索支持
        $('#searchMedicalCode, #searchMedicalName').on('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchMedicalCodes();
            }
        });

        function loadRulesByCity() {
                        // 收集表单数据
            const formData = {};
            $('#ruleForm').serializeArray().forEach(item => {
                if (['发布时间', '生效日期', '失效日期'].includes(item.name)) {
                    formData[item.name] = item.value || null;  // 如果为空字符串则设为 null
                } else {
                    formData[item.name] = item.value;
                }
            });
            console.log('Form data:', formData);
            const ruleId = formData.id;
            const selectedCity = $('#citySelect').val();
            console.log('Selected city:', selectedCity);
            console.log('Rule ID:', ruleId);

            $.ajax({
                url: `/api/rules/city/${encodeURIComponent(selectedCity)}/${ruleId}`,
                method: 'GET',
                success: function(response) {
                    if (response.success && response.rule) {
                        // 填充表单字段
                        const rule = response.rule;
                        Object.keys(rule).forEach(key => {
                            const input = $(`input[name="${key}"], textarea[name="${key}"], select[name="${key}"]`);
                            if (input.length) {
                                if (input.is('select')) {
                                    // 对于select元素，先设置值
                                    input.val(rule[key] || '');
                                    // 如果值没有成功设置（可能是因为选项还未加载），则创建新选项
                                    if (input.val() !== rule[key] && rule[key]) {
                                        input.append(new Option(rule[key], rule[key], true, true));
                                        input.val(rule[key]);
                                    }
                                    console.log(`设置${key}的值为:`, rule[key], '当前select的值为:', input.val());
                                } else {
                                    input.val(rule[key] || '');
                                }
                            }
                        });
                        
                        console.log('Rule loaded:', rule);
                    } else {
                        showToast('未找到规则信息', 'warning');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading rule:', error);
                    showToast('加载规则失败', 'error');
                }
            });
        }

        // 添加一个隐藏的 input 来存储当前规则的 ID
        $('#citySelect').on('change', function() {
            const ruleId = $('#ruleIdInput').val(); // 从隐藏的 input 获取规则 ID
            if (ruleId) {
                loadRulesByCity(ruleId);
            }
        });

        // 在加载规则信息时设置规则 ID
        function loadRuleInfo(rule) {
            $('#ruleIdInput').val(rule.id); // 设置规则 ID
            // ... 其他规则信息加载逻辑 ...
        }

        function filterPreviewRules() {
            console.log('开始执行过滤规则...');
            
            const showExistingRules = document.getElementById('showExistingRules')?.checked ?? false;
            const showExistingCityRules = document.getElementById('showExistingCityRules')?.checked ?? false;
            const showUnCreatedRules = document.getElementById('showUnCreatedRules')?.checked ?? false;
            
            console.log('过滤开关状态:', {
                showExistingRules,
                showExistingCityRules,
                showUnCreatedRules
            });
            
            const tbody = document.querySelector('#previewTable tbody');
            if (!tbody) {
                console.error('找不到表格体元素');
                return;
            }
            
            const rows = tbody.querySelectorAll('tr');
            console.log(`找到 ${rows.length} 行数据`);
            
            rows.forEach((row, index) => {
                const cells = row.getElementsByTagName('td');
                
                if (!cells || cells.length === 0) {
                    console.log(`第 ${index + 1} 行没有单元格，跳过`);
                    return;
                }
                
                const ruleNameCell = cells[2]; // 规则名称列
                const cityCell = cells[8];     // 城市列
                
                if (!ruleNameCell || !cityCell) {
                    console.log(`第 ${index + 1} 行缺少必要单元格，跳过`);
                    return;
                }
                
                const isExistingRule = ruleNameCell.classList.contains('table-success');
                const isExistingCityRule = cityCell.classList.contains('table-success');
                const isUnCreatedRule = !isExistingRule;
                
                let shouldShow = false;
                
                // 修改显示逻辑
                if (!showExistingRules && !showUnCreatedRules && !showExistingCityRules) {
                    // 所有开关都关闭时显示所有规则
                    shouldShow = true;
                } else {
                    if (showExistingRules && showUnCreatedRules) {
                        // 同时显示已有规则和未创建规则（即显示所有规则）
                        shouldShow = true;
                    } else if (showExistingRules && showExistingCityRules) {
                        // 显示已有规则且已有城市规则
                        shouldShow = isExistingRule && !isExistingCityRule;
                    } else if (showExistingRules) {
                        // 只显示已有的规则
                        shouldShow = isExistingRule;
                    } else if (showUnCreatedRules) {
                        // 只显示未创建规则
                        shouldShow = isUnCreatedRule;
                    } else if (showExistingCityRules) {
                        // 只显示城市规则不存在的
                        shouldShow = !isExistingCityRule;
                    }
                }
                
                console.log(`第 ${index + 1} 行状态:`, {
                    规则名称: ruleNameCell.textContent,
                    城市: cityCell.textContent,
                    isExistingRule,
                    isExistingCityRule,
                    isUnCreatedRule,
                    shouldShow
                });
                
                row.style.display = shouldShow ? '' : 'none';
            });
            
            updateSelectedCount();
            console.log('过滤规则执行完成');
        }

        // 更新选中计数
        function updateSelectedCount() {
            const selectedCount = document.getElementById('selectedCount');
            if (selectedCount) {
                // 只计算可见行中被选中的复选框
                const visibleCheckedBoxes = document.querySelectorAll('#previewTable tbody tr:not([style*="display: none"]) .rule-checkbox:checked').length;
                selectedCount.textContent = visibleCheckedBoxes;
            }
        }

        // 确保在 DOM 加载完成后初始化事件监听
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM 加载完成，初始化事件监听...');
            
            const showExistingRules = document.getElementById('showExistingRules');
            const showExistingCityRules = document.getElementById('showExistingCityRules');
            
            if (showExistingRules && showExistingCityRules) {
                console.log('找到过滤开关元素，添加事件监听器');
                showExistingRules.addEventListener('change', () => {
                    console.log('规则名称开关状态改变:', showExistingRules.checked);
                    filterPreviewRules();
                });
                
                showExistingCityRules.addEventListener('change', () => {
                    console.log('城市规则开关状态改变:', showExistingCityRules.checked);
                    filterPreviewRules();
                });
            } else {
                console.error('找不到过滤开关元素');
            }
        });

        function formatExistingRules(ruleName) {
            console.log('开始比较规则:', ruleName);
            if (!ruleName) return '';
            
            // 使用同步 XMLHttpRequest
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/api/rules/similar', false);  // false 表示同步请求
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            try {
                xhr.send(JSON.stringify({ rule_name: ruleName }));
                
                if (xhr.status === 200) {
                    const data = JSON.parse(xhr.responseText);
                    console.log('获取到相似规则:', data);
                    
                    if (!data.success || !data.matches) return '';
                    
                    // 如果有多个匹配规则（相似度>=80%）
                    if (data.matches.length > 1) {
                        return `
                            <select class="form-select form-select-sm">
                                <option value="">请选择匹配规则</option>
                                ${data.matches.map(match => 
                                    `<option value="${match.name}">${match.name} (${match.similarity}%)</option>`
                                ).join('')}
                            </select>
                        `;
                    }
                    
                    // 如果只有一个完全匹配的规则
                    if (data.matches.length === 1 && data.matches[0].similarity === 100) {
                        return data.matches[0].name;
                    }
                }
                
                return '';
                
            } catch (error) {
                console.error('获取相似规则失败:', error);
                return '';
            }
        }

        // 修改 displayRulePreview 函数以支持异步操作
        async function displayRulePreview(rules) {
            console.log('开始显示规则预览，规则数据:', rules);
            const tbody = document.querySelector('#previewTableBody');
            tbody.innerHTML = '';
            
            for (const [index, rule] of rules.entries()) {
                console.log(`处理第 ${index + 1} 条规则:`, rule);
                
                const tr = document.createElement('tr');
                const existingRules = await formatExistingRules(rule.规则名称);
                
                tr.innerHTML = `
                    <td>
                        <input type="checkbox" class="rule-checkbox" data-index="${index}">
                    </td>
                    <td>${rule.序号 || ''}</td>
                    <td>${rule.规则名称 || ''}</td>
                    <td>${existingRules}</td>
                    <td>${rule.规则来源 || ''}</td>
                    <td>${rule.行为认定 || ''}</td>
                    <td>${rule.规则内涵 || ''}</td>
                    <td>${rule.适用范围 || ''}</td>
                    <td>${rule.城市 || ''}</td>
                `;
                
                tbody.appendChild(tr);
            }
        }

        // 显示规则搜索模态框
        async function showRuleSearch(ruleName, rowIndex) {
            const searchModal = new bootstrap.Modal(document.getElementById('ruleSearchModal'));
            const searchInput = document.getElementById('ruleSearchInput');
            const searchTable = document.getElementById('ruleSearchTable').getElementsByTagName('tbody')[0];
            
            // 清空搜索框和结果，并设置初始搜索词
            searchInput.value = decodeURIComponent(ruleName) || '';
            searchTable.innerHTML = '';
            
            // 保存当前行索引，供选择时使用
            document.getElementById('ruleSearchModal').dataset.rowIndex = rowIndex;
            
            // 执行初始搜索
            await searchImportRules(searchInput.value);
            
            // 显示模态框
            searchModal.show();
        }

        // 搜索规则
        async function searchImportRules(keyword) {
            try {
                const response = await fetch('/api/rules/search-import-rule', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ keyword })
                });
                
                const data = await response.json();
                const searchTable = document.getElementById('ruleSearchTable').getElementsByTagName('tbody')[0];
                searchTable.innerHTML = '';
                
                if (data.rules && data.rules.length > 0) {
                    data.rules.forEach(rule => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `
                            <td>${rule.规则名称 || ''}</td>
                            <td>${rule.规则来源 || ''}</td>
                            <td>${rule.规则内涵 || ''}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" 
                                        onclick="selectMatchRule('${rule.id}', '${encodeURIComponent(rule.规则名称)}')">
                                    选择
                                </button>
                            </td>
                        `;
                        searchTable.appendChild(tr);
                    });
                } else {
                    searchTable.innerHTML = `
                        <tr>
                            <td colspan="4" class="text-center">未找到匹配的规则</td>
                        </tr>
                    `;
                }
            } catch (error) {
                console.error('搜索规则失败:', error);
                showToast('搜索规则失败', 'error');
            }
        }

        // 选择匹配规则
        function selectMatchRule(ruleId, ruleName) {
            const rowIndex = document.getElementById('ruleSearchModal').dataset.rowIndex;
            const matchCell = document.querySelector(`#previewTable tbody tr:nth-child(${parseInt(rowIndex) + 1}) td:nth-child(4)`);
            
            // 更新选中的规则ID和名称
            matchCell.querySelector('.selected-rule-id').value = ruleId;
            matchCell.querySelector('.selected-rule-name').innerHTML = `
                <span class="bg-info text-dark">
                    ${decodeURIComponent(ruleName)}
                    <button type="button" class="btn-close btn-close-white ms-2" 
                            onclick="clearMatchRule(${rowIndex})" 
                            style="font-size: 0.5em;">
                    </button>
                </span>
            `;
            
            // 关闭搜索模态框
            const searchModal = bootstrap.Modal.getInstance(document.getElementById('ruleSearchModal'));
            searchModal.hide();
        }

        // 清除匹配规则
        function clearMatchRule(rowIndex) {
            const matchCell = document.querySelector(`#previewTable tbody tr:nth-child(${parseInt(rowIndex) + 1}) td:nth-child(4)`);
            matchCell.querySelector('.selected-rule-id').value = '';
            matchCell.querySelector('.selected-rule-name').innerHTML = '';
        }

        // 添加搜索框实时搜索功能
        document.getElementById('ruleSearchInput')?.addEventListener('input', debounce(async (e) => {
            await searchImportRules(e.target.value);
        }, 300));

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 模板选择和SQL生成相关函数
        function generateTemplateSQL() {
            // 加载系统模板列表
            fetch('/api/sql_templates')
                .then(response => response.json())
                .then(data => {
                    const select = document.getElementById('systemTemplateSelect');
                    select.innerHTML = '<option value="">请选择模板</option>';
                    data.templates.forEach(template => {
                        select.innerHTML += `<option value="${template.id}">${template.name}</option>`;
                    });
                })
                .catch(error => {
                    console.error('加载模板列表失败:', error);
                    showToast('加载模板列表失败', 'error');
                });
            
            // 显示模板选择模态框
            const modal = new bootstrap.Modal(document.getElementById('templateModal'));
            modal.show();
        }

        // 切换模板来源
        document.querySelectorAll('input[name="templateSource"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                document.getElementById('systemTemplateList').style.display = 
                    e.target.value === 'system' ? 'block' : 'none';
                document.getElementById('localTemplateList').style.display = 
                    e.target.value === 'local' ? 'block' : 'none';
                document.getElementById('templatePreview').textContent = '';
            });
        });

        // 预览系统模板
        document.getElementById('systemTemplateSelect').addEventListener('change', (e) => {
            if (e.target.value) {
                fetch(`/api/sql_templates/${e.target.value}`)
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('templatePreview').textContent = data.content;
                    })
                    .catch(error => {
                        console.error('加载模板预览失败:', error);
                        showToast('加载模板预览失败', 'error');
                    });
            }
        });

        // 预览本地模板
        document.getElementById('localTemplateFile').addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    document.getElementById('templatePreview').textContent = e.target.result;
                };
                reader.readAsText(file);
            }
        });

        // 生成SQL
        async function generateSQL() {
            try {
                const source = document.querySelector('input[name="templateSource"]:checked').value;
                let templateContent;
                
                if (source === 'system') {
                    const templateId = document.getElementById('systemTemplateSelect').value;
                    if (!templateId) {
                        showToast('请选择模板', 'warning');
                        return;
                    }
                    const response = await fetch(`/api/sql_templates/${templateId}`);
                    const data = await response.json();
                    templateContent = data.content;
                } else {
                    const file = document.getElementById('localTemplateFile').files[0];
                    if (!file) {
                        showToast('请选择模板文件', 'warning');
                        return;
                    }
                    templateContent = await new Promise((resolve) => {
                        const reader = new FileReader();
                        reader.onload = (e) => resolve(e.target.result);
                        reader.readAsText(file);
                    });
                }
                
                // 获取当前规则ID
                const ruleId = document.querySelector('input[name="id"]').value;
                
                // 调用后端生成SQL
                const response = await fetch('/api/generate_rule_sql', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        rule_ids: [ruleId],
                        template_content: templateContent
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    // 关闭模板选择模态框
                    bootstrap.Modal.getInstance(document.getElementById('templateModal')).hide();
                    // 显示生成的SQL
                    showSQLPreview(result.sql);
                } else {
                    showToast(result.error || '生成SQL失败', 'error');
                }
            } catch (error) {
                console.error('生成SQL失败:', error);
                showToast('生成SQL失败', 'error');
            }
        }

        function importSingleRule(index) {
            try {
                // 获取预览数据中的单条规则
                const rule = previewData[index];
                if (!rule) {
                    showToast('未找到规则数据', 'error');
                    return;
                }

                console.log('准备导入单条规则:', rule);
                
                // 显示加载提示
                showLoading('正在导入规则...');

                // 发送导入请求
                fetch('/api/rules/import', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        rules: [rule]  // 包装成数组，复用批量导入的接口
                    })
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        // 显示成功消息
                        showToast(data.message || '导入成功', 'success');
                        
                        // 如果规则已存在，显示详细信息
                        if (data.details && data.details.skip_count > 0) {
                            const skippedRule = data.details.skipped_rules[0];
                            showToast(`规则"${skippedRule.规则名称}"已存在，已跳过导入`, 'warning');
                        }
                        
                        // 刷新规则列表
                        table.ajax.reload();
                        
                        // 标记该行为已导入状态
                        const tr = document.querySelector(`#previewTable tbody tr:nth-child(${index + 1})`);
                        if (tr) {
                            tr.classList.add('table-success');
                            const statusCell = tr.querySelector('td:nth-last-child(2)');
                            if (statusCell) {
                                statusCell.innerHTML = '<span class="badge bg-success">已导入</span>';
                            }
                            // 禁用导入按钮
                            const importBtn = tr.querySelector('button');
                            if (importBtn) {
                                importBtn.disabled = true;
                                importBtn.title = '已导入';
                            }
                        }
                        
                    } else {
                        showToast(data.error || '导入失败', 'error');
                    }
                })
                .catch(error => {
                    hideLoading();
                    console.error('导入失败:', error);
                    showToast('导入失败: ' + error.message, 'error');
                });
                
            } catch (error) {
                hideLoading();
                console.error('导入规则时出错:', error);
                showToast('导入规则失败: ' + error.message, 'error');
            }
        }

        function selectRule(ruleId, ruleName, index) {
            try {
                // 找到对应行的元素
                const row = document.querySelector(`#previewTable tbody tr:nth-child(${index + 1})`);
                if (!row) {
                    console.error('未找到对应的行:', index);
                    return;
                }

                // 更新规则名称显示
                const nameSpan = row.querySelector('.selected-rule-name');
                if (nameSpan) {
                    nameSpan.textContent = ruleName;
                    // 使用和规则来源相同的样式
                    nameSpan.classList.add('text-success'); // 绿色文本
                }

                // 更新隐藏的规则ID
                const idInput = row.querySelector('.selected-rule-id');
                if (idInput) {
                    idInput.value = ruleId;
                }

                // 关闭规则搜索模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('ruleSearchModal'));
                if (modal) {
                    modal.hide();
                }
            } catch (error) {
                console.error('选择规则时出错:', error);
            }
        }

        let mergeRuleModal;
        let currentSourceRuleId = null;

        // 初始化合并规则模态框
        document.addEventListener('DOMContentLoaded', function() {
            mergeRuleModal = new bootstrap.Modal(document.getElementById('mergeRuleModal'));
        });

        // 打开合并规则模态框
        function mergeRule(id) {
            if (!id || isNaN(id)) {
                console.error('Invalid rule ID:', id);
                showToast('无效的规则ID', 'error');
                return;
            }
            
            // 保存源规则ID
            document.getElementById('ruleIdInput').value = id;
            
            // 显示规则搜索模态框
            const modal = new bootstrap.Modal(document.getElementById('ruleSearchModal'));
            modal.show();
            
            // 清空搜索输入框和结果
            document.getElementById('ruleSearchInput').value = '';
            document.querySelector('#ruleSearchTable tbody').innerHTML = '';
            
            // 加载初始规则列表
            searchRulesForMerge('');
        }

        function searchRulesForMerge(keyword) {
            const sourceRuleId = document.getElementById('ruleIdInput').value;
            
            // 显示加载指示器
            const tbody = document.querySelector('#ruleSearchTable tbody');
            tbody.innerHTML = '<tr><td colspan="4" class="text-center">正在搜索...</td></tr>';
            
            fetch(`/api/rules/search?keyword=${encodeURIComponent(keyword)}&exclude=${sourceRuleId}`)
                .then(response => response.json())
                .then(response => {
                    // 清空表格内容
                    tbody.innerHTML = '';
                    
                    // 检查数据格式
                    const data = Array.isArray(response) ? response : 
                                 (response.data ? response.data : []);
                    
                    console.log('搜索结果数据:', data); // 添加日志
                    
                    if (data.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="4" class="text-center">未找到匹配的规则</td></tr>';
                        return;
                    }
                    
                    // 构建所有行的HTML
                    let rowsHtml = '';
                    data.forEach(rule => {
                        // 确保rule是一个有效的对象
                        if (!rule || typeof rule !== 'object') return;
                        
                        rowsHtml += `
                            <tr>
                                <td>${rule.规则名称 || ''}</td>
                                <td>${rule.规则来源 || ''}</td>
                                <td>${rule.规则内涵 || ''}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="confirmMergeRule('${sourceRuleId}', '${rule.ID || rule.id}')">
                                        选择
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    // 一次性更新DOM
                    tbody.innerHTML = rowsHtml;
                    
                    // 防止其他事件清空表格
                    setTimeout(() => {
                        if (tbody.innerHTML === '') {
                            console.warn('表格内容被清空，正在恢复...');
                            tbody.innerHTML = rowsHtml;
                        }
                    }, 100);
                })
                .catch(error => {
                    console.error('搜索规则失败:', error);
                    tbody.innerHTML = '<tr><td colspan="4" class="text-center text-danger">搜索规则失败，请重试</td></tr>';
                    showToast('搜索规则失败', 'error');
                });
        }

        // 修改确认合并函数，确保ID参数正确处理
        function confirmMergeRule(sourceRuleId, targetRuleId) {
            console.log('合并规则:', sourceRuleId, targetRuleId); // 添加日志
            
            if (confirm('确定要将规则合并到目标规则吗？此操作不可撤销。')) {
                // 显示加载提示
                showLoading('正在合并规则...');
                
                fetch('/api/rules/merge', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        source_rule_id: sourceRuleId,
                        target_rule_id: targetRuleId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    
                    if (data.success) {
                        showToast('规则合并成功', 'success');
                        
                        // 关闭模态框
                        const modal = bootstrap.Modal.getInstance(document.getElementById('ruleSearchModal'));
                        if (modal) {
                            modal.hide();
                        }
                        
                        // 刷新表格
                        if (typeof table !== 'undefined' && table.ajax) {
                            table.ajax.reload();
                        } else {
                            console.warn('表格对象不存在或没有ajax方法');
                            location.reload(); // 如果表格对象不可用，则刷新页面
                        }
                    } else {
                        showToast(data.error || '规则合并失败', 'error');
                    }
                })
                .catch(error => {
                    hideLoading();
                    console.error('合并规则失败:', error);
                    showToast('合并规则失败', 'error');
                });
            }
        }

        // 确保模态框显示时初始化搜索
        document.addEventListener('DOMContentLoaded', function() {
            const ruleSearchInput = document.getElementById('ruleSearchInput');
            if (ruleSearchInput) {
                let searchTimeout;
                ruleSearchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        searchRulesForMerge(this.value);
                    }, 300);
                });
                
                // 防止回车键提交表单
                ruleSearchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        searchRulesForMerge(this.value);
                    }
                });
            }
            
            // 初始化模态框事件
            const ruleSearchModal = document.getElementById('ruleSearchModal');
            if (ruleSearchModal) {
                ruleSearchModal.addEventListener('shown.bs.modal', function() {
                    // 清空搜索框
                    if (ruleSearchInput) ruleSearchInput.value = '';
                    // 执行初始搜索
                    searchRulesForMerge('');
                    // 聚焦搜索框
                    setTimeout(() => ruleSearchInput.focus(), 300);
                });
            }
        });

        // Add loading and hiding functions if they don't exist
        function showLoading(message = '加载中...') {
            // Create loading overlay if it doesn't exist
            if (!document.getElementById('loadingOverlay')) {
                const overlay = document.createElement('div');
                overlay.id = 'loadingOverlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                `;
                
                const spinner = document.createElement('div');
                spinner.className = 'spinner-border text-light';
                spinner.setAttribute('role', 'status');
                
                const messageEl = document.createElement('span');
                messageEl.id = 'loadingMessage';
                messageEl.className = 'text-light ms-3';
                messageEl.textContent = message;
                
                const container = document.createElement('div');
                container.className = 'd-flex align-items-center';
                container.appendChild(spinner);
                container.appendChild(messageEl);
                
                overlay.appendChild(container);
                document.body.appendChild(overlay);
            } else {
                document.getElementById('loadingMessage').textContent = message;
                document.getElementById('loadingOverlay').style.display = 'flex';
            }
        }

        function hideLoading() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        // 当选择目标规则时显示其内容
        document.getElementById('targetRuleSelect').addEventListener('change', function() {
            const targetRuleId = this.value;
            if (targetRuleId) {
                fetch(`/api/rules/${targetRuleId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            document.getElementById('targetRuleContent').textContent = data.rule.rule_content;
                        }
                    })
                    .catch(error => {
                        console.error('加载目标规则失败:', error);
                        showMessage('加载目标规则失败：' + error.message, 'error');
                    });
            } else {
                document.getElementById('targetRuleContent').textContent = '';
            }
        });

        // 确认合并规则
        function confirmMergeRules() {
            const targetRuleId = document.getElementById('targetRuleSelect').value;
            if (!targetRuleId) {
                showMessage('请选择目标规则', 'warning');
                return;
            }
            
            if (!confirm('确定要将所选规则合并吗？此操作不可撤销。')) {
                return;
            }
            
            fetch('/api/rules/merge', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    source_rule_id: currentSourceRuleId,
                    target_rule_id: targetRuleId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('规则合并成功', 'success');
                    mergeRuleModal.hide();
                    refreshList();
                } else {
                    throw new Error(data.error || '合并失败');
                }
            })
            .catch(error => {
                console.error('合并规则失败:', error);
                showMessage('合并规则失败：' + error.message, 'error');
            });
        }

        // 添加批量合并规则功能
        function batchMergeRules() {
            // 获取所有选中的行
            const selectedRows = [];
            $('#rulesTable tbody tr.selected').each(function() {
                const rowData = table.row(this).data();
                if (rowData) {
                    selectedRows.push(rowData);
                }
            });
            
            // 也检查复选框选中的行
            $('#rulesTable tbody tr').each(function() {
                const checkbox = $(this).find('td:first-child input[type="checkbox"]');
                if (checkbox.prop('checked')) {
                    const rowData = table.row(this).data();
                    if (rowData && !selectedRows.includes(rowData)) {
                        selectedRows.push(rowData);
                    }
                }
            });
            
            if (selectedRows.length < 2) {
                showToast('请至少选择两条规则进行合并', 'warning');
                return;
            }
            
            // 收集所有选中的规则ID
            const selectedRuleIds = [];
            for (let i = 0; i < selectedRows.length; i++) {
                selectedRuleIds.push(selectedRows[i].ID || selectedRows[i].id);
            }
            
            // 打开目标规则选择模态框
            openTargetRuleSelectionModal(selectedRuleIds);
        }

        // 打开目标规则选择模态框
        function openTargetRuleSelectionModal(sourceRuleIds) {
            // 保存源规则ID列表
            document.getElementById('sourceRuleIdsInput').value = JSON.stringify(sourceRuleIds);
            
            // 填充目标规则选择下拉框
            const targetRuleSelect = document.getElementById('targetRuleSelect');
            targetRuleSelect.innerHTML = '<option value="">请选择目标规则</option>';
            
            sourceRuleIds.forEach(ruleId => {
                // 获取规则详情
                fetch(`/api/rules/${ruleId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (!data.error) {
                            const option = document.createElement('option');
                            option.value = ruleId;
                            option.textContent = `(ID: ${ruleId})${data.规则名称 || '未命名规则'} `;
                            targetRuleSelect.appendChild(option);
                        }
                    })
                    .catch(error => console.error('获取规则详情失败:', error));
            });
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('targetRuleSelectionModal'));
            modal.show();
        }

        // 确认合并到目标规则
        function confirmBatchMerge() {
            const targetRuleId = document.getElementById('targetRuleSelect').value;
            if (!targetRuleId) {
                showToast('请选择目标规则', 'warning');
                return;
            }
            
            const sourceRuleIdsStr = document.getElementById('sourceRuleIdsInput').value;
            if (!sourceRuleIdsStr) {
                showToast('未找到源规则ID', 'error');
                return;
            }
            
            const sourceRuleIds = JSON.parse(sourceRuleIdsStr);
            // 移除目标规则ID，避免自己合并到自己
            const filteredSourceIds = sourceRuleIds.filter(id => id != targetRuleId);
            
            if (filteredSourceIds.length === 0) {
                showToast('没有可合并的源规则', 'warning');
                return;
            }
            
            if (!confirm(`确定要将${filteredSourceIds.length}条规则合并到ID为${targetRuleId}的规则吗？此操作不可撤销。`)) {
                return;
            }
            
            // 显示加载提示
            showLoading('正在合并规则...');
            
            // 逐个合并规则
            const mergePromises = filteredSourceIds.map(sourceId => 
                fetch('/api/rules/merge', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        source_rule_id: sourceId,
                        target_rule_id: targetRuleId
                    })
                }).then(response => response.json())
            );
            
            Promise.all(mergePromises)
                .then(results => {
                    hideLoading();
                    
                    // 检查是否所有合并都成功
                    const allSuccess = results.every(result => result.success);
                    const successCount = results.filter(result => result.success).length;
                    
                    if (allSuccess) {
                        showToast(`成功合并${successCount}条规则`, 'success');
                    } else {
                        showToast(`部分规则合并成功(${successCount}/${filteredSourceIds.length})`, 'warning');
                    }
                    
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('targetRuleSelectionModal'));
                    if (modal) {
                        modal.hide();
                    }
                    
                    // 刷新表格
                    if (typeof table !== 'undefined' && table.ajax) {
                        table.ajax.reload();
                    } else {
                        location.reload();
                    }
                })
                .catch(error => {
                    hideLoading();
                    console.error('合并规则失败:', error);
                    showToast('合并规则失败', 'error');
                });
        }

        // 添加目标规则选择模态框到HTML
        document.addEventListener('DOMContentLoaded', function() {
            const modalHtml = `
            <div class="modal fade" id="targetRuleSelectionModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">选择目标规则</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <input type="hidden" id="sourceRuleIdsInput">
                            <div class="mb-3">
                                <label for="targetRuleSelect" class="form-label">请选择要合并到的目标规则：</label>
                                <select id="targetRuleSelect" class="form-select">
                                    <option value="">请选择目标规则</option>
                                </select>
                            </div>
                            <div class="alert alert-info">
                                所有选中的规则将被合并到目标规则中，此操作不可撤销。
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="confirmBatchMerge()">确认合并</button>
                        </div>
                    </div>
                </div>
            </div>
            `;
            
            // 将模态框添加到body
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        });

        // 添加回车键搜索功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为搜索输入框添加回车键事件监听
            document.getElementById('searchRuleName').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            document.getElementById('searchRuleSource').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            document.getElementById('searchBehavior').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            document.getElementById('searchRuleContent').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            // 防止表单默认提交行为
            document.getElementById('ruleForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveRule();
            });
            
            // 初始化所有模态框
            try {
                const ruleModal = new bootstrap.Modal(document.getElementById('ruleModal'));
                window.ruleModal = ruleModal; // 将模态框对象添加到window对象，使其全局可访问
                provinceModal = new bootstrap.Modal(document.getElementById('provinceModal'));
                mergeRuleModal = new bootstrap.Modal(document.getElementById('mergeRuleModal'));
                console.log('所有模态框初始化成功');
            } catch (error) {
                console.error('模态框初始化失败:', error);
            }

            // 初始化批量按钮状态
            updateBatchButtonState();

            console.log('所有事件监听器和模态框已初始化');
        });

        // 批量AI智能获取相关函数
        let batchProcessData = [];
        let batchCurrentIndex = 0;
        let batchResults = [];
        let batchProcessCancelled = false; // 添加取消标志

        // 批量AI智能获取主函数
        function batchAiIntelligentGet() {
            const selectedRules = getSelectedRules();
            if (selectedRules.length === 0) {
                showToast('请先选择要处理的规则', 'warning');
                return;
            }

            // 重置取消标志
            batchProcessCancelled = false;

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('batchAiModal'));
            modal.show();

            // 更新选中数量
            document.getElementById('batchSelectedCount').textContent = selectedRules.length;

            // 准备批量处理数据
            batchProcessData = selectedRules;
            batchCurrentIndex = 0;
            batchResults = [];

            // 清空结果表格
            document.getElementById('batchResultBody').innerHTML = '';

            // 隐藏进度条
            document.getElementById('batchProgressContainer').style.display = 'none';

            // 启用开始按钮
            document.getElementById('startBatchBtn').disabled = false;
            document.getElementById('saveBatchBtn').disabled = true;
            document.getElementById('cancelBatchBtn').disabled = false;

            // 监听模态框关闭事件
            const batchModal = document.getElementById('batchAiModal');
            batchModal.addEventListener('hidden.bs.modal', function() {
                cancelBatchProcess();
            });
        }

        // 处理复选框变化
        function handleCheckboxChange() {
            updateBatchButtonState();
        }

        // 更新批量按钮状态
        function updateBatchButtonState() {
            const selectedCount = document.querySelectorAll('.rule-checkbox:checked').length;
            const batchAiBtn = document.getElementById('batchAiBtn');
            const batchMergeBtn = document.getElementById('batchMergeBtn');

            if (batchAiBtn) {
                batchAiBtn.disabled = selectedCount === 0;
                batchAiBtn.title = selectedCount === 0 ?
                    '请先选择要处理的规则' :
                    `批量AI智能分析选中的 ${selectedCount} 条规则`;
            }

            if (batchMergeBtn) {
                batchMergeBtn.disabled = selectedCount === 0;
            }
        }

        // 获取选中的规则
        function getSelectedRules() {
            const selectedRules = [];
            const checkboxes = document.querySelectorAll('.rule-checkbox:checked');

            checkboxes.forEach(checkbox => {
                const row = checkbox.closest('tr');
                const rowData = table.row(row).data();
                if (rowData) {
                    selectedRules.push({
                        id: rowData.ID,
                        ruleName: rowData.规则名称,
                        city: rowData.城市,
                        behaviorType: rowData.行为认定,
                        ruleContent: rowData.规则内涵
                    });
                }
            });

            return selectedRules;
        }

        // 开始批量AI处理
        async function startBatchAiProcess() {
            if (batchProcessData.length === 0) {
                showToast('没有要处理的数据', 'warning');
                return;
            }

            // 重置取消标志
            batchProcessCancelled = false;

            // 禁用开始按钮，启用取消按钮
            document.getElementById('startBatchBtn').disabled = true;
            document.getElementById('cancelBatchBtn').disabled = false;

            // 显示进度条
            document.getElementById('batchProgressContainer').style.display = 'block';

            // 重置进度
            batchCurrentIndex = 0;
            batchResults = [];
            updateBatchProgress();

            // 开始处理
            for (let i = 0; i < batchProcessData.length; i++) {
                // 检查是否已取消
                if (batchProcessCancelled) {
                    showToast('批量处理已取消', 'warning');
                    break;
                }

                batchCurrentIndex = i;
                updateBatchProgress();

                const rule = batchProcessData[i];
                await processSingleRule(rule, i);

                // 检查是否已取消（在处理完单个规则后再次检查）
                if (batchProcessCancelled) {
                    showToast('批量处理已取消', 'warning');
                    break;
                }

                // 添加延迟避免API限制
                if (i < batchProcessData.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            // 处理完成或取消
            if (!batchProcessCancelled) {
                batchCurrentIndex = batchProcessData.length;
                updateBatchProgress();

                // 启用保存按钮
                document.getElementById('saveBatchBtn').disabled = false;

                showToast(`批量处理完成！成功处理 ${batchResults.filter(r => r.success).length} 条规则`, 'success');
            }

            // 禁用取消按钮
            document.getElementById('cancelBatchBtn').disabled = true;
        }

        // 处理单个规则
        async function processSingleRule(rule, index) {
            try {
                // 检查是否已取消
                if (batchProcessCancelled) {
                    return;
                }

                // 添加处理中的行
                addBatchResultRow(rule, index, 'processing', '处理中...', '', '', '', '');

                // 调用AI智能获取API
                const response = await fetch('/api/rules/intelligent-get-medical-names', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        rule_name: rule.ruleName,
                        behavior_type: rule.behaviorType,
                        rule_content: rule.ruleContent,
                        city: rule.city
                    })
                });

                // 再次检查是否已取消
                if (batchProcessCancelled) {
                    return;
                }

                const result = await response.json();

                if (result.success) {
                    // 更新结果行
                    updateBatchResultRow(index, 'success', '成功',
                        result.medical_name1 || '',
                        result.medical_name2 || '',
                        result.type || '',
                        result.violation_count || '');

                    // 保存结果到指定索引位置
                    batchResults[index] = {
                        success: true,
                        ruleId: rule.id,
                        data: result
                    };
                } else {
                    // 更新结果行为失败状态
                    updateBatchResultRow(index, 'error', '失败: ' + (result.error || '未知错误'), '', '', '', '');

                    batchResults[index] = {
                        success: false,
                        ruleId: rule.id,
                        error: result.error || '未知错误'
                    };
                }
            } catch (error) {
                console.error('处理规则失败:', error);

                // 更新结果行为错误状态
                updateBatchResultRow(index, 'error', '错误: ' + error.message, '', '', '', '');

                batchResults[index] = {
                    success: false,
                    ruleId: rule.id,
                    error: error.message
                };
            }
        }

        // 更新批量处理进度
        function updateBatchProgress() {
            const total = batchProcessData.length;
            const current = batchCurrentIndex + 1;
            const percentage = Math.round((batchCurrentIndex / total) * 100);

            document.getElementById('batchProgressText').textContent = `${current}/${total}`;
            document.getElementById('batchProgressBar').style.width = `${percentage}%`;
        }

        // 添加批量结果行
        function addBatchResultRow(rule, index, status, statusText, medicalName1, medicalName2, type, violationCount) {
            const tbody = document.getElementById('batchResultBody');
            const row = document.createElement('tr');
            row.id = `batch-row-${index}`;

            let statusClass = '';
            let statusIcon = '';

            switch (status) {
                case 'processing':
                    statusClass = 'text-warning';
                    statusIcon = '<i class="bi bi-hourglass-split"></i>';
                    break;
                case 'success':
                    statusClass = 'text-success';
                    statusIcon = '<i class="bi bi-check-circle"></i>';
                    break;
                case 'error':
                    statusClass = 'text-danger';
                    statusIcon = '<i class="bi bi-x-circle"></i>';
                    break;
            }

            row.innerHTML = `
                <td>${rule.id}</td>
                <td title="${rule.ruleName}">${rule.ruleName.length > 20 ? rule.ruleName.substring(0, 20) + '...' : rule.ruleName}</td>
                <td>${rule.city}</td>
                <td class="${statusClass}">${statusIcon} ${statusText}</td>
                <td title="${medicalName1}">${medicalName1.length > 15 ? medicalName1.substring(0, 15) + '...' : medicalName1}</td>
                <td title="${medicalName2}">${medicalName2.length > 15 ? medicalName2.substring(0, 15) + '...' : medicalName2}</td>
                <td>${type}</td>
                <td>${violationCount}</td>
                <td>
                    ${status === 'success' ?
                        '<button class="btn btn-sm btn-outline-primary" onclick="previewBatchResult(' + index + ')">预览</button>' :
                        status === 'error' ?
                        '<button class="btn btn-sm btn-outline-warning" onclick="retryBatchRule(' + index + ')"><i class="bi bi-arrow-clockwise"></i> 重试</button>' :
                        '-'}
                </td>
            `;

            tbody.appendChild(row);
        }

        // 更新批量结果行
        function updateBatchResultRow(index, status, statusText, medicalName1, medicalName2, type, violationCount) {
            const row = document.getElementById(`batch-row-${index}`);
            if (!row) return;

            let statusClass = '';
            let statusIcon = '';

            switch (status) {
                case 'processing':
                    statusClass = 'text-warning';
                    statusIcon = '<i class="bi bi-hourglass-split"></i>';
                    break;
                case 'success':
                    statusClass = 'text-success';
                    statusIcon = '<i class="bi bi-check-circle"></i>';
                    break;
                case 'error':
                    statusClass = 'text-danger';
                    statusIcon = '<i class="bi bi-x-circle"></i>';
                    break;
            }

            const cells = row.cells;
            cells[3].className = statusClass;
            cells[3].innerHTML = `${statusIcon} ${statusText}`;
            cells[4].textContent = medicalName1.length > 15 ? medicalName1.substring(0, 15) + '...' : medicalName1;
            cells[4].title = medicalName1;
            cells[5].textContent = medicalName2.length > 15 ? medicalName2.substring(0, 15) + '...' : medicalName2;
            cells[5].title = medicalName2;
            cells[6].textContent = type;
            cells[7].textContent = violationCount;
            cells[8].innerHTML = status === 'success' ?
                `<button class="btn btn-sm btn-outline-primary" onclick="previewBatchResult(${index})">预览</button>` :
                status === 'error' ?
                `<button class="btn btn-sm btn-outline-warning" onclick="retryBatchRule(${index})"><i class="bi bi-arrow-clockwise"></i> 重试</button>` :
                '-';
        }

        // 预览批量结果
        function previewBatchResult(index) {
            const result = batchResults[index];
            if (!result || !result.success) return;

            const data = result.data;
            let preview = `
                <div class="row">
                    <div class="col-md-6"><strong>医保名称1:</strong> ${data.medical_name1 || ''}</div>
                    <div class="col-md-6"><strong>医保名称2:</strong> ${data.medical_name2 || ''}</div>
                    <div class="col-md-6"><strong>类型:</strong> ${data.type || ''}</div>
                    <div class="col-md-6"><strong>违规数量:</strong> ${data.violation_count || ''}</div>
                    <div class="col-md-6"><strong>时间类型:</strong> ${data.time_type || ''}</div>
                    <div class="col-md-6"><strong>违规金额:</strong> ${data.violation_amount || ''}</div>
                    <div class="col-md-6"><strong>年龄限制:</strong> ${data.age_limit || ''}</div>
                    <div class="col-md-6"><strong>性别限制:</strong> ${data.gender_limit || ''}</div>
                    <div class="col-md-12"><strong>排除诊断:</strong> ${data.exclude_diagnosis || ''}</div>
                    <div class="col-md-12"><strong>排除科室:</strong> ${data.exclude_departments || ''}</div>
                    <div class="col-md-12"><strong>包含诊断:</strong> ${data.include_diagnosis || ''}</div>
                    <div class="col-md-12"><strong>包含科室:</strong> ${data.include_departments || ''}</div>
                </div>
            `;

            // 创建预览模态框
            const previewModal = document.createElement('div');
            previewModal.className = 'modal fade';
            previewModal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">AI分析结果预览</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${preview}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(previewModal);
            const modal = new bootstrap.Modal(previewModal);
            modal.show();

            // 模态框关闭后移除元素
            previewModal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(previewModal);
            });
        }

        // 取消批量处理
        function cancelBatchProcess() {
            batchProcessCancelled = true;

            // 重置按钮状态
            document.getElementById('startBatchBtn').disabled = false;
            document.getElementById('cancelBatchBtn').disabled = true;
            document.getElementById('saveBatchBtn').disabled = true;

            // 隐藏进度条
            document.getElementById('batchProgressContainer').style.display = 'none';

            console.log('批量处理已取消');
        }

        // 手动取消批量处理
        function manualCancelBatch() {
            if (confirm('确定要取消当前的批量处理吗？')) {
                cancelBatchProcess();
                showToast('批量处理已取消', 'warning');
            }
        }

        // 重试单个失败的规则
        async function retryBatchRule(index) {
            if (index >= batchProcessData.length || index < 0) {
                showToast('无效的规则索引', 'error');
                return;
            }

            const rule = batchProcessData[index];

            try {
                // 更新状态为处理中
                updateBatchResultRow(index, 'processing', '重试中...', '', '', '', '');

                // 重新处理这个规则 - 调用修改后的处理函数
                await processSingleRuleForRetry(rule, index);

                showToast(`规则 "${rule.ruleName}" 重试完成`, 'success');

            } catch (error) {
                console.error(`重试规则 ${rule.id} 失败:`, error);
                updateBatchResultRow(index, 'error', '重试失败: ' + error.message, '', '', '', '');
                showToast(`规则 "${rule.ruleName}" 重试失败`, 'error');
            }
        }

        // 专门用于重试的处理函数，不会添加新行
        async function processSingleRuleForRetry(rule, index) {
            try {
                // 检查是否已取消
                if (batchProcessCancelled) {
                    return;
                }

                // 调用AI智能获取API
                const response = await fetch('/api/rules/intelligent-get-medical-names', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        rule_name: rule.ruleName,
                        behavior_type: rule.behaviorType,
                        rule_content: rule.ruleContent,
                        city: rule.city
                    })
                });

                // 再次检查是否已取消
                if (batchProcessCancelled) {
                    return;
                }

                const result = await response.json();

                if (result.success) {
                    // 更新结果行
                    updateBatchResultRow(index, 'success', '成功',
                        result.medical_name1 || '',
                        result.medical_name2 || '',
                        result.type || '',
                        result.violation_count || '');

                    // 更新结果数组
                    batchResults[index] = {
                        success: true,
                        ruleId: rule.id,
                        data: result
                    };
                } else {
                    // 更新结果行为失败状态
                    updateBatchResultRow(index, 'error', '失败: ' + (result.error || '未知错误'), '', '', '', '');

                    batchResults[index] = {
                        success: false,
                        ruleId: rule.id,
                        error: result.error || '未知错误'
                    };
                }
            } catch (error) {
                console.error('重试处理规则失败:', error);

                // 更新结果行为错误状态
                updateBatchResultRow(index, 'error', '错误: ' + error.message, '', '', '', '');

                batchResults[index] = {
                    success: false,
                    ruleId: rule.id,
                    error: error.message
                };
            }
        }

        // 保存批量结果 - 复用现有的保存逻辑
        async function saveBatchResults() {
            const successResults = batchResults.filter(r => r.success);

            if (successResults.length === 0) {
                showToast('没有成功的结果可以保存', 'warning');
                return;
            }

            try {
                // 禁用保存按钮
                document.getElementById('saveBatchBtn').disabled = true;
                document.getElementById('saveBatchBtn').innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';

                let savedCount = 0;
                let errorCount = 0;

                for (const result of successResults) {
                    try {
                        // 获取规则对应的城市信息
                        const ruleData = batchProcessData.find(r => r.id === result.ruleId);
                        const city = ruleData ? ruleData.city : '西安';

                        // 1. 更新规则基本信息（使用专门的批量更新API）
                        const ruleResponse = await fetch(`/api/rules/batch-update-ai-fields/${result.ruleId}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                type: result.data.type || '',
                                violation_count: result.data.violation_count || '',
                                time_type: result.data.time_type || '',
                                violation_amount: result.data.violation_amount || '',
                                age_limit: result.data.age_limit || '',
                                gender_limit: result.data.gender_limit || '',
                                exclude_diagnosis: result.data.exclude_diagnosis || '',
                                exclude_departments: result.data.exclude_departments || '',
                                include_diagnosis: result.data.include_diagnosis || '',
                                include_departments: result.data.include_departments || ''
                            })
                        });

                        if (!ruleResponse.ok) {
                            const errorData = await ruleResponse.json();
                            throw new Error(errorData.error || '更新规则失败');
                        }

                        // 2. 更新医保编码对照（复用saveRule的逻辑）
                        if (city && city !== '') {
                            const medicalCodeData = {
                                城市: city,
                                医保编码1: result.data.medical_codes?.medical_codes1 || '',
                                医保名称1: result.data.medical_name1 || '',
                                医保编码2: result.data.medical_codes?.medical_codes2 || '',
                                医保名称2: result.data.medical_name2 || ''
                            };

                            // 检查是否存在对照记录
                            const cityResponse = await fetch(`/api/rules/city/${encodeURIComponent(city)}/${result.ruleId}`);
                            const cityData = await cityResponse.json();

                            let medicalResponse;
                            if (cityData.COMPARE_ID) {
                                // 如果存在对照ID，执行更新操作
                                medicalResponse = await fetch(`/api/rules/${result.ruleId}`, {
                                    method: 'PUT',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify({
                                        ...medicalCodeData,
                                        对照ID: cityData.COMPARE_ID
                                    })
                                });
                            } else {
                                // 如果不存在对照ID，执行新增操作
                                medicalResponse = await fetch(`/api/rule_medical_codes/${result.ruleId}`, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify(medicalCodeData)
                                });
                            }

                            if (!medicalResponse.ok) {
                                const errorData = await medicalResponse.json();
                                throw new Error(errorData.error || '保存医保编码对照失败');
                            }
                        }

                        savedCount++;
                    } catch (error) {
                        errorCount++;
                        console.error(`保存规则 ${result.ruleId} 失败:`, error);
                    }
                }

                // 恢复保存按钮
                document.getElementById('saveBatchBtn').disabled = false;
                document.getElementById('saveBatchBtn').innerHTML = '<i class="bi bi-save"></i> 保存所有结果';

                // 显示结果
                if (savedCount > 0) {
                    showToast(`成功保存 ${savedCount} 条规则${errorCount > 0 ? `，${errorCount} 条失败` : ''}`,
                             errorCount > 0 ? 'warning' : 'success');

                    // 刷新表格
                    table.ajax.reload();

                    // 关闭模态框
                    bootstrap.Modal.getInstance(document.getElementById('batchAiModal')).hide();
                } else {
                    showToast('保存失败，请检查网络连接', 'error');
                }

            } catch (error) {
                console.error('批量保存失败:', error);
                showToast('批量保存失败: ' + error.message, 'error');

                // 恢复保存按钮
                document.getElementById('saveBatchBtn').disabled = false;
                document.getElementById('saveBatchBtn').innerHTML = '<i class="bi bi-save"></i> 保存所有结果';
            }
        }

        // 添加一个辅助函数，用于检查可用序列并选择正确的序列
        async function checkAvailableSequences() {
            try {
                const response = await fetch('/api/sequences');
                const data = await response.json();
                console.log('数据库中可用的序列:', data.sequences);
                
                // 检查常见序列名称是否存在
                const sequenceNames = data.sequences || [];
                const possibleNames = [
                    '规则医保编码对照_SEQ',
                    'RULE_CITY_COMPARE_SEQ',
                    '规则医保编码对照ID_SEQ',
                    '规则城市对照_SEQ'
                ];
                
                for (const name of possibleNames) {
                    if (sequenceNames.includes(name)) {
                        console.log('找到匹配的序列名:', name);
                        return name;
                    }
                }
                
                // 如果没有找到匹配的序列，但有其他序列可用
                if (sequenceNames.length > 0) {
                    const filtered = sequenceNames.filter(name => 
                        name.includes('规则') || 
                        name.includes('RULE') || 
                        name.includes('对照')
                    );
                    
                    if (filtered.length > 0) {
                        console.log('找到可能的序列:', filtered[0]);
                        return filtered[0];
                    }
                }
                
                return null;
            } catch (error) {
                console.error('检查序列失败:', error);
                return null;
            }
        }
        
        // 修改获取序列的函数，先检查可用序列
        async function getNextSequence() {
            try {
                // 先尝试获取正确的序列名
                const sequenceName = await checkAvailableSequences();
                if (!sequenceName) {
                    throw new Error('未找到合适的序列');
                }
                
                console.log('使用序列名:', sequenceName);
                const response = await fetch(`/api/sequence/${sequenceName}`);
                const data = await response.json();
                console.log('序列响应数据:', data);
                
                if (data.sequence) {
                    return data.sequence;
                }
                throw new Error('获取序列号失败');
            } catch (error) {
                console.error('获取序列号失败:', error);
                showToast('获取序列号失败: ' + error.message, 'error');
                return null;
            }
        }
        </script>
</body>
</html> 