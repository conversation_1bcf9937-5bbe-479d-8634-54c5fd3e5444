import os
import pandas as pd
import warnings
import re

# 忽略特定的警告
warnings.filterwarnings("ignore", category=UserWarning, module="openpyxl")

# 设置包含Excel文件的文件夹路径
folder_path = r'C:\Users\<USER>\Desktop\工作个人\2025重庆飞检\重庆市_重庆大学附属涪陵医院_医院_20250512134840_多数据源导出'  # 替换为你的文件夹路径

# 获取文件夹中所有的Excel文件
excel_files = [file for file in os.listdir(folder_path) if file.endswith('.xlsx')]

# 创建一个空的DataFrame用于存储汇总数据
summary_data = pd.DataFrame()


# 1. 遍历文件夹中的文件
for filename in os.listdir(folder_path):
    if os.path.isfile(os.path.join(folder_path, filename)):
        # 1.1 替换文件名中的指定前缀
        old_name = filename
        new_name = re.sub(r"北京市_儿童医院_医院-北京自查自纠", "", old_name)
        os.rename(os.path.join(folder_path, old_name), os.path.join(folder_path, new_name))
        print(f"文件名已修改：{old_name} -> {new_name}")


# 遍历每个Excel文件
for file in excel_files:
    file_path = os.path.join(folder_path, file)
    print(f"正在处理文件: {file}")  # 打印当前处理的文件名
    try:
        # 读取每个文件中的“规则详情”工作表
        data = pd.read_excel(file_path, sheet_name='规则详情', engine='openpyxl')
        # 检查读取的数据是否有效
        if data.empty:
            print(f"文件 {file} 中的'规则详情'工作表为空，跳过该文件")
            continue
        # 添加一列，填充文件名
        data['文件名'] = file
        # 如果是第一个文件，保留其表头
        if summary_data.empty:
            summary_data = data
            print(f"初始化汇总数据，使用文件 {file} 的表头")
        else:
            # 确保列名一致
            if not data.columns.equals(summary_data.columns):
                print(f"文件 {file} 的列名与之前的文件不一致，跳过该文件")
                continue
            summary_data = pd.concat([summary_data, data], ignore_index=True)
            print(f"从文件 {file} 中追加了 {len(data)} 行数据")
    except Exception as e:
        print(f"处理文件 {file} 时出错: {e}")

# 将汇总数据写入新的Excel文件
summary_data.to_excel(os.path.join(folder_path, '汇总结果.xlsx'), index=False, engine='openpyxl')

print("汇总完成，结果已保存到'汇总结果.xlsx'")