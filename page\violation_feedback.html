<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞检违规反馈</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        /* 全局缩放到90% */
        body {
            transform: scale(0.9);
            transform-origin: top left;
            width: 111.11%; /* 100% / 0.9 = 111.11% */
            margin: 0;
            padding: 0;
            min-height: auto;
        }
        
        .container-fluid {
            width: 100%;
            max-width: none;
            min-height: auto;
        }
        
        /* 确保页面内容不会产生多余的空白 */
        html, body {
            overflow-x: hidden;
        }

        .step-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .step-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        
        .step-1 { background-color: #007bff; }
        .step-2 { background-color: #28a745; }
        
        .file-drop-zone {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: border-color 0.3s;
            cursor: pointer;
        }
        
        .file-drop-zone:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        
        .file-drop-zone.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        
        .progress-container {
            display: none;
        }
        
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background-color: #1e1e1e;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            color: #ffffff;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 4px;
            padding: 2px 0;
        }

        .log-time {
            color: #888;
            font-size: 11px;
        }

        .log-message {
            margin-left: 5px;
        }

        .log-container .text-success {
            color: #28a745 !important;
        }

        .log-container .text-warning {
            color: #ffc107 !important;
        }

        .log-container .text-danger {
            color: #dc3545 !important;
        }

        .log-container .text-primary {
            color: #007bff !important;
        }

        .log-container .text-info {
            color: #17a2b8 !important;
        }
        
        .status-badge {
            font-size: 0.8em;
        }
        
        .result-table {
            font-size: 0.9em;
        }
        
        .summary-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-shield-exclamation text-warning"></i>
                        飞检违规反馈
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="/">主页</a></li>
                            <li class="breadcrumb-item active">飞检违规反馈</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h5 class="alert-heading">
                        <i class="bi bi-info-circle"></i> 功能说明
                    </h5>
                    <p class="mb-2">本工具用于自动处理飞检违规数据，一键完成以下操作：</p>
                    <ol class="mb-0">
                        <li><strong>规则汇总</strong>：将指定文件夹中所有Excel文件的"规则详情"工作表汇总到一个文件中</li>
                        <li><strong>违规统计</strong>：自动计算每个文件的违规数量和违规金额总和，并回填到汇总文件中</li>
                        <li><strong>结果生成</strong>：生成以文件夹名命名的最终汇总结果文件</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- 操作卡片 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card step-card">
                    <div class="card-body text-center">
                        <div class="mb-4">
                            <div class="step-number step-1 mx-auto mb-3" style="width: 60px; height: 60px; font-size: 24px;">
                                <i class="bi bi-magic"></i>
                            </div>
                            <h4 class="card-title">一键处理飞检违规数据</h4>
                            <p class="card-text text-muted">
                                输入包含Excel文件的文件夹路径，系统将自动完成规则汇总和违规统计，
                                生成完整的违规反馈报告。无需上传文件，只需提供文件夹路径即可。
                            </p>
                        </div>
                        <div class="d-grid gap-2 d-md-block">
                            <button class="btn btn-primary btn-lg px-5" onclick="showFolderSelector()">
                                <i class="bi bi-play-circle"></i> 输入文件夹路径开始处理
                            </button>
                            <button class="btn btn-outline-secondary btn-lg px-4 ms-md-3" onclick="showHelp()">
                                <i class="bi bi-question-circle"></i> 使用帮助
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件夹选择模态框 -->
        <div class="modal fade" id="folderModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">选择文件夹进行飞检违规数据处理</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>注意：</strong>请确保选择的文件夹中包含需要处理的Excel文件，且每个文件都有"规则详情"工作表。
                        </div>

                        <div class="mb-3">
                            <label for="folderPath" class="form-label">
                                <i class="bi bi-folder2-open"></i> 文件夹路径
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="folderPath"
                                       placeholder="请输入文件夹路径，例如：D:\飞检数据\芜湖市_大病数据_医保_20250620112815">
                                <button class="btn btn-outline-primary" type="button" onclick="showPathHelp()">
                                    <i class="bi bi-question-circle"></i> 如何获取路径
                                </button>
                                <button class="btn btn-outline-info" type="button" onclick="testFolder()">
                                    <i class="bi bi-search"></i> 测试
                                </button>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle"></i>
                                <strong>请直接输入文件夹的完整路径</strong>，系统将自动处理该文件夹中的所有Excel文件。
                                <br>
                                <small class="text-muted">
                                    不知道如何获取路径？点击"如何获取路径"按钮查看详细步骤。
                                    建议先点击"测试"验证文件夹内容。
                                </small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="optimizePerformance" unchecked>
                                <label class="form-check-label" for="optimizePerformance">
                                    <strong>启用性能优化（推荐）</strong>
                                </label>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-lightning"></i>
                                <strong>性能优化功能：</strong>
                                <ul class="mb-0 mt-1">
                                    <li>多线程并发处理Excel文件（4个线程同时工作）</li>
                                    <li>只读取必要的列和工作表，减少内存占用</li>
                                    <li>批量计算违规统计，提升计算速度</li>
                                    <li>适合处理大量文件，速度提升5-10倍</li>
                                </ul>
                                <small class="text-muted">注意：如果文件较少（少于5个），建议关闭以避免线程开销</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="executeProcessing()" id="executeBtn">
                            <i class="bi bi-magic"></i> 开始一键处理
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 路径获取帮助模态框 -->
        <div class="modal fade" id="pathHelpModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-folder2-open"></i> 如何获取文件夹路径
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-lightbulb"></i> 获取文件夹路径的步骤：</h6>
                            <ol class="mb-2">
                                <li><strong>打开文件资源管理器</strong>，找到包含Excel文件的文件夹</li>
                                <li><strong>点击地址栏</strong>（或按 <kbd>Ctrl+L</kbd>）</li>
                                <li><strong>复制完整路径</strong>（<kbd>Ctrl+C</kbd>）</li>
                                <li><strong>粘贴到输入框</strong>（<kbd>Ctrl+V</kbd>）</li>
                            </ol>
                        </div>

                        <h6>示例路径：</h6>
                        <div class="bg-light p-3 rounded mb-3">
                            <code>D:\Personal\Desktop\芜湖检查\芜湖市_大病数据_医保_20250620112815_最终规则46条_多数据源导出</code>
                        </div>

                        <h6>注意事项：</h6>
                        <ul class="mb-0">
                            <li>确保文件夹中包含需要处理的Excel文件</li>
                            <li>每个Excel文件都应该有"规则详情"工作表</li>
                            <li>路径中不要包含特殊字符</li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">我知道了</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用帮助模态框 -->
        <div class="modal fade" id="helpModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-question-circle"></i> 使用帮助
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <h6>功能说明</h6>
                        <p>飞检违规反馈工具用于自动处理飞检Excel文件，生成违规统计报告。</p>

                        <h6>使用步骤</h6>
                        <ol>
                            <li>准备包含Excel文件的文件夹</li>
                            <li>确保每个Excel文件都有"规则详情"工作表</li>
                            <li>输入文件夹路径（点击"如何获取路径"查看详细步骤）</li>
                            <li>点击"测试"验证文件夹内容（可选）</li>
                            <li>点击"开始一键处理"</li>
                            <li>等待处理完成，查看结果</li>
                        </ol>

                        <h6>文件要求</h6>
                        <ul>
                            <li>Excel文件格式：.xlsx</li>
                            <li>必须包含"规则详情"工作表</li>
                            <li>工作表中需要有"违规数量"和"违规金额"列</li>
                        </ul>

                        <h6>输出结果</h6>
                        <ul>
                            <li>生成以文件夹名命名的汇总结果文件</li>
                            <li>包含所有文件的规则详情汇总</li>
                            <li>自动计算违规数量和金额统计</li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">我知道了</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 进度显示 -->
        <div class="progress-container" id="progressContainer">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-gear-fill spin"></i> 
                        <span id="progressTitle">正在处理...</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="progressBar"></div>
                    </div>
                    <div class="log-container" id="logContainer"></div>
                </div>
            </div>
        </div>

        <!-- 结果显示 -->
        <div id="resultContainer" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-check-circle text-success"></i> 
                        处理结果
                    </h5>
                </div>
                <div class="card-body">
                    <div class="summary-stats" id="summaryStats">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <span class="stat-number" id="totalFiles">0</span>
                                    <span class="stat-label">处理文件数</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <span class="stat-number" id="totalViolations">0</span>
                                    <span class="stat-label">违规总数量</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <span class="stat-number" id="totalAmount">0</span>
                                    <span class="stat-label">违规总金额(元)</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <span class="stat-number" id="processingTime">0</span>
                                    <span class="stat-label">处理耗时(秒)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped result-table" id="resultTable">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>文件名</th>
                                    <th>处理状态</th>
                                    <th>违规数量</th>
                                    <th>违规金额(元)</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody id="resultTableBody">
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-success me-2" onclick="downloadResult()">
                            <i class="bi bi-download"></i> 下载结果文件
                        </button>
                        <button class="btn btn-outline-primary" onclick="resetProcess()">
                            <i class="bi bi-arrow-clockwise"></i> 重新开始
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let currentStep = '';
        let folderModal;
        let startTime;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            folderModal = new bootstrap.Modal(document.getElementById('folderModal'));
        });

        // 显示文件夹选择器
        function showFolderSelector() {
            // 清空输入框
            document.getElementById('folderPath').value = '';
            folderModal.show();
        }

        // 显示帮助
        function showHelp() {
            const helpModal = new bootstrap.Modal(document.getElementById('helpModal'));
            helpModal.show();
        }

        // 显示路径获取帮助
        function showPathHelp() {
            const helpModal = new bootstrap.Modal(document.getElementById('pathHelpModal'));
            helpModal.show();
        }



        // 测试文件夹
        function testFolder() {
            const folderPath = document.getElementById('folderPath').value.trim();

            if (!folderPath) {
                showToast('请先输入文件夹路径', 'warning');
                return;
            }

            addLog('开始测试文件夹...', 'info');
            addLog(`测试路径: ${folderPath}`, 'info');

            fetch('/api/test_folder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    folder_path: folderPath
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog('✓ 文件夹测试成功！', 'success');
                    addLog(`✓ 文件夹路径: ${data.folder_path}`, 'success');
                    addLog(`✓ 总文件数: ${data.total_files}`, 'info');
                    addLog(`✓ 有效Excel文件数: ${data.valid_excel_count}`, 'success');

                    if (data.excel_files.length > 0) {
                        addLog('✓ 有效Excel文件列表:', 'info');
                        data.excel_files.slice(0, 10).forEach((file, index) => {
                            addLog(`  ${index + 1}. ${file}`, 'info');
                        });
                        if (data.excel_files.length > 10) {
                            addLog(`  ... 还有 ${data.excel_files.length - 10} 个文件`, 'info');
                        }
                    }

                    if (data.excluded_files.length > 0) {
                        addLog('⚠ 已排除的文件:', 'warning');
                        data.excluded_files.forEach((file, index) => {
                            addLog(`  ${index + 1}. ${file}`, 'warning');
                        });
                    }

                    if (data.valid_excel_count > 0) {
                        addLog(`✓ 文件夹测试通过，可以开始处理 ${data.valid_excel_count} 个Excel文件`, 'success');
                        showToast(`文件夹测试成功！找到 ${data.valid_excel_count} 个有效Excel文件`, 'success');
                    } else {
                        addLog('✗ 文件夹中没有找到有效的Excel文件', 'error');
                        showToast('文件夹中没有找到有效的Excel文件', 'error');
                    }
                } else {
                    addLog(`✗ 文件夹测试失败: ${data.error}`, 'error');
                    showToast('文件夹测试失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                addLog(`✗ 测试请求失败: ${error.message}`, 'error');
                showToast('测试请求失败: ' + error.message, 'error');
            });
        }

        // 执行一键处理
        function executeProcessing() {
            const folderPath = document.getElementById('folderPath').value.trim();
            const optimizePerformance = document.getElementById('optimizePerformance').checked;

            if (!folderPath) {
                showToast('请输入文件夹路径', 'warning');
                return;
            }

            // 关闭模态框
            folderModal.hide();

            // 显示进度
            showProgress();

            // 执行一键处理
            executeOneStepProcessing(folderPath, optimizePerformance);
        }

        // 日志轮询相关变量
        let logPollingInterval = null;
        let lastLogTime = null;

        // 启动日志轮询
        function startLogPolling() {
            lastLogTime = new Date().toISOString();
            logPollingInterval = setInterval(fetchRecentLogs, 1000); // 每秒轮询一次
        }

        // 停止日志轮询
        function stopLogPolling() {
            if (logPollingInterval) {
                clearInterval(logPollingInterval);
                logPollingInterval = null;
            }
        }

        // 获取最近的日志
        function fetchRecentLogs() {
            fetch('/api/recent_logs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    since: lastLogTime
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.logs && data.logs.length > 0) {
                    data.logs.forEach(log => {
                        // 解析日志级别和消息
                        let logType = 'info';
                        let message = log.message;

                        // 智能解析不同类型的日志
                        if (message.includes('✓ 成功读取文件:') || message.includes('✓ 成功处理文件:')) {
                            // 简化文件名显示
                            const match = message.match(/✓ 成功[读取|处理]文件: (.+?)\.xlsx/);
                            if (match) {
                                const fileName = match[1];
                                const simplifiedName = fileName.replace(/芜湖市_大病数据_医保-[^_]+_/, '').substring(0, 50);
                                message = `✅ 处理文件: ${simplifiedName}...`;
                            }
                            logType = 'success';
                        } else if (message.includes('✓ 统计完成:')) {
                            // 提取统计信息
                            const match = message.match(/✓ 统计完成: (.+?)\.xlsx \(违规数量: (\d+), 违规金额: ([\d.]+)元\)/);
                            if (match) {
                                const fileName = match[1];
                                const count = match[2];
                                const amount = match[3];
                                const simplifiedName = fileName.replace(/芜湖市_大病数据_医保-[^_]+_/, '').substring(0, 40);
                                message = `📊 统计完成: ${simplifiedName}... (${count}条, ${amount}元)`;
                            }
                            logType = 'success';
                        } else if (message.includes('✗ 统计失败:')) {
                            const match = message.match(/✗ 统计失败: (.+?)\.xlsx/);
                            if (match) {
                                const fileName = match[1];
                                const simplifiedName = fileName.replace(/芜湖市_大病数据_医保-[^_]+_/, '').substring(0, 40);
                                message = `❌ 统计失败: ${simplifiedName}...`;
                            }
                            logType = 'error';
                        } else if (message.includes('找到') && message.includes('个有效Excel文件')) {
                            message = `📂 ${message}`;
                            logType = 'info';
                        } else if (message.includes('规则汇总完成')) {
                            message = `✅ ${message}`;
                            logType = 'success';
                        } else if (message.includes('开始执行违规统计')) {
                            message = `🔢 ${message}`;
                            logType = 'processing';
                        } else if (message.includes('需要统计') && message.includes('个文件')) {
                            message = `📊 ${message}`;
                            logType = 'info';
                        } else if (message.includes('一键处理完成')) {
                            message = `🎉 ${message}`;
                            logType = 'success';
                        } else if (message.includes('正在处理文件:') || message.includes('正在统计违规数据:')) {
                            const match = message.match(/\[(\d+)\/(\d+)\] 正在[处理文件|统计违规数据]: (.+?)\.xlsx/);
                            if (match) {
                                const current = match[1];
                                const total = match[2];
                                const fileName = match[3];
                                const simplifiedName = fileName.replace(/芜湖市_大病数据_医保-[^_]+_/, '').substring(0, 40);
                                message = `🔄 [${current}/${total}] 正在处理: ${simplifiedName}...`;
                            }
                            logType = 'processing';
                        }

                        addLog(message, logType);
                    });

                    // 更新最后日志时间
                    if (data.logs.length > 0) {
                        lastLogTime = data.logs[data.logs.length - 1].timestamp;
                    }
                }
            })
            .catch(error => {
                console.error('获取日志失败:', error);
            });
        }

        // 执行一键处理
        function executeOneStepProcessing(folderPath, optimizePerformance) {
            startTime = Date.now();
            updateProgress(10, '正在执行飞检违规数据处理...');
            addLog('🚀 开始执行一键处理...', 'info');
            addLog(`📁 文件夹路径: ${folderPath}`, 'info');
            addLog(`⚙️ 性能优化: ${optimizePerformance ? '启用（多线程并发处理）' : '禁用（单线程处理）'}`, 'info');

            if (optimizePerformance) {
                addLog('⚡ 性能优化已启用，将使用4个线程并发处理文件', 'success');
            } else {
                addLog('🐌 性能优化已禁用，将使用单线程顺序处理文件', 'warning');
            }

            // 启动日志轮询
            startLogPolling();

            // 模拟处理阶段的进度更新
            updateProgress(20, '正在读取文件夹内容...');

            fetch('/api/violation_processing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    folder_path: folderPath,
                    optimize_performance: optimizePerformance
                })
            })
            .then(response => response.json())
            .then(data => {
                // 停止日志轮询
                stopLogPolling();

                if (data.success) {
                    updateProgress(100, '处理完成');
                    addLog('🎉 一键处理完成！', 'success');
                    addLog(`✅ 规则汇总：处理了 ${data.processed_files} 个Excel文件`, 'success');
                    addLog(`✅ 数据汇总：生成了 ${data.total_records} 条汇总记录`, 'success');
                    addLog(`✅ 违规统计：统计了 ${data.total_files} 个文件的违规数据`, 'success');
                    addLog(`📊 总违规数量: ${data.total_violations?.toLocaleString() || 0} 条`, 'info');
                    addLog(`💰 总违规金额: ${data.total_amount?.toLocaleString() || 0} 元`, 'info');
                    addLog(`⏱️ 处理耗时: ${data.processing_time} 秒`, 'info');
                    addLog(`📄 输出文件: ${data.output_file}`, 'success');

                    // 显示详细的文件处理结果（只显示前10个和失败的）
                    if (data.results && data.results.length > 0) {
                        addLog('📋 详细处理结果：', 'info');

                        // 显示成功的文件（最多10个）
                        const successResults = data.results.filter(r => r.status === 'success');
                        const failedResults = data.results.filter(r => r.status !== 'success');

                        successResults.slice(0, 10).forEach((result, index) => {
                            addLog(`✅ ${result.file_name}`, 'success');
                            addLog(`   📊 违规数量: ${result.violation_count?.toLocaleString() || 0}, 💰 违规金额: ${result.violation_amount?.toLocaleString() || 0} 元`, 'info');
                        });

                        if (successResults.length > 10) {
                            addLog(`   ... 还有 ${successResults.length - 10} 个文件处理成功`, 'info');
                        }

                        // 显示所有失败的文件
                        if (failedResults.length > 0) {
                            addLog(`⚠️ 处理失败的文件 (${failedResults.length} 个):`, 'warning');
                            failedResults.forEach((result) => {
                                addLog(`❌ ${result.file_name}: ${result.message}`, 'error');
                            });
                        }
                    }

                    setTimeout(() => {
                        hideProgress();
                        showResult(data);
                    }, 2000);
                } else {
                    updateProgress(100, '处理失败');
                    addLog(`❌ 处理失败: ${data.error}`, 'error');
                    setTimeout(() => {
                        hideProgress();
                        showToast('处理失败: ' + data.error, 'error');
                    }, 1000);
                }
            })
            .catch(error => {
                // 停止日志轮询
                stopLogPolling();
                updateProgress(100, '请求失败');
                addLog(`❌ 请求失败: ${error.message}`, 'error');
                setTimeout(() => {
                    hideProgress();
                    showToast('请求失败: ' + error.message, 'error');
                }, 1000);
            });
        }

        // 显示进度
        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('resultContainer').style.display = 'none';
            document.getElementById('logContainer').innerHTML = '';
        }

        // 隐藏进度
        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
        }

        // 更新进度
        function updateProgress(percent, title) {
            document.getElementById('progressTitle').textContent = title;
            document.getElementById('progressBar').style.width = percent + '%';
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();

            let iconClass = 'bi-info-circle';
            let textClass = '';

            switch(type) {
                case 'success':
                    iconClass = 'bi-check-circle';
                    textClass = 'text-success';
                    break;
                case 'warning':
                    iconClass = 'bi-exclamation-triangle';
                    textClass = 'text-warning';
                    break;
                case 'error':
                    iconClass = 'bi-x-circle';
                    textClass = 'text-danger';
                    break;
                case 'processing':
                    iconClass = 'bi-gear';
                    textClass = 'text-primary';
                    break;
            }

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${textClass}`;
            logEntry.innerHTML = `
                <span class="log-time">[${timestamp}]</span>
                <i class="bi ${iconClass} me-1"></i>
                <span class="log-message">${message}</span>
            `;

            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 添加进度日志
        function addProgressLog(current, total, fileName, status) {
            const percentage = Math.round((current / total) * 100);
            const message = `[${current}/${total}] ${status}: ${fileName} (${percentage}%)`;
            addLog(message, 'processing');
        }

        // 显示结果
        function showResult(data) {
            const endTime = Date.now();
            const processingTime = Math.round((endTime - startTime) / 1000);

            // 更新统计数据
            document.getElementById('totalFiles').textContent = data.total_files || 0;
            document.getElementById('totalViolations').textContent = (data.total_violations || 0).toLocaleString();
            document.getElementById('totalAmount').textContent = (data.total_amount || 0).toLocaleString();
            document.getElementById('processingTime').textContent = processingTime;

            // 更新结果表格
            const tbody = document.getElementById('resultTableBody');
            tbody.innerHTML = '';

            if (data.results && data.results.length > 0) {
                data.results.forEach((result, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${result.file_name}</td>
                        <td>
                            <span class="badge ${result.status === 'success' ? 'bg-success' : 'bg-danger'} status-badge">
                                ${result.status === 'success' ? '成功' : '失败'}
                            </span>
                        </td>
                        <td class="text-end">${(result.violation_count || 0).toLocaleString()}</td>
                        <td class="text-end">${(result.violation_amount || 0).toLocaleString()}</td>
                        <td>${result.message || ''}</td>
                    `;
                    tbody.appendChild(row);
                });
            }

            document.getElementById('resultContainer').style.display = 'block';
        }

        // 下载结果文件
        function downloadResult() {
            showToast('结果文件已保存到指定目录', 'success');
        }

        // 重新开始
        function resetProcess() {
            document.getElementById('resultContainer').style.display = 'none';
            showToast('已重置，可以重新开始处理', 'info');
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            // 创建toast元素
            const toastContainer = document.getElementById('toastContainer') || createToastContainer();

            const toastId = 'toast_' + Date.now();
            const bgClass = {
                'success': 'bg-success',
                'error': 'bg-danger',
                'warning': 'bg-warning',
                'info': 'bg-info'
            }[type] || 'bg-info';

            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement);
            toast.show();

            // 自动移除
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }

        // 创建toast容器
        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
            return container;
        }


    </script>
</body>
</html>
